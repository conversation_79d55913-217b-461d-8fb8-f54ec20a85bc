<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:padding="16dp">

    <!-- 平台选择区域 -->
    <com.google.android.material.card.MaterialCardView
        android:id="@+id/platform_card"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:cardCornerRadius="8dp"
        app:cardElevation="2dp"
        app:layout_constraintTop_toTopOf="parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="选择平台"
                android:textSize="16sp"
                android:textStyle="bold" />

            <com.google.android.material.chip.ChipGroup
                android:id="@+id/platform_chip_group"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp">

                <com.google.android.material.chip.Chip
                    android:id="@+id/meituan_chip"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:checked="true"
                    android:text="美团"
                    app:chipBackgroundColor="@color/chip_background_color"
                    app:chipIcon="@drawable/ic_meituan" />

                <com.google.android.material.chip.Chip
                    android:id="@+id/eleme_chip"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:checked="true"
                    android:text="饿了么"
                    app:chipBackgroundColor="@color/chip_background_color"
                    app:chipIcon="@drawable/ic_eleme" />

            </com.google.android.material.chip.ChipGroup>
        </LinearLayout>
    </com.google.android.material.card.MaterialCardView>

    <!-- 营业时间设置区域 -->
    <com.google.android.material.card.MaterialCardView
        android:id="@+id/time_settings_card"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        app:cardCornerRadius="8dp"
        app:cardElevation="2dp"
        app:layout_constraintTop_toBottomOf="@id/platform_card">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="营业时间设置"
                android:textSize="16sp"
                android:textStyle="bold" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="开始时间:" />

                <TextView
                    android:id="@+id/start_time_text"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="8dp"
                    android:background="?attr/selectableItemBackground"
                    android:clickable="true"
                    android:focusable="true"
                    android:padding="8dp"
                    android:text="08:30"
                    android:textColor="@color/black"
                    android:textSize="16sp" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="16dp"
                    android:text="结束时间:" />

                <TextView
                    android:id="@+id/end_time_text"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="8dp"
                    android:background="?attr/selectableItemBackground"
                    android:clickable="true"
                    android:focusable="true"
                    android:padding="8dp"
                    android:text="21:30"
                    android:textColor="@color/black"
                    android:textSize="16sp" />

            </LinearLayout>

        </LinearLayout>
    </com.google.android.material.card.MaterialCardView>

    <!-- 门店列表区域 -->
    <com.google.android.material.card.MaterialCardView
        android:id="@+id/shops_card"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginTop="16dp"
        android:layout_marginBottom="16dp"
        app:cardCornerRadius="8dp"
        app:cardElevation="2dp"
        app:layout_constraintBottom_toTopOf="@id/update_button"
        app:layout_constraintTop_toBottomOf="@id/time_settings_card">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical"
            android:padding="16dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="选择门店"
                    android:textSize="16sp"
                    android:textStyle="bold" />

                <com.google.android.material.checkbox.MaterialCheckBox
                    android:id="@+id/select_all_checkbox"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:checked="true"
                    android:text="全选" />

            </LinearLayout>

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/shops_recycler_view"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_marginTop="8dp"
                android:clipToPadding="false"
                android:paddingBottom="8dp"
                tools:listitem="@layout/item_shop_checkbox" />

        </LinearLayout>
    </com.google.android.material.card.MaterialCardView>

    <!-- 提交按钮 -->
    <com.google.android.material.button.MaterialButton
        android:id="@+id/update_button"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="12dp"
        android:text="批量更新营业时间"
        app:layout_constraintBottom_toBottomOf="parent" />

    <!-- 加载中指示器 -->
    <ProgressBar
        android:id="@+id/progress_bar"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout> 