package com.meituaneleme.assistant.model

import kotlinx.serialization.Serializable
import kotlinx.serialization.SerialName

@Serializable
data class ElePageQueryGetData(
    val code: Int = 0,
    val message: String? = null,
    @SerialName("data") val data: List<ItemData> = listOf(),
    val activityId: String = ""
) {
    @Serializable
    data class ItemData(
        val id: String? = null,
        val title: String = "",
        val price: Double = 0.0,
        val status: Int = 0,
        val quantity: Double = 0.0,
        val barCode: String = "",
        val itemId: Long = 0,
        val picUrl: String = "",
        @SerialName("monthlySaledQuantity") val monthlySaledQuantity: Int = 0,
        @SerialName("itemCanSell") val itemCanSell: Boolean = false,
        val spuId: String? = null,
        @SerialName("cateName2") val categoryName: String? = null,
        @SerialName("channelItemSalePackageVOList") val salePackages: List<SalePackage>? = null,
        val isWeight: Boolean = false,
        val itemCateRelationship: String? = null
    )

    @Serializable
    data class SalePackage(
        val id: String? = null,
        val name: String? = null,
        val price: Double? = null
        // 添加其他需要的字段
    )
} 