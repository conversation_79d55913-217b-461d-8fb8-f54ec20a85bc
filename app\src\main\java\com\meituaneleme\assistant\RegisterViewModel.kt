import android.content.Context
import android.util.Log
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.meituaneleme.assistant.api.ApiClient
import com.meituaneleme.assistant.api.User
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import android.provider.Settings

class RegisterViewModel : ViewModel() {
    private val _registerResult = MutableLiveData<Boolean>()
    val registerResult: LiveData<Boolean> = _registerResult
    
    private val _registeredUserInfo = MutableLiveData<String>()
    val registeredUserInfo: LiveData<String> = _registeredUserInfo

    private val api = ApiClient.api

    fun register(username: String, password: String, context: Context) {
        viewModelScope.launch(Dispatchers.IO) {
            try {
                // 获取设备ID
                val deviceId = Settings.Secure.getString(
                    context.contentResolver,
                    Settings.Secure.ANDROID_ID
                )

                // 构建查询条件
                val whereQuery = """{"deviceId":"$deviceId"}"""
                
                // 检查设备是否已注册
                val deviceCheckResponse = api.checkDeviceRegistration(whereQuery)
                if (deviceCheckResponse.isSuccessful && 
                    deviceCheckResponse.body()?.results?.isNotEmpty() == true) {
                    // 获取已注册的用户名No
                    val registeredUser = deviceCheckResponse.body()?.results?.firstOrNull()
                    withContext(Dispatchers.Main) {
                        _registerResult.value = false
                        _registeredUserInfo.value = "该设备已注册账号: ${registeredUser?.username} ,请使用: ${registeredUser?.username}直接登录，如有疑问请联系管理员微信：WMSHXAD"
                    }
                    return@launch
                }

                // 创建用户对象
                val user = User(
                    username = username,
                    password = password,
                    deviceId = deviceId
                )

                // 注册用户
                val response = api.registerUser(user)
                withContext(Dispatchers.Main) {
                    _registerResult.value = response.isSuccessful
                }

                if (!response.isSuccessful) {
                    Log.e("RegisterViewModel", "注册失败: ${response.code()}")
                }
            } catch (e: Exception) {
                Log.e("RegisterViewModel", "注册异常", e)
                withContext(Dispatchers.Main) {
                    _registerResult.value = false
                }
            }
        }
    }
}