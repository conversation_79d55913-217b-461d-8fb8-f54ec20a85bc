package com.meituaneleme.assistant

import android.annotation.SuppressLint
import android.content.Intent
import android.os.Bundle
import android.webkit.CookieManager
import android.webkit.WebSettings
import android.webkit.WebView
import android.webkit.WebViewClient
import android.widget.Button
import android.widget.EditText
import android.widget.Spinner
import android.widget.Toast
import androidx.appcompat.app.AppCompatActivity
import androidx.appcompat.app.AlertDialog
import com.meituaneleme.assistant.api.LeanCloudApi
import com.meituaneleme.assistant.api.StoreCookies
import com.meituaneleme.assistant.api.User
import com.meituaneleme.assistant.api.UserResponse
import com.meituaneleme.assistant.api.Store // 确保导入的是 api 包中的 Store
import retrofit2.Call
import retrofit2.Callback
import retrofit2.Response
import android.util.Log
import android.widget.ArrayAdapter
import android.widget.TextView
import androidx.localbroadcastmanager.content.LocalBroadcastManager
import com.meituaneleme.assistant.api.ApiClient
import com.meituaneleme.assistant.api.CookiesResponseWrapper
import com.meituaneleme.assistant.api.StoreResponse
import com.meituaneleme.assistant.api.StoreResponseWrapper
import com.meituaneleme.assistant.api.UpdateCookiesRequest
import org.json.JSONArray
import org.json.JSONObject
import android.content.Context
import com.meituaneleme.assistant.util.DeviceFingerprintManager
import com.meituaneleme.assistant.util.SessionManager

class WebViewActivity : AppCompatActivity() {
    private lateinit var webView: WebView
    private lateinit var bindStoreButton: Button
    private lateinit var apiService: LeanCloudApi
    private lateinit var storeName: String
    private var storeList: MutableList<StoreResponse> = mutableListOf()
    private var isUpdateMode: Boolean = false
    private lateinit var deviceFingerprintManager: DeviceFingerprintManager
    private lateinit var sessionManager: SessionManager



    @SuppressLint("MissingInflatedId")
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_web_view)

        val platform = intent.getStringExtra("platform")
        isUpdateMode = intent.getBooleanExtra("isUpdate", false)
        storeName = intent.getStringExtra("storeName") ?: ""

        if (platform == null) {
            Log.e("WebViewActivity", "Received platform is null")
        } else {
            Log.d("WebViewActivity", "Received platform: $platform, isUpdate: $isUpdateMode, storeName: $storeName")
        }

        bindStoreButton = findViewById(R.id.btn_bind_store)

        webView = findViewById(R.id.web_view)

        // 初始化管理器
        deviceFingerprintManager = DeviceFingerprintManager.getInstance(this)
        sessionManager = SessionManager.getInstance(this)

        webView.webViewClient = WebViewClient() // 使用默认的 WebViewClient

        // 使用设备指纹管理器配置WebView
        platform?.let {
            deviceFingerprintManager.configureWebView(webView, it)

            // 如果是更新模式，尝试恢复会话
            if (isUpdateMode && storeName.isNotEmpty()) {
                val sessionRestored = sessionManager.restoreWebViewSession(webView, it, storeName)
                if (sessionRestored) {
                    Log.d("WebViewActivity", "会话已恢复: $storeName ($it)")
                }
            }
        }

        // 根据模式设置按钮文本
        if (isUpdateMode) {
            bindStoreButton.text = "更新Cookies"
        } else {
            bindStoreButton.text = "绑定门店"
        }

        // 根据平台选择加载不同的 URL
        val url = when (platform) {
            "美团" -> "https://waimaie.meituan.com/new_fe/login_gw#/login"
            "饿了么" -> "https://nr.ele.me/eleme-nr-bfe-newretail/eb_login"
            else -> ""
        }

        if (url.isNotEmpty()) {
            webView.loadUrl(url)
        } else {
            Toast.makeText(this, "平台选择错误", Toast.LENGTH_SHORT).show()
        }

        // 设置按钮点击事件
        setupButtonClickListener()

        // 初始化 apiService
        apiService = ApiClient.api
    }

    private fun setupButtonClickListener() {
        bindStoreButton.setOnClickListener {
            showBindStoreDialog()
        }
    }

    private fun showBindStoreDialog() {
        val dialogView = layoutInflater.inflate(R.layout.dialog_bind_store, null)
        val platformSpinner: Spinner = dialogView.findViewById(R.id.platform_spinner)
        val storeNameEditText: EditText = dialogView.findViewById(R.id.store_name)
        val cookiesTextView: TextView = dialogView.findViewById(R.id.store_cookies)
        val confirmButton: Button = dialogView.findViewById(R.id.confirm_button)

        // 设置 Spinner 数据
        val platform = intent.getStringExtra("platform")
        val platforms = arrayOf(platform)
        val adapter = ArrayAdapter(this, android.R.layout.simple_spinner_item, platforms)
        adapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item)
        platformSpinner.adapter = adapter


        val cookies = getCookiesFromWebView() // 假设你有一个方法获取当前 WebView 的 cookies
        if (webView.url?.contains("login") == true) {
            cookiesTextView.text = "未登录，请先登录"
            //文字颜色设置为红色
            cookiesTextView.setTextColor(resources.getColor(R.color.red))
        } else {
            cookiesTextView.text = "已登录，点击确认绑定门店"
            //文字颜色设置为绿色
            cookiesTextView.setTextColor(resources.getColor(R.color.green))
        }


        // 根据模式设置对话框标题和门店名称
        val dialogTitle = if (isUpdateMode) "更新Cookies" else "绑定门店"
        if (isUpdateMode && storeName.isNotEmpty()) {
            storeNameEditText.setText(storeName)
            storeNameEditText.isEnabled = false // 更新模式下不允许修改门店名称
        }

        // 创建对话框
        val dialog = AlertDialog.Builder(this)
            .setTitle(dialogTitle)
            .setView(dialogView)
            .setCancelable(true)
            .create()

        confirmButton.setOnClickListener {
            val selectedPlatform = platformSpinner.selectedItem.toString()
            val storeName = storeNameEditText.text.toString()

            if (storeName.isNotEmpty()) {
                val cookies = getCookiesFromWebView()

                if (isUpdateMode) {
                    // 更新模式：直接更新本地Cookies
                    updateLocalStoreCookies(storeName, selectedPlatform, cookies)
                    Toast.makeText(this@WebViewActivity, "Cookies更新成功", Toast.LENGTH_SHORT).show()
                    dialog.dismiss()
                    finish()
                } else {
                    // 添加模式：原有逻辑
                    //在后端查询门店列表
                    //获取当前用户名称
                    val sharedPreferences = getSharedPreferences("UserPrefs", MODE_PRIVATE)
                    val username = sharedPreferences.getString("username", "") ?: ""

                    val whereClause = "{\"owner\":\"$username\"}"
                    ApiClient.api.getStores(whereClause).enqueue(object : Callback<StoreResponseWrapper> {
                        override fun onResponse(call: Call<StoreResponseWrapper>, response: Response<StoreResponseWrapper>) {
                            if (response.isSuccessful) {
                                storeList.clear()
                                storeList.addAll(response.body()?.results ?: emptyList())
                                //判断storeName是否已存在
                                if (storeList.any { store ->
                                        store.storeName.trim().equals(storeName.trim(), ignoreCase = true) &&
                                                store.platform.trim().equals(selectedPlatform.trim(), ignoreCase = true)
                                    }) {
                                    //执行更新cookies逻辑
                                    storeList.forEach { store ->
                                        if (store.storeName.trim().equals(storeName.trim(), ignoreCase = true) &&
                                            store.platform.trim().equals(selectedPlatform.trim(), ignoreCase = true)) {
                                            val storeId = store.objectId
                                            val platform = store.platform
                                            updateStoreCookies(storeId, cookies,platform)
                                        }
                                    }
                                }
                                else{
                                //把门店保存到后端
                                //获取用户名称
                                val sharedPreferences = getSharedPreferences("UserPrefs", MODE_PRIVATE)
                                val username = sharedPreferences.getString("username", "") ?: ""
                                val platform = selectedPlatform

                                val store = Store(storeName, username,platform) // 替换为实际的用户ID
                                apiService.createStore(store).enqueue(object : Callback<StoreResponse> {
                                    override fun onResponse(call: Call<StoreResponse>, response: Response<StoreResponse>) {
                                        if (response.isSuccessful) {
                                            var storeId = response.body()?.objectId ?: ""
                                            Toast.makeText(this@WebViewActivity, "门店添加成功", Toast.LENGTH_SHORT).show()
                                            // 保存 Cookies 到后端
                                            saveStoreCookies(username, storeId, cookies,platform)
                                        } else {
                                            Toast.makeText(this@WebViewActivity, "门店添加失败: ${response.message()}", Toast.LENGTH_SHORT).show()
                                        }
                                    }

                                    override fun onFailure(call: Call<StoreResponse>, t: Throwable) {
                                        Toast.makeText(this@WebViewActivity, "网络错误: ${t.message}", Toast.LENGTH_SHORT).show()
                                    }
                                })

                            }


                        }
                        else {
                            Toast.makeText(this@WebViewActivity, "获取门店失败: ${response.message()}", Toast.LENGTH_SHORT).show()
                        }
                    }

                    override fun onFailure(call: Call<StoreResponseWrapper>, t: Throwable) {
                        Toast.makeText(this@WebViewActivity, "网络错误: ${t.message}", Toast.LENGTH_SHORT).show()
                    }
                })
                }

                dialog.dismiss()
            } else {
                Toast.makeText(this, "请填写门店名称", Toast.LENGTH_SHORT).show()
            }
        }

        dialog.show()
    }

    private fun updateStoreCookies(storeId: String, newCookies: String,platform:String) {
        // 查询对应的 cookies 对象
        val whereClause = "{\"storeId\":\"$storeId\"}"
        ApiClient.api.getCookiesByStoreId(whereClause).enqueue(object : Callback<CookiesResponseWrapper> {
            override fun onResponse(call: Call<CookiesResponseWrapper>, response: Response<CookiesResponseWrapper>) {
                if (response.isSuccessful) {
                    val cookiesList = response.body()?.results
                    if (cookiesList != null && cookiesList.isNotEmpty()) {
                        // 获取第一个匹配的 cookies 对象
                        val cookiesObjectId = cookiesList[0].objectId
                        var cookies_change = ""
                        if (!newCookies.contains("terminal=bizCenter;") && platform.contains("美团")) {
                            cookies_change = "terminal=bizCenter;"+newCookies
                        }else{
                            cookies_change = newCookies
                        }


                        val request = UpdateCookiesRequest(cookies = cookies_change)

                        // 更新 cookies
                        ApiClient.api.updateStoreCookies(cookiesObjectId, request).enqueue(object : Callback<Void> {
                            override fun onResponse(call: Call<Void>, response: Response<Void>) {
                                if (response.isSuccessful) {
                                    Toast.makeText(this@WebViewActivity, "Cookies 更新成功", Toast.LENGTH_SHORT).show()
                                } else {
                                    Toast.makeText(this@WebViewActivity, "更新失败: ${response.message()}", Toast.LENGTH_SHORT).show()
                                }
                            }

                            override fun onFailure(call: Call<Void>, t: Throwable) {
                                Toast.makeText(this@WebViewActivity, "网络错误: ${t.message}", Toast.LENGTH_SHORT).show()
                            }
                        })
                    } else {
                        Toast.makeText(this@WebViewActivity, "未找到对应的 Cookies", Toast.LENGTH_SHORT).show()
                    }
                } else {
                    Toast.makeText(this@WebViewActivity, "查询失败: ${response.message()}", Toast.LENGTH_SHORT).show()
                }
            }

            override fun onFailure(call: Call<CookiesResponseWrapper>, t: Throwable) {
                Toast.makeText(this@WebViewActivity, "网络错误: ${t.message}", Toast.LENGTH_SHORT).show()
            }
        })
    }

    private fun getCookiesFromWebView(): String {
        val cookieManager = CookieManager.getInstance()
        return cookieManager.getCookie(webView.url) ?: ""
    }

    private fun saveStoreCookies(username: String, storeId: String, cookies: String,platform: String) {
        //判断cookies是否包含："terminal=bizCenter;"如果不存在就添加进去
        if (!cookies.contains("terminal=bizCenter;")) {
            var cookies_change = ""
            if (platform.contains("美团")){
                 cookies_change = "terminal=bizCenter;"+cookies
            }
            else{
                 cookies_change = cookies
            }

            val storeCookies = StoreCookies(username, storeId, cookies_change)
            apiService.saveStoreCookies(storeCookies).enqueue(object : Callback<Void> {
                override fun onResponse(call: Call<Void>, response: Response<Void>) {
                    if (response.isSuccessful) {
                        Toast.makeText(this@WebViewActivity, "Cookies 保存成功", Toast.LENGTH_SHORT).show()
                        // 更新 StoreManagementActivity 显示

                    } else {
                        Toast.makeText(this@WebViewActivity, "保存失败: ${response.message()}", Toast.LENGTH_SHORT).show()
                    }
                }

                override fun onFailure(call: Call<Void>, t: Throwable) {
                    Toast.makeText(this@WebViewActivity, "网络错误: ${t.message}", Toast.LENGTH_SHORT).show()
                }
            })
        }
        else{
            val storeCookies = StoreCookies(username, storeId, cookies)
            apiService.saveStoreCookies(storeCookies).enqueue(object : Callback<Void> {
                override fun onResponse(call: Call<Void>, response: Response<Void>) {
                    if (response.isSuccessful) {
                        Toast.makeText(this@WebViewActivity, "Cookies 保存成功", Toast.LENGTH_SHORT).show()
                        // 更新 StoreManagementActivity 显示

                    } else {
                        Toast.makeText(this@WebViewActivity, "保存失败: ${response.message()}", Toast.LENGTH_SHORT).show()
                    }
                }

                override fun onFailure(call: Call<Void>, t: Throwable) {
                    Toast.makeText(this@WebViewActivity, "网络错误: ${t.message}", Toast.LENGTH_SHORT).show()
                }
            })
        }



    }

    /**
     * 更新本地存储的门店Cookies
     */
    private fun updateLocalStoreCookies(storeName: String, platform: String, newCookies: String) {
        val sharedPreferences = getSharedPreferences("StoreCookies", Context.MODE_PRIVATE)
        val existingData = sharedPreferences.getString("storeCookiesList", "[]")
        val jsonArray = JSONArray(existingData)

        // 处理美团平台的terminal参数
        var processedCookies = newCookies
        if (platform == "美团" && !newCookies.contains("terminal=bizCenter;")) {
            processedCookies = "terminal=bizCenter;$newCookies"
        }

        // 查找并更新对应的门店Cookies
        for (i in 0 until jsonArray.length()) {
            val store = jsonArray.getJSONObject(i)
            if (store.getString("storeName") == storeName && store.getString("platform") == platform) {
                store.put("cookies", processedCookies)
                store.put("isValid", true) // 更新后标记为有效
                break
            }
        }

        // 保存更新后的数据
        val editor = sharedPreferences.edit()
        editor.putString("storeCookiesList", jsonArray.toString())
        editor.apply()

        // 保存会话信息以便下次快速登录
        sessionManager.saveSession(
            platform = platform,
            storeName = storeName,
            cookies = processedCookies,
            additionalData = mapOf(
                "lastUpdate" to System.currentTimeMillis().toString(),
                "deviceFingerprint" to deviceFingerprintManager.getPlatformFingerprint(platform)
            )
        )

        Log.d("WebViewActivity", "本地Cookies和会话已更新: $storeName ($platform)")
    }


}
