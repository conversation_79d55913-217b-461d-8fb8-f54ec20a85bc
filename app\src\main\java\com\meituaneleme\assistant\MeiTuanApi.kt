package com.meituaneleme.assistant

import android.icu.util.Calendar
import android.util.Log
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import okhttp3.MediaType.Companion.toMediaTypeOrNull
import okhttp3.OkHttpClient
import okhttp3.Request
import okhttp3.RequestBody
import java.io.IOException
import java.net.URLEncoder
import java.util.concurrent.TimeUnit
import org.json.JSONObject
import com.meituaneleme.assistant.util.DecryptUtils
import com.meituaneleme.assistant.config.AppConfig

class MeiTuanApi(private val cookieStr: String) {

    private var mtgsig: String = ""
    private val TAG = "MeiTuanApi"

    /**
     * 通用POST请求方法
     * @param url 请求的URL
     * @param data 请求体数据
     * @param headers 请求头
     * @return 响应字符串
     */
    suspend fun postRequest(url: String, data: String, headers: Map<String, String>): String = withContext(Dispatchers.IO) {
        Log.d(TAG, "发起POST请求: url=$url")
        val client = OkHttpClient.Builder()
            .connectTimeout(30, TimeUnit.SECONDS)
            .readTimeout(30, TimeUnit.SECONDS)
            .writeTimeout(30, TimeUnit.SECONDS)
            .build()
        
        val mediaType = "application/x-www-form-urlencoded".toMediaTypeOrNull()
        val requestBody = RequestBody.create(mediaType, data)
        
        val requestBuilder = Request.Builder()
            .url(url)
            .post(requestBody)
        
        // 添加请求头
        headers.forEach { (key, value) ->
            requestBuilder.addHeader(key, value)
        }
        
        val request = requestBuilder.build()
        
        try {
            val response = client.newCall(request).execute()
            val responseBody = response.body?.string() ?: ""
            Log.d(TAG, "POST请求响应: isSuccessful=${response.isSuccessful}, code=${response.code}")
            
            if (response.isSuccessful) {
                responseBody
            } else {
                Log.e(TAG, "POST请求失败: code=${response.code}, message=${response.message}")
                "请求失败: ${response.code} ${response.message}"
            }
        } catch (e: Exception) {
            Log.e(TAG, "POST请求异常", e)
            "请求异常: ${e.message}"
        }
    }

    // 从 cookieStr 中解析出 wmPoiId
    private fun parseWmPoiId(): Int? {
        val regex = "wmPoiId=(\\d+)".toRegex()
        val matchResult = regex.find(cookieStr)
        return matchResult?.groupValues?.get(1)?.toInt()
    }

    // postData 方法
    suspend fun postData(searchWord: String, pageNumber: Int = 1): String = withContext(Dispatchers.IO) {
        val wmPoiId = parseWmPoiId()
        if (wmPoiId == null) {
            return@withContext "无法从 cookieStr 中解析出 wmPoiId"
        }

        val params = mapOf(
            "wmPoiId" to wmPoiId.toString(),
            "pageNum" to pageNumber.toString(),
            "pageSize" to "100",
            "needTag" to "1",
            "name" to "",
            "brandId" to "0",
            "tagId" to "0",
            "searchWord" to searchWord,
            "state" to "0",
            "labelIds" to "",
            "saleStatus" to "0",
            "limitSale" to "0",
            "needCombinationSpu" to "2",
            "noStockAutoClear" to "-1",
            "medicareType" to "1"
        )

        val urlEncodedData = params.entries.joinToString("&") {
            "${URLEncoder.encode(it.key, "UTF-8")}=${URLEncoder.encode(it.value, "UTF-8")}"
        }

        val mtgsigData = JSONObject()
        mtgsigData.put("url", "https://shangoue.meituan.com/reuse/sc/product/retail/r/searchListPage?yodaReady=h5&csecplatform=4&csecversion=2.4.0")
        mtgsigData.put("data", JSONObject(params))

        val client = OkHttpClient.Builder()
            .connectTimeout(10, TimeUnit.SECONDS)
            .build()

        val requestBody = RequestBody.create("application/json".toMediaTypeOrNull(), mtgsigData.toString())

        val request = Request.Builder()
            .url("${AppConfig.API_BASE_URL}/calculate_signature")
            .post(requestBody)
            .build()

        try {
            val response = client.newCall(request).execute()
            if (response.isSuccessful) {
                mtgsig = response.body?.string() ?: ""
            } else {
                return@withContext "获取 mtgsig 失败"
            }
        } catch (e: IOException) {
            return@withContext "请求失败: ${e.message}"
        }

        val apiRequestBody = RequestBody.create("application/x-www-form-urlencoded".toMediaTypeOrNull(), urlEncodedData)

        val apiRequest = Request.Builder()
            .url("https://shangoue.meituan.com/reuse/sc/product/retail/r/searchListPage?yodaReady=h5&csecplatform=4&csecversion=2.4.0")
            .post(apiRequestBody)
            .addHeader("Accept", "application/json, text/plain, */*")
            .addHeader("Accept-Language", "zh-CN,zh;q=0.9")
            .addHeader("Connection", "keep-alive")
            .addHeader("Content-Type", "application/x-www-form-urlencoded")
            .addHeader("Cookie", cookieStr)
            .addHeader("M-APPKEY", "fe_waimai_sc_fe_product")
            .addHeader("M-TRACEID", "-1211098663782321178")
            .addHeader("Origin", "https://shangoue.meituan.com")
            .addHeader("Referer", "https://shangoue.meituan.com/reuse/sc/product/views/product/searchList?tagId=&brandId=&keyword=${URLEncoder.encode(searchWord, "UTF-8")}&wmPoiId=$wmPoiId&from=single&sourceId=0")
            .addHeader("Sec-Fetch-Dest", "empty")
            .addHeader("Sec-Fetch-Mode", "cors")
            .addHeader("Sec-Fetch-Site", "same-origin")
            .addHeader("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/122.0.6261.95 Safari/537.36")
            .addHeader("appType", "3")
            .addHeader("mtgsig", mtgsig)
            .addHeader("sec-ch-ua", "\"Chromium\";v=\"122\", \"Not(A:Brand\";v=\"24\", \"Google Chrome\";v=\"122\"")
            .addHeader("sec-ch-ua-mobile", "?0")
            .addHeader("sec-ch-ua-platform", "\"Windows\"")
            .build()

        return@withContext try {
            val response = client.newCall(apiRequest).execute()
            if (response.isSuccessful) {
                val responseBody = response.body?.string() ?: "无数据返回"
                DecryptUtils.processApiResponse(responseBody)
            } else {
                "请求失败，错误码: ${response.code}"
            }
        } catch (e: IOException) {
            "请求失败: ${e.message}"
        }
    }
    // 获取几天后的时间戳
    fun getTimestampDaysLater(days: Int): Long {
        val calendar = Calendar.getInstance()
        calendar.set(Calendar.HOUR_OF_DAY, 0)
        calendar.set(Calendar.MINUTE, 0)
        calendar.set(Calendar.SECOND, 0)
        calendar.set(Calendar.MILLISECOND, 0)
        return calendar.timeInMillis / 1000 + days * 24 * 60 * 60
    }

    // 在 MeiTuanApi 类中添加方法
    suspend fun getActPriceBySkuID(SkuId: String): String = withContext(Dispatchers.IO) {
        val wmPoiId = parseWmPoiId()
        if (wmPoiId == null) {
            return@withContext "无法从 cookieStr 中解析出 wmPoiId"
        }

        // 构建请求参数
        val params = mapOf(
            "actType" to "17",
            "alonePoi" to "true",
            "loginPoiId" to wmPoiId.toString(),
            "page" to mapOf("pageNo" to 1, "pageSize" to 10),
            "queryReq" to mapOf(
                "actStatus" to 101,
                "skuIdList" to listOf(SkuId),
                "source" to listOf(-1)
            )
        )

        // 构建 mtgsig 请求数据
        val mtgsigData = JSONObject()
        mtgsigData.put("url", "https://waimaieapp.meituan.com/sg/business/item/activity/query/pageList?yodaReady=h5&csecplatform=4&csecversion=3.0.0")
        mtgsigData.put("data", JSONObject(params))

        val client = OkHttpClient.Builder()
            .connectTimeout(10, TimeUnit.SECONDS)
            .build()

        // 获取 mtgsig
        val requestBody = RequestBody.create("application/json".toMediaTypeOrNull(), mtgsigData.toString())

        val mtgsigRequest = Request.Builder()
            .url("${AppConfig.API_BASE_URL}/calculate_signature")
            .post(requestBody)
            .build()

        try {
            val mtgsigResponse = client.newCall(mtgsigRequest).execute()
            if (mtgsigResponse.isSuccessful) {
                mtgsig = mtgsigResponse.body?.string() ?: ""
            } else {
                return@withContext "获取 mtgsig 失败"
            }
        } catch (e: IOException) {
            return@withContext "请求失败: ${e.message}"
        }

        // 构建用于获取价格信息的请求
        val apiRequestBody = RequestBody.create(
            "application/json".toMediaTypeOrNull(),
            JSONObject(params).toString()
        )

        val apiRequest = Request.Builder()
            .url("https://waimaieapp.meituan.com/sg/business/item/activity/query/pageList?yodaReady=h5&csecplatform=4&csecversion=3.0.0")
            .post(apiRequestBody)
            .addHeader("Accept", "application/json")
            .addHeader("Accept-Language", "zh-CN,zh;q=0.9")
            .addHeader("Connection", "keep-alive")
            .addHeader("Content-Type", "application/json")
            .addHeader("Cookie", cookieStr)
            .addHeader("Mtgsig", mtgsig)
            .addHeader("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/122.0.6261.95 Safari/537.36")
            .build()

        return@withContext try {
            val response = client.newCall(apiRequest).execute()
            if (response.isSuccessful) {
                val responseBody = response.body?.string() ?: "无数据返回"
                DecryptUtils.processApiResponse(responseBody)
            } else {
                "请求失败，错误码: ${response.code}"
            }
        } catch (e: IOException) {
            "请求失败: ${e.message}"
        }
    }

    suspend fun changeOrignPrice(skuId: String, orginPrice: Double): String = withContext(Dispatchers.IO) {
        val wmPoiId = parseWmPoiId()
        if (wmPoiId == null) {
            return@withContext "无法从 cookieStr 中解析出 wmPoiId"
        }

        val params = mapOf(
            "editSource" to 2,
            "skuId" to skuId,
            "wmPoiId" to wmPoiId,
            "price" to orginPrice
        )

        val mtgsigData = JSONObject().apply {
            put("url", "https://shangoue.meituan.com/reuse/sc/product/retail/w/updatePrice?yodaReady=h5&csecplatform=4&csecversion=2.4.0")
            put("data", JSONObject(params))
        }

        val client = OkHttpClient.Builder()
            .connectTimeout(10, TimeUnit.SECONDS)
            .build()

        try {
            val response = client.newCall(
                Request.Builder()
                    .url("${AppConfig.API_BASE_URL}/calculate_signature")
                    .post(RequestBody.create("application/json".toMediaTypeOrNull(), mtgsigData.toString()))
                    .build()
            ).execute()

            if (response.isSuccessful) {
                mtgsig = response.body?.string() ?: ""
            } else {
                return@withContext "获取 mtgsig 失败"
            }
        } catch (e: IOException) {
            return@withContext "请求失败: ${e.message}"
        }

        val urlEncodedData = params.entries.joinToString("&") {
            "${URLEncoder.encode(it.key, "UTF-8")}=${URLEncoder.encode(it.value.toString(), "UTF-8")}"
        }

        try {
            val response = client.newCall(
                Request.Builder()
                    .url("https://shangoue.meituan.com/reuse/sc/product/retail/w/updatePrice?yodaReady=h5&csecplatform=4&csecversion=2.4.0")
                    .post(RequestBody.create("application/x-www-form-urlencoded".toMediaTypeOrNull(), urlEncodedData))
                    .addHeader("Accept", "application/json, text/plain, */*")
                    .addHeader("Accept-Language", "zh-CN,zh;q=0.9")
                    .addHeader("Connection", "keep-alive")
                    .addHeader("Content-Type", "application/x-www-form-urlencoded")
                    .addHeader("Cookie", cookieStr)
                    .addHeader("M-APPKEY", "fe_waimai_sc_fe_product")
                    .addHeader("M-TRACEID", "-1211098663782321178")
                    .addHeader("Origin", "https://shangoue.meituan.com")
                    .addHeader("Referer", "https://shangoue.meituan.com/reuse/sc/product/views/product/searchList?tagId=&brandId=&keyword=%E6%B8%A9%E6%9F%94%E8%87%B3%E6%9E%81&wmPoiId=$wmPoiId&from=single&sourceId=0")
                    .addHeader("Sec-Fetch-Dest", "empty")
                    .addHeader("Sec-Fetch-Mode", "cors")
                    .addHeader("Sec-Fetch-Site", "same-origin")
                    .addHeader("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/122.0.6261.95 Safari/537.36")
                    .addHeader("appType", "3")
                    .addHeader("mtgsig", mtgsig)
                    .addHeader("sec-ch-ua", "\"Chromium\";v=\"122\", \"Not(A:Brand\";v=\"24\", \"Google Chrome\";v=\"122\"")
                    .addHeader("sec-ch-ua-mobile", "?0")
                    .addHeader("sec-ch-ua-platform", "\"Windows\"")
                    .build()
            ).execute()

            return@withContext DecryptUtils.processApiResponse(response.body?.string() ?: "无数据返回")
        } catch (e: IOException) {
            return@withContext "请求失败: ${e.message}"
        }
    }

    //修改折扣价
    suspend fun upActPrice(actPrice: Double, itemActId: String): String = withContext(Dispatchers.IO) {
        val wmPoiId = parseWmPoiId()
        if (wmPoiId == null) {
            return@withContext "无法从 cookieStr 中解析出 wmPoiId"
        }

        val params = mapOf(
            "actType" to 17,
            "modifyItemList" to listOf(mapOf("itemActId" to itemActId, "poiId" to wmPoiId)),
            "updateActInfo" to true,
            "startTime" to getTimestampDaysLater(0),
            "stopTime" to getTimestampDaysLater(360),
            "weeks" to "1,2,3,4,5,6,7",
            "time" to "00:00-23:59",
            "autoDelayDays" to 30,
            "discountType" to 0,
            "actPrice" to actPrice,
            "orderLimit" to false,
            "orderLimitCount" to -1,
            "dayInventory" to false,
            "dayInventoryCount" to -1
        )

        val mtgsigData = JSONObject().apply {
            put("url", "https://waimaieapp.meituan.com/sg/business/item/activity/batch/modify/saleFood?yodaReady=h5&csecplatform=4&csecversion=3.0.0")
            put("data", JSONObject(params))
        }

        val client = OkHttpClient.Builder()
            .connectTimeout(10, TimeUnit.SECONDS)
            .build()

        try {
            val response = client.newCall(
                Request.Builder()
                    .url("${AppConfig.API_BASE_URL}/calculate_signature")
                    .post(RequestBody.create("application/json".toMediaTypeOrNull(), mtgsigData.toString()))
                    .build()
            ).execute()

            if (response.isSuccessful) {
                mtgsig = response.body?.string() ?: ""
            } else {
                return@withContext "获取 mtgsig 失败"
            }
        } catch (e: IOException) {
            return@withContext "请求失败: ${e.message}"
        }

        try {
            val response = client.newCall(
                Request.Builder()
                    .url("https://waimaieapp.meituan.com/sg/business/item/activity/batch/modify/saleFood?yodaReady=h5&csecplatform=4&csecversion=3.0.0")
                    .post(RequestBody.create("application/json".toMediaTypeOrNull(), JSONObject(params).toString()))
                    .addHeader("Accept", "application/json")
                    .addHeader("Accept-Language", "zh-CN,zh;q=0.9")
                    .addHeader("Connection", "keep-alive")
                    .addHeader("Content-Type", "application/json")
                    .addHeader("Origin", "https://waimaieapp.meituan.com")
                    .addHeader("Referer", "https://waimaieapp.meituan.com/marketing/shangou/activity/pc/merchant/discount/list?wmPoiId=17234837&acctId=120209920")
                    .addHeader("Sec-Fetch-Dest", "empty")
                    .addHeader("Sec-Fetch-Mode", "cors")
                    .addHeader("Sec-Fetch-Site", "same-origin")
                    .addHeader("Mtgsig", mtgsig)
                    .addHeader("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/122.0.6261.95 Safari/537.36")
                    .addHeader("sec-ch-ua", "\"Chromium\";v=\"122\", \"Not(A:Brand\";v=\"24\", \"Google Chrome\";v=\"122\"")
                    .addHeader("sec-ch-ua-mobile", "?0")
                    .addHeader("sec-ch-ua-platform", "\"Windows\"")
                    .addHeader("Cookie", cookieStr)
                    .build()
            ).execute()

            return@withContext DecryptUtils.processApiResponse(response.body?.string() ?: "无数据返回")
        } catch (e: IOException) {
            return@withContext "请求失败: ${e.message}"
        }
    }

    //修改商品名称
    suspend fun updateSpuName(spuIds: Long, spuName: String): String = withContext(Dispatchers.IO)  {
        val wmPoiId = parseWmPoiId()
        if (wmPoiId == null) {
            return@withContext "无法从 cookieStr 中解析出 wmPoiId"
        }

        val params = mapOf(
            "editSource" to 2,
            "wmPoiId" to wmPoiId,
            "spuId" to  spuIds,
            "spuName" to  spuName,
            "checkActivitySkuModify" to  "true"
        )

        val mtgsigData = JSONObject().apply {
            put("url", "https://shangoue.meituan.com/reuse/sc/product/retail/w/updateSpuName?yodaReady=h5&csecplatform=4&csecversion=3.0.0")
            put("data", JSONObject(params))
        }

        val client = OkHttpClient.Builder()
            .connectTimeout(10, TimeUnit.SECONDS)
            .build()

        try {
            val response = client.newCall(
                Request.Builder()
                    .url("${AppConfig.API_BASE_URL}/calculate_signature")
                    .post(RequestBody.create("application/json".toMediaTypeOrNull(), mtgsigData.toString()))
                    .build()
            ).execute()

            if (response.isSuccessful) {
                mtgsig = response.body?.string() ?: ""
            } else {
                return@withContext "获取 mtgsig 失败"
            }
        } catch (e: IOException) {
            return@withContext "请求失败: ${e.message}"
        }




        val data = params.entries.joinToString("&") { (key, value) ->
            "${URLEncoder.encode(key, "UTF-8")}=${URLEncoder.encode(value.toString(), "UTF-8")}"
        }


        val requestBody = RequestBody.create("application/x-www-form-urlencoded".toMediaTypeOrNull(), data)
        val request = Request.Builder()
            .url("https://shangoue.meituan.com/reuse/sc/product/retail/w/updateSpuName?yodaReady=h5&csecplatform=4&csecversion=3.0.0")
            .post(requestBody)
            .addHeader("Accept", "application/json, text/plain, */*")
            .addHeader("Accept-Encoding", "gzip, deflate, br")
            .addHeader("Accept-Language", "zh-CN,zh;q=0.9")
            .addHeader("Connection", "keep-alive")
            .addHeader("Content-Type", "application/x-www-form-urlencoded")
            .addHeader("Cookie", cookieStr)
            .addHeader("mtgsig", mtgsig)
            .addHeader("Host", "shangoue.meituan.com")
            .addHeader("M-APPKEY", "fe_waimai_sc_fe_product")
            .addHeader("M-TRACEID", "-1406782098582037830")
            .addHeader("Origin", "https://shangoue.meituan.com")
            .addHeader("Referer", "https://shangoue.meituan.com/reuse/sc/product/views/product/searchList?tagId=&brandId=&keyword=%E6%B8%A9%E6%9F%94%E8%87%B3%E6%9E%81&wmPoiId=17234837&from=single&sourceId=0")
            .addHeader("Sec-Fetch-Dest", "empty")
            .addHeader("Sec-Fetch-Mode", "cors")
            .addHeader("Sec-Fetch-Site", "same-origin")
            .addHeader("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/122.0.6261.95 Safari/537.36")
            .addHeader("sec-ch-ua", "\"Chromium\";v=\"122\", \"Not(A:Brand\";v=\"24\", \"Google Chrome\";v=\"122\"")
            .addHeader("sec-ch-ua-mobile", "?0")
            .addHeader("sec-ch-ua-platform", "\"Windows\"")
            .build()

        return@withContext try {
            val response = client.newCall(request).execute()
            if (response.isSuccessful) {
                DecryptUtils.processApiResponse(response.body?.string() ?: "无数据返回")
            } else {
                "请求失败，错误码: ${response.code}"
            }
        } catch (e: IOException) {
            "请求失败: ${e.message}"
        }
    }

    suspend fun batchSetSellStatus(spuIds: Long, skuIds: String, sellStatus: Int): String = withContext(Dispatchers.IO) {
        val wmPoiId = parseWmPoiId()
        if (wmPoiId == null) {
            return@withContext "无法从 cookieStr 中解析出 wmPoiId"
        }

        val params = mapOf(
            "editSource" to 2,
            "tagCat" to 0,
            "spuIds" to spuIds,
            "skuIds" to skuIds,
            "opTab" to 0,
            "wmPoiId" to wmPoiId,
            "sellstatus" to sellStatus,
            "packageConfirmFlag" to false,
            "v2" to 1,
            "viewStyle" to 0
        )

        val data = params.entries.joinToString("&") { (key, value) ->
            "${URLEncoder.encode(key, "UTF-8")}=${URLEncoder.encode(value.toString(), "UTF-8")}"
        }

        val client = OkHttpClient.Builder()
            .connectTimeout(10, TimeUnit.SECONDS)
            .build()

        val requestBody = RequestBody.create("application/x-www-form-urlencoded".toMediaTypeOrNull(), data)
        val request = Request.Builder()
            .url("https://shangoue.meituan.com/reuse/sc/product/retail/w/batchSetSellStatus?yodaReady=h5&csecplatform=4&csecversion=2.4.0")
            .post(requestBody)
            .addHeader("Accept", "application/json, text/plain, */*")
            .addHeader("Accept-Encoding", "gzip, deflate, br")
            .addHeader("Accept-Language", "zh-CN,zh;q=0.9")
            .addHeader("Connection", "keep-alive")
            .addHeader("Content-Type", "application/x-www-form-urlencoded")
            .addHeader("Cookie", cookieStr)
            .addHeader("Host", "shangoue.meituan.com")
            .addHeader("M-APPKEY", "fe_waimai_sc_fe_product")
            .addHeader("M-TRACEID", "-1406782098582037830")
            .addHeader("Origin", "https://shangoue.meituan.com")
            .addHeader("Referer", "https://shangoue.meituan.com/reuse/sc/product/views/product/searchList?tagId=&brandId=&keyword=%E6%B8%A9%E6%9F%94%E8%87%B3%E6%9E%81&wmPoiId=17234837&from=single&sourceId=0")
            .addHeader("Sec-Fetch-Dest", "empty")
            .addHeader("Sec-Fetch-Mode", "cors")
            .addHeader("Sec-Fetch-Site", "same-origin")
            .addHeader("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/122.0.6261.95 Safari/537.36")
            .addHeader("sec-ch-ua", "\"Chromium\";v=\"122\", \"Not(A:Brand\";v=\"24\", \"Google Chrome\";v=\"122\"")
            .addHeader("sec-ch-ua-mobile", "?0")
            .addHeader("sec-ch-ua-platform", "\"Windows\"")
            .build()

        return@withContext try {
            val response = client.newCall(request).execute()
            if (response.isSuccessful) {
                DecryptUtils.processApiResponse(response.body?.string() ?: "无数据返回")
            } else {
                "请求失败，错误码: ${response.code}"
            }
        } catch (e: IOException) {
            "请求失败: ${e.message}"
        }
    }

    /**
     * 更新商品库存
     * @param skuId 商品规格ID或规格ID列表(用逗号分隔)
     * @param stock 新的库存数量
     * @return 请求结果
     */
    suspend fun updateStock(skuId: String, stock: Int): String = withContext(Dispatchers.IO) {
        val wmPoiId = parseWmPoiId()
        if (wmPoiId == null) {
            return@withContext """{"msg":"无法从 cookieStr 中解析出 wmPoiId","code":400,"data":""}"""
        }

        // 构建更新库存的请求参数
        val params = mapOf(
            "wmPoiId" to wmPoiId.toString(),
            "skuIds" to skuId,
            "stock" to stock.toString(),
            "editSource" to "1",
            "packageConfirmFlag" to "false"
        )

        val urlEncodedData = params.entries.joinToString("&") {
            "${URLEncoder.encode(it.key, "UTF-8")}=${URLEncoder.encode(it.value, "UTF-8")}"
        }

        // 构建 mtgsig 请求数据
        val mtgsigData = JSONObject()
        mtgsigData.put("url", "https://shangoue.meituan.com/reuse/sc/product/retail/w/batchUpdateSkuStock")
        mtgsigData.put("data", JSONObject(params))

        val client = OkHttpClient.Builder()
            .connectTimeout(10, TimeUnit.SECONDS)
            .build()

        // 获取 mtgsig
        val requestBody = RequestBody.create("application/json".toMediaTypeOrNull(), mtgsigData.toString())
        val request = Request.Builder()
            .url("${AppConfig.API_BASE_URL}/calculate_signature")
            .post(requestBody)
            .build()

        try {
            val response = client.newCall(request).execute()
            if (response.isSuccessful) {
                mtgsig = response.body?.string() ?: ""
            } else {
                return@withContext """{"msg":"获取 mtgsig 失败","code":400,"data":""}"""
            }
        } catch (e: IOException) {
            return@withContext """{"msg":"请求失败: ${e.message}","code":400,"data":""}"""
        }

        // 发送更新库存的请求
        val apiRequestBody = RequestBody.create("application/x-www-form-urlencoded".toMediaTypeOrNull(), urlEncodedData)
        val apiRequest = Request.Builder()
            .url("https://shangoue.meituan.com/reuse/sc/product/retail/w/batchUpdateSkuStock")
            .post(apiRequestBody)
            .addHeader("Accept", "application/json, text/plain, */*")
            .addHeader("Accept-Language", "zh-CN,zh;q=0.9")
            .addHeader("Connection", "keep-alive")
            .addHeader("Content-Type", "application/x-www-form-urlencoded")
            .addHeader("Cookie", cookieStr)
            .addHeader("M-APPKEY", "fe_waimai_sc_fe_product")
            .addHeader("Origin", "https://shangoue.meituan.com")
            .addHeader("Referer", "https://shangoue.meituan.com/reuse/sc/product/views/product/searchList")
            .addHeader("Sec-Fetch-Dest", "empty")
            .addHeader("Sec-Fetch-Mode", "cors")
            .addHeader("Sec-Fetch-Site", "same-origin")
            .addHeader("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/122.0.6261.95 Safari/537.36")
            .addHeader("mtgsig", mtgsig)
            .build()

        return@withContext try {
            val response = client.newCall(apiRequest).execute()
            if (response.isSuccessful) {
                val responseBody = response.body?.string() ?: """{"msg":"无数据返回","code":400,"data":""}"""
                DecryptUtils.processApiResponse(responseBody)
            } else {
                """{"msg":"请求失败，错误码: ${response.code}","code":400,"data":""}"""
            }
        } catch (e: IOException) {
            """{"msg":"请求失败: ${e.message}","code":400,"data":""}"""
        }
    }
}
