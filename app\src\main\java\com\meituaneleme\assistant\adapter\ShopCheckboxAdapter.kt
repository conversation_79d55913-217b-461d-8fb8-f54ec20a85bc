package com.meituaneleme.assistant.adapter

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.CheckBox
import android.widget.ImageView
import android.widget.TextView
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import com.meituaneleme.assistant.R
import com.meituaneleme.assistant.api.ShopModel

/**
 * 门店选择适配器
 */
class ShopCheckboxAdapter(
    private val onShopCheckedChanged: (ShopModel, Boolean) -> Unit
) : ListAdapter<ShopModel, ShopCheckboxAdapter.ViewHolder>(ShopDiffCallback()) {

    // 使用"平台:ID"作为唯一标识符，避免不同平台同名门店的冲突
    private val selectedShopKeys = mutableSetOf<String>()
    
    // 生成门店唯一键
    private fun getShopKey(shop: ShopModel): String = "${shop.platform}:${shop.id}"
    
    init {
        // 默认全选
        setAllSelected(true)
    }
    
    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        val view = LayoutInflater.from(parent.context)
            .inflate(R.layout.item_shop_checkbox, parent, false)
        return ViewHolder(view)
    }
    
    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        val shop = getItem(position)
        holder.bind(shop, selectedShopKeys.contains(getShopKey(shop)))
    }
    
    /**
     * 设置所有门店选中状态
     */
    fun setAllSelected(selected: Boolean) {
        if (selected) {
            currentList.forEach { selectedShopKeys.add(getShopKey(it)) }
        } else {
            selectedShopKeys.clear()
        }
        notifyDataSetChanged()
    }
    
    /**
     * 设置选中的门店
     */
    fun setSelectedShops(shops: List<ShopModel>) {
        selectedShopKeys.clear()
        shops.forEach { selectedShopKeys.add(getShopKey(it)) }
        notifyDataSetChanged()
    }
    
    /**
     * 获取选中的门店
     */
    fun getSelectedShops(): List<ShopModel> {
        return currentList.filter { selectedShopKeys.contains(getShopKey(it)) }
    }
    
    /**
     * 是否全部选中
     */
    fun isAllSelected(): Boolean {
        return currentList.size == selectedShopKeys.size
    }
    
    inner class ViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        private val platformIcon: ImageView = itemView.findViewById(R.id.platform_icon)
        private val shopNameText: TextView = itemView.findViewById(R.id.shop_name_text)
        private val shopCheckbox: CheckBox = itemView.findViewById(R.id.shop_checkbox)
        
        fun bind(shop: ShopModel, isSelected: Boolean) {
            // 添加平台前缀，更清晰地区分不同平台的同名门店
            shopNameText.text = "${shop.name} [${shop.platform}]"
            
            // 设置平台图标并添加颜色
            val iconRes = when (shop.platform) {
                "美团" -> R.drawable.ic_meituan
                "饿了么" -> R.drawable.ic_eleme
                else -> R.drawable.ic_store
            }
            platformIcon.setImageResource(iconRes)
            
            // 设置选中状态
            shopCheckbox.isChecked = isSelected
            
            // 设置点击事件
            shopCheckbox.setOnClickListener {
                val checked = shopCheckbox.isChecked
                val shopKey = getShopKey(shop)
                if (checked) {
                    selectedShopKeys.add(shopKey)
                } else {
                    selectedShopKeys.remove(shopKey)
                }
                onShopCheckedChanged(shop, checked)
            }
            
            // 设置整个条目的点击事件
            itemView.setOnClickListener {
                shopCheckbox.performClick()
            }
        }
    }
    
    /**
     * DiffUtil回调
     */
    class ShopDiffCallback : DiffUtil.ItemCallback<ShopModel>() {
        override fun areItemsTheSame(oldItem: ShopModel, newItem: ShopModel): Boolean {
            // 使用平台和ID组合作为唯一标识
            return oldItem.platform == newItem.platform && oldItem.id == newItem.id
        }
        
        override fun areContentsTheSame(oldItem: ShopModel, newItem: ShopModel): Boolean {
            return oldItem == newItem
        }
    }
} 