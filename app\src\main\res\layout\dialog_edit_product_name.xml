<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:padding="16dp">

    <EditText
        android:id="@+id/et_product_name"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:hint="编辑商品名称"
        android:inputType="textMultiLine"
        android:maxLines="5"
        android:scrollHorizontally="false"
        android:padding="12dp" />
        
    <TextView
        android:id="@+id/tv_char_count"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="0/45"
        android:textColor="@android:color/darker_gray"
        android:gravity="end"
        android:paddingTop="4dp"
        android:paddingEnd="8dp" />

    <TextView
        android:id="@+id/tv_edit_result"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="修改结果:\n"
        android:textColor="@android:color/black"
        android:paddingTop="8dp" />

    <Button
        android:id="@+id/btn_save"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="保存"
        android:layout_gravity="end"
        android:layout_marginTop="16dp" />
</LinearLayout> 