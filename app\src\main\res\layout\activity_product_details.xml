<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".ProductDetailsActivity">

    <TextView
        android:id="@+id/tv_product_name"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:text="商品名称"
        android:textSize="24sp"
        android:textStyle="bold"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:layout_margin="16dp" />

    <TextView
        android:id="@+id/tv_store_name"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:text="门店名称"
        android:textSize="18sp"
        app:layout_constraintTop_toBottomOf="@id/tv_product_name"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:layout_margin="8dp" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv_product_specs"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintTop_toBottomOf="@id/tv_store_name"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        android:layout_margin="16dp" />

</androidx.constraintlayout.widget.ConstraintLayout>