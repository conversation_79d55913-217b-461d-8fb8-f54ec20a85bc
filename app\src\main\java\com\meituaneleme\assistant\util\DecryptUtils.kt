package com.meituaneleme.assistant.util

import android.util.Base64
import android.util.Log
import org.json.JSONException
import org.json.JSONObject
import java.nio.charset.StandardCharsets
import javax.crypto.Cipher
import javax.crypto.spec.SecretKeySpec

/**
 * 解密美团API返回的加密数据的工具类
 */
object DecryptUtils {
    
    private const val TAG = "DecryptUtils"
    
    /**
     * 解密美团API返回的加密数据
     * @param encryptedData 加密的数据字符串
     * @return 解密后的JSON对象，如果解密失败则返回空对象
     */
    fun decryptMeituanResponse(encryptedData: String?): JSONObject {
        if (encryptedData.isNullOrEmpty()) {
            return JSONObject()
        }
        
        // 移除空白字符
        val cleanData = encryptedData.replace("\\s".toRegex(), "")
        
        // 密钥: sgproduct + decrypt
        val key = "sgproductdecrypt"
        val keyBytes = key.toByteArray(StandardCharsets.UTF_8)
        
        return try {
            // 创建AES-ECB解密器
            val cipher = Cipher.getInstance("AES/ECB/PKCS5Padding")
            val secretKey = SecretKeySpec(keyBytes, "AES")
            cipher.init(Cipher.DECRYPT_MODE, secretKey)
            
            // 解密数据
            val encryptedBytes = try {
                // 尝试base64解码
                Base64.decode(cleanData, Base64.DEFAULT)
            } catch (e: Exception) {
                // 如果不是base64格式，直接使用字节编码
                cleanData.toByteArray(StandardCharsets.UTF_8)
            }
            
            // 解密并转为字符串
            val decryptedBytes = cipher.doFinal(encryptedBytes)
            val decryptedText = String(decryptedBytes, StandardCharsets.UTF_8)
            
            // 解析JSON
            JSONObject(decryptedText)
        } catch (e: Exception) {
            Log.e(TAG, "解密失败: ${e.message}", e)
            JSONObject()
        }
    }
    
    /**
     * 处理API响应，检查是否需要解密
     * @param responseData API返回的原始响应字符串
     * @return 处理后的JSON对象字符串
     */
    fun processApiResponse(responseData: String): String {
        return try {
            val jsonObject = JSONObject(responseData)
            
            // 检查是否需要解密
            if (jsonObject.optBoolean("encrypted", false)) {
                Log.d(TAG, "检测到加密响应，开始解密")
                // 获取加密的数据字段
                val encryptedData = jsonObject.optString("data", "")
                
                // 解密数据
                val decryptedData = decryptMeituanResponse(encryptedData)
                
                // 替换原始响应中的data字段
                jsonObject.put("data", decryptedData)
                jsonObject.put("encrypted", false)
                
                Log.d(TAG, "解密成功，返回解密后的数据")
            }
            
            jsonObject.toString()
        } catch (e: JSONException) {
            // 如果不是JSON格式，直接返回原始数据
            Log.e(TAG, "处理响应失败: ${e.message}", e)
            responseData
        }
    }
    
    /**
     * 测试解密功能
     * @param encryptedData 加密的测试数据
     * @return 解密结果，成功返回解密后的数据，失败返回错误信息
     */
    fun testDecryption(encryptedData: String): String {
        return try {
            val jsonObject = JSONObject()
            jsonObject.put("encrypted", true)
            jsonObject.put("data", encryptedData)
            
            val result = processApiResponse(jsonObject.toString())
            val resultJson = JSONObject(result)
            
            if (!resultJson.optBoolean("encrypted", true)) {
                "解密成功: " + resultJson.toString(2)
            } else {
                "解密失败，无法处理数据"
            }
        } catch (e: Exception) {
            "解密过程出错: ${e.message}"
        }
    }
} 