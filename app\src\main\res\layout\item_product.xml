<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginHorizontal="8dp"
    android:layout_marginVertical="4dp"
    app:cardCornerRadius="8dp"
    app:cardElevation="0.5dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="10dp">

        <!-- 商品标题和图片 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginBottom="8dp"
            android:minHeight="90dp">

            <!-- 商品图片 -->
            <com.google.android.material.imageview.ShapeableImageView
                android:id="@+id/iv_product_image"
                android:layout_width="80dp"
                android:layout_height="80dp"
                android:scaleType="centerCrop"
                app:shapeAppearanceOverlay="@style/RoundedImageView"
                android:src="@drawable/product_image"
                app:strokeColor="@color/light_gray"
                app:strokeWidth="0.5dp"/>

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:layout_marginStart="8dp"
                android:orientation="vertical"
                android:paddingTop="2dp"
                android:paddingBottom="2dp">
                
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:minHeight="60dp">

                <!-- 商品信息 -->
                <TextView
                    android:id="@+id/tv_product_title"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                    android:ellipsize="end"
                    android:maxLines="3"
                    android:text="商品标题"
                        android:textColor="@color/text_primary"
                        android:textSize="14sp"
                        android:textStyle="bold"
                        android:lineSpacingExtra="1dp"
                        android:layout_marginBottom="2dp" />

                    <!-- 编辑按钮 -->
                    <com.google.android.material.button.MaterialButton
                        android:id="@+id/iv_edit_product"
                        android:layout_width="36dp"
                        android:layout_height="36dp"
                        style="@style/Widget.MaterialComponents.Button.OutlinedButton.Icon"
                        app:icon="@drawable/ic_edit_modern"
                        app:iconTint="@color/colorPrimary"
                        app:iconGravity="textStart"
                        app:iconPadding="0dp"
                        android:insetLeft="0dp"
                        android:insetTop="0dp"
                        android:insetRight="0dp"
                        android:insetBottom="0dp"
                        app:strokeWidth="0dp"
                        android:padding="6dp"
                        android:contentDescription="@string/edit_product" />
                </LinearLayout>

                <!-- 遮罩 -->
                <FrameLayout
                    android:id="@+id/overlay_view"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:background="@drawable/rounded_overlay"
                    android:visibility="gone">

                    <TextView
                        android:id="@+id/tv_overlay_text"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:text="操作中..."
                        android:textColor="@android:color/white"
                        android:textSize="16sp" />
                </FrameLayout>
            </LinearLayout>
        </LinearLayout>

        <!-- 批量操作标题栏和折叠按钮 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:layout_marginBottom="4dp">

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="批量操作"
                android:textColor="@color/text_primary"
                android:textSize="14sp"
                android:textStyle="bold"/>

            <com.google.android.material.button.MaterialButton
                android:id="@+id/btn_toggle_batch_operations"
                android:layout_width="36dp"
                android:layout_height="36dp"
                style="@style/Widget.MaterialComponents.Button.OutlinedButton.Icon"
                app:icon="@drawable/ic_expand"
                app:iconTint="@color/colorPrimary"
                app:iconGravity="textStart"
                app:iconPadding="0dp"
                android:insetLeft="0dp"
                android:insetTop="0dp"
                android:insetRight="0dp"
                android:insetBottom="0dp"
                app:strokeWidth="0dp"
                android:padding="6dp"
                android:contentDescription="展开/折叠批量操作区域"/>
        </LinearLayout>

        <!-- 价格设置区域 -->
        <androidx.cardview.widget.CardView
            android:id="@+id/card_price_settings"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="8dp"
            app:cardCornerRadius="6dp"
            app:cardElevation="0dp"
            android:visibility="gone"
            app:cardBackgroundColor="@color/gray">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="8dp">

                <!-- 原价设置 -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center_vertical"
                    android:layout_marginBottom="4dp">

                    <TextView
                        android:layout_width="40dp"
                        android:layout_height="wrap_content"
                        android:text="原价"
                        android:textColor="@color/text_secondary"
                        android:textSize="13sp"/>

                    <com.google.android.material.textfield.TextInputLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        style="@style/Widget.App.TextInputLayout"
                        android:hint="输入原价"
                        app:hintTextColor="@color/colorPrimary">

                        <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/et_original_price_allstore"
                            android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:inputType="numberDecimal"
                            android:minHeight="38dp"
                            android:paddingVertical="6dp"/>
                    </com.google.android.material.textfield.TextInputLayout>

                    <com.google.android.material.button.MaterialButton
                android:id="@+id/btn_save_original_price_allstore"
                android:layout_width="wrap_content"
                        android:layout_height="36dp"
                        android:text="保存"
                        android:layout_marginStart="4dp"
                        app:cornerRadius="4dp"
                        android:textSize="12sp"
                        android:insetTop="0dp"
                        android:insetBottom="0dp"
                        style="@style/Widget.App.Button"/>
        </LinearLayout>

                <!-- 折扣价设置 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:layout_marginBottom="4dp">

            <TextView
                android:layout_width="40dp"
                        android:layout_height="wrap_content"
                        android:text="折扣价"
                        android:textColor="@color/text_secondary"
                android:textSize="13sp"/>

                    <com.google.android.material.textfield.TextInputLayout
                        android:layout_width="0dp"
                android:layout_height="wrap_content"
                        android:layout_weight="1"
                        style="@style/Widget.App.TextInputLayout"
                        android:hint="输入折扣价"
                        app:hintTextColor="@color/colorPrimary">

                        <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/et_discount_price_allstore"
                            android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:inputType="numberDecimal"
                    android:minHeight="38dp"
                    android:paddingVertical="6dp"/>
                    </com.google.android.material.textfield.TextInputLayout>

                    <com.google.android.material.button.MaterialButton
                android:id="@+id/btn_save_discount_price_allstore"
                android:layout_width="wrap_content"
                android:layout_height="36dp"
                        android:text="保存"
                android:layout_marginStart="4dp"
                        app:cornerRadius="4dp"
                android:textSize="12sp"
                android:insetTop="0dp"
                android:insetBottom="0dp"
                style="@style/Widget.App.Button"/>
        </LinearLayout>
        
        <!-- 库存设置 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical">

            <TextView
                android:layout_width="40dp"
                android:layout_height="wrap_content"
                android:text="库存"
                android:textColor="@color/text_secondary"
                android:textSize="13sp"/>

            <com.google.android.material.textfield.TextInputLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                style="@style/Widget.App.TextInputLayout"
                android:hint="输入库存"
                app:hintTextColor="@color/colorPrimary">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/et_stock_allstore"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:inputType="number"
                    android:minHeight="38dp"
                    android:paddingVertical="6dp"/>
            </com.google.android.material.textfield.TextInputLayout>

            <com.google.android.material.button.MaterialButton
                android:id="@+id/btn_save_stock_allstore"
                android:layout_width="wrap_content"
                android:layout_height="36dp"
                android:text="保存"
                android:layout_marginStart="4dp"
                app:cornerRadius="4dp"
                android:textSize="12sp"
                android:insetTop="0dp"
                android:insetBottom="0dp"
                        style="@style/Widget.App.Button"/>
                </LinearLayout>
        </LinearLayout>
        </androidx.cardview.widget.CardView>
        
        <!-- 一键上下架按钮 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginBottom="8dp">

            <com.google.android.material.button.MaterialButton
                android:id="@+id/btn_batch_on_shelf"
                android:layout_width="0dp"
                android:layout_height="40dp"
                android:layout_weight="1"
                android:text="一键上架"
                android:textSize="13sp"
                android:layout_marginEnd="4dp"
                app:cornerRadius="6dp"
                style="@style/Widget.App.Button"/>

            <com.google.android.material.button.MaterialButton
                android:id="@+id/btn_batch_off_shelf"
                android:layout_width="0dp"
                android:layout_height="40dp"
                android:layout_weight="1"
                android:text="一键下架"
                android:textSize="13sp"
                android:layout_marginStart="4dp"
                app:cornerRadius="6dp"
                style="@style/Widget.App.Button.Secondary"/>
        </LinearLayout>

        <!-- 商品信息标题 -->
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="店铺列表"
            android:textSize="14sp"
            android:textStyle="bold"
            android:textColor="@color/text_primary"
            android:layout_marginBottom="8dp"/>

        <!-- 嵌套RecyclerView -->
        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rv_product_info_list"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:nestedScrollingEnabled="false" />

    </LinearLayout>
</androidx.cardview.widget.CardView>