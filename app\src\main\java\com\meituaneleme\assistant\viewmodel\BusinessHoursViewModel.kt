package com.meituaneleme.assistant.viewmodel

import android.util.Log
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.meituaneleme.assistant.api.ApiResult
import com.meituaneleme.assistant.api.BusinessHoursRepository
import com.meituaneleme.assistant.api.ShopModel
import com.meituaneleme.assistant.model.BatchUpdateBusinessHoursRequest
import com.meituaneleme.assistant.model.BatchUpdateResult
import com.meituaneleme.assistant.model.BusinessHourModel
import com.meituaneleme.assistant.model.PlatformAccountModel
import com.meituaneleme.assistant.security.AccountStorageManager
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch

/**
 * 营业时间UI状态
 */
data class BusinessHoursState(
    val isLoading: Boolean = false,
    val allShops: List<ShopModel> = emptyList(),
    val selectedShops: List<ShopModel> = emptyList(),
    val selectedPlatforms: List<String> = listOf("美团", "饿了么"),
    val businessHours: BusinessHourModel? = null,
    val updateResult: BatchUpdateResult? = null,
    val needAccountSetup: Boolean = false,
    val missingAccounts: List<ShopAccountInfo> = emptyList(),
    val error: String? = null
)

/**
 * 店铺账号信息
 */
data class ShopAccountInfo(
    val platform: String,
    val shopId: String,
    val shopName: String
)

/**
 * UI意图
 */
sealed class BusinessHoursIntent {
    data class SelectShop(val shop: ShopModel, val selected: Boolean) : BusinessHoursIntent()
    data class SelectAllShops(val selected: Boolean) : BusinessHoursIntent()
    data class SelectPlatform(val platform: String, val selected: Boolean) : BusinessHoursIntent()
    data class SetBusinessHours(val businessHours: BusinessHourModel) : BusinessHoursIntent()
    data class SaveAccount(val account: PlatformAccountModel) : BusinessHoursIntent()
    data class UpdateAccount(val platform: String, val shopId: String, val username: String, val password: String) : BusinessHoursIntent()
    object UpdateBusinessHours : BusinessHoursIntent()
    object LoadShops : BusinessHoursIntent()
    object DismissError : BusinessHoursIntent()
    object ClearMissingAccounts : BusinessHoursIntent()
    object ClearUpdateResult : BusinessHoursIntent()
}

/**
 * 营业时间ViewModel
 */
class BusinessHoursViewModel(
    private val repository: BusinessHoursRepository,
    private val accountManager: AccountStorageManager
) : ViewModel() {

    private val TAG = "BusinessHoursVM"
    private val _state = MutableStateFlow(BusinessHoursState())
    val state: StateFlow<BusinessHoursState> = _state.asStateFlow()
    
    init {
        // 默认选中所有门店
        loadAllShops()
    }
    
    fun handleIntent(intent: BusinessHoursIntent) {
        when (intent) {
            is BusinessHoursIntent.SelectShop -> selectShop(intent.shop, intent.selected)
            is BusinessHoursIntent.SelectAllShops -> selectAllShops(intent.selected)
            is BusinessHoursIntent.SelectPlatform -> selectPlatform(intent.platform, intent.selected)
            is BusinessHoursIntent.SetBusinessHours -> setBusinessHours(intent.businessHours)
            is BusinessHoursIntent.SaveAccount -> saveAccount(intent.account)
            is BusinessHoursIntent.UpdateAccount -> updateAccount(intent.platform, intent.shopId, intent.username, intent.password)
            is BusinessHoursIntent.UpdateBusinessHours -> updateBusinessHours()
            is BusinessHoursIntent.LoadShops -> loadAllShops()
            is BusinessHoursIntent.DismissError -> dismissError()
            is BusinessHoursIntent.ClearMissingAccounts -> clearMissingAccounts()
            is BusinessHoursIntent.ClearUpdateResult -> clearUpdateResult()
        }
    }
    
    private fun loadAllShops() {
        viewModelScope.launch {
            Log.d(TAG, "开始加载所有门店")
            _state.update { it.copy(isLoading = true) }
            
            when (val result = repository.getShopList()) {
                is ApiResult.Success -> {
                    Log.d(TAG, "成功加载门店: ${result.data.size}个")
                    _state.update { 
                        it.copy(
                            isLoading = false,
                            allShops = result.data,
                            selectedShops = result.data
                        )
                    }
                }
                is ApiResult.Error -> {
                    Log.e(TAG, "加载门店失败: ${result.error}")
                    _state.update { 
                        it.copy(
                            isLoading = false,
                            error = result.error
                        )
                    }
                }
                is ApiResult.AccountError -> {
                    Log.e(TAG, "获取门店列表时账号认证错误: 平台=${result.platform}, 门店=${result.shopName}")
                    _state.update {
                        it.copy(
                            isLoading = false,
                            error = "账号认证错误: ${result.platform} ${result.shopName}"
                        )
                    }
                }
            }
        }
    }
    
    private fun selectShop(shop: ShopModel, selected: Boolean) {
        _state.update { currentState ->
            val updatedSelectedShops = if (selected) {
                currentState.selectedShops + shop
            } else {
                currentState.selectedShops - shop
            }
            
            // 重要：选择门店时只更新选中状态，不触发账号验证
            // 账号验证只在用户点击更新按钮时才执行
            currentState.copy(
                selectedShops = updatedSelectedShops,
                // 清除可能存在的账号设置需求，避免选择门店时弹出账号设置对话框
                needAccountSetup = false,
                missingAccounts = emptyList()
            )
        }
    }
    
    private fun selectAllShops(selected: Boolean) {
        _state.update { currentState ->
            val filteredShops = if (selected) {
                currentState.allShops.filter { shop ->
                    currentState.selectedPlatforms.contains(shop.platform)
                }
            } else {
                emptyList()
            }
            
            // 重要：全选或取消全选门店时只更新选中状态，不触发账号验证
            // 账号验证只在用户点击更新按钮时才执行
            currentState.copy(
                selectedShops = filteredShops,
                // 清除可能存在的账号设置需求，避免选择门店时弹出账号设置对话框
                needAccountSetup = false,
                missingAccounts = emptyList()
            )
        }
    }
    
    private fun selectPlatform(platform: String, selected: Boolean) {
        _state.update { currentState ->
            val updatedPlatforms = if (selected) {
                currentState.selectedPlatforms + platform
            } else {
                currentState.selectedPlatforms - platform
            }
            
            // 更新选中的门店，只保留选中平台的门店
            val updatedShops = currentState.selectedShops.filter { shop ->
                updatedPlatforms.contains(shop.platform)
            }
            
            // 重要：选择平台时只更新选中状态，不触发账号验证
            // 账号验证只在用户点击更新按钮时才执行
            currentState.copy(
                selectedPlatforms = updatedPlatforms,
                selectedShops = updatedShops,
                // 清除可能存在的账号设置需求，避免选择平台时弹出账号设置对话框
                needAccountSetup = false,
                missingAccounts = emptyList()
            )
        }
    }
    
    private fun setBusinessHours(businessHours: BusinessHourModel) {
        _state.update { it.copy(businessHours = businessHours) }
    }
    
    private fun saveAccount(account: PlatformAccountModel) {
        viewModelScope.launch {
            try {
                Log.d(TAG, "保存账号: 平台=${account.platform}, 门店ID=${account.shopId}")
                accountManager.saveAccount(account)
                
                // 移除已保存的账号从缺失列表中
                _state.update { currentState ->
                    val updatedMissingAccounts = currentState.missingAccounts.filterNot { 
                        it.platform == account.platform && it.shopId == account.shopId 
                    }
                    
                    currentState.copy(
                        missingAccounts = updatedMissingAccounts,
                        needAccountSetup = updatedMissingAccounts.isNotEmpty()
                    )
                }
                
                // 如果所有缺失账号都已保存，自动重试更新
                if (_state.value.missingAccounts.isEmpty() && _state.value.businessHours != null) {
                    Log.d(TAG, "所有账号已设置，自动重试更新")
                    updateBusinessHours()
                }
                
            } catch (e: Exception) {
                Log.e(TAG, "保存账号失败", e)
                _state.update { it.copy(error = "保存账号失败: ${e.message}") }
            }
        }
    }
    
    private fun updateAccount(platform: String, shopId: String, username: String, password: String) {
        viewModelScope.launch {
            try {
                Log.d(TAG, "更新账号: 平台=$platform, 门店ID=$shopId")
                _state.update { it.copy(isLoading = true) }
                
                val success = repository.updateAccount(platform, shopId, username, password)
                
                if (success) {
                    Log.d(TAG, "账号更新成功")
                    // 移除已更新的账号从缺失列表中
                    _state.update { currentState ->
                        val updatedMissingAccounts = currentState.missingAccounts.filterNot { 
                            it.platform == platform && it.shopId == shopId 
                        }
                        
                        currentState.copy(
                            isLoading = false,
                            missingAccounts = updatedMissingAccounts,
                            needAccountSetup = updatedMissingAccounts.isNotEmpty(),
                            error = null
                        )
                    }
                    
                    // 如果所有缺失账号都已保存，自动重试更新
                    if (_state.value.missingAccounts.isEmpty() && _state.value.businessHours != null) {
                        Log.d(TAG, "所有账号已更新，自动重试更新")
                        updateBusinessHours()
                    }
                } else {
                    Log.e(TAG, "账号更新失败")
                    _state.update { it.copy(
                        isLoading = false,
                        error = "账号更新失败"
                    )}
                }
            } catch (e: Exception) {
                Log.e(TAG, "更新账号失败", e)
                _state.update { it.copy(
                    isLoading = false,
                    error = "更新账号失败: ${e.message}"
                )}
            }
        }
    }
    
    private fun updateBusinessHours() {
        val currentState = _state.value
        val businessHours = currentState.businessHours
        
        if (businessHours == null) {
            Log.e(TAG, "更新失败: 未设置营业时间")
            _state.update { it.copy(error = "请先设置营业时间") }
            return
        }
        
        // 检查是否有选中的门店
        if (currentState.selectedShops.isEmpty()) {
            Log.e(TAG, "更新失败: 未选择门店")
            _state.update { it.copy(error = "请选择至少一个门店") }
            return
        }
        
        // 检查是否有选中的美团门店
        val selectedMeituanShops = currentState.selectedShops.filter { it.platform == "美团" }
        Log.d(TAG, "选中的美团门店数: ${selectedMeituanShops.size}, 总门店数: ${currentState.selectedShops.size}")
        
        viewModelScope.launch {
            Log.d(TAG, "开始批量更新营业时间: 选中门店数=${currentState.selectedShops.size}")
            _state.update { it.copy(isLoading = true, error = null, updateResult = null) }
            
            // 只包含实际选中门店的平台
            val actualSelectedPlatforms = currentState.selectedShops.map { it.platform }.distinct()
            Log.d(TAG, "实际选中的平台: $actualSelectedPlatforms")

            val request = BatchUpdateBusinessHoursRequest(
                businessHours = businessHours,
                platforms = actualSelectedPlatforms,
                shopIds = currentState.selectedShops.map { it.id }
            )
            
            // 检查账号信息
            val missingAccounts = checkMissingAccounts(request)
            if (missingAccounts.isNotEmpty()) {
                Log.d(TAG, "发现缺少账号信息: ${missingAccounts.size}个")
                // 只处理第一个缺失账号，避免同时弹出多个对话框
                val firstMissingAccount = missingAccounts.first()
                Log.d(TAG, "处理缺失账号: 平台=${firstMissingAccount.platform}, 门店=${firstMissingAccount.shopName}")
                _state.update { it.copy(
                    isLoading = false,
                    needAccountSetup = true,
                    missingAccounts = listOf(firstMissingAccount) // 只保留一个缺失账号
                )}
                return@launch
            }
            
            // 执行更新
            Log.d(TAG, "调用仓库执行批量更新")
            when (val result = repository.batchUpdateBusinessHours(request)) {
                is ApiResult.Success -> {
                    Log.d(TAG, "批量更新成功: 总数=${result.data.totalCount}, 成功=${result.data.successCount}, 失败=${result.data.failedCount}")
                    _state.update { it.copy(
                        isLoading = false,
                        updateResult = result.data,
                        error = null
                    )}
                }
                is ApiResult.Error -> {
                    Log.e(TAG, "批量更新失败: ${result.error}")
                    _state.update { it.copy(
                        isLoading = false,
                        error = result.error
                    )}
                }
                is ApiResult.AccountError -> {
                    Log.e(TAG, "账号认证错误: 平台=${result.platform}, 门店=${result.shopName}")
                    // 只有美团平台需要账号，饿了么平台不需要
                    if (result.platform == "美团") {
                        // 添加到缺失账号列表中
                        val accountInfo = ShopAccountInfo(
                            platform = result.platform,
                            shopId = result.shopId,
                            shopName = result.shopName
                        )
                        _state.update { it.copy(
                            isLoading = false,
                            needAccountSetup = true,
                            missingAccounts = listOf(accountInfo)
                        )}
                    } else {
                        // 饿了么平台不需要账号，当作一般错误处理
                        _state.update { it.copy(
                            isLoading = false,
                            error = "饿了么平台更新失败: 可能是Cookie已过期，请重新登录"
                        )}
                    }
                }
            }
        }
    }
    
    private suspend fun checkMissingAccounts(request: BatchUpdateBusinessHoursRequest): List<ShopAccountInfo> {
        val missingAccounts = mutableListOf<ShopAccountInfo>()
        
        // 检查当前选中的门店中是否有美团平台的门店
        val hasMeituanShops = _state.value.selectedShops.any { it.platform == "美团" }
        if (!hasMeituanShops) {
            // 没有选择美团门店，不需要检查账号
            Log.d(TAG, "未选择美团门店，跳过账号检查")
            return emptyList()
        }
        
        // 只检查请求中包含的门店（即用户选中的门店）
        val selectedShops = _state.value.selectedShops.filter { shop -> 
            request.shopIds.contains(shop.id) && shop.platform == "美团" 
        }
        
        for (shop in selectedShops) {
            // 只需要检查美团平台的门店，饿了么平台不需要账号
            val account = accountManager.getAccountByShopId("美团", shop.id)
            if (account == null) {
                Log.d(TAG, "缺少美团账号: 门店=${shop.name}, ID=${shop.id}")
                missingAccounts.add(ShopAccountInfo("美团", shop.id, shop.name))
            }
        }
        
        return missingAccounts
    }
    
    private fun dismissError() {
        _state.update { it.copy(error = null) }
    }
    
    private fun clearMissingAccounts() {
        _state.update { it.copy(missingAccounts = emptyList()) }
    }

    private fun clearUpdateResult() {
        _state.update { it.copy(updateResult = null) }
    }
} 