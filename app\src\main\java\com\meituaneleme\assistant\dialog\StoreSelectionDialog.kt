//package com.meituaneleme.assistant.dialog
//
//import android.app.Dialog
//import android.content.Context
//import android.os.Bundle
//import android.view.Window
//import androidx.recyclerview.widget.LinearLayoutManager
//import androidx.recyclerview.widget.RecyclerView
//import com.meituaneleme.assistant.R
////import com.meituaneleme.assistant.adapter.StoreSelectionAdapter
//import com.google.android.material.button.MaterialButton
//
//class StoreSelectionDialog(
//    context: Context,
//    private val stores: List<StoreSelectionAdapter.Store>,
//    private val onConfirm: (List<StoreSelectionAdapter.Store>) -> Unit
//) : Dialog(context) {
//
//    private lateinit var adapter: StoreSelectionAdapter
//    private lateinit var recyclerView: RecyclerView
//    private lateinit var btnSelectAll: MaterialButton
//    private lateinit var btnCancel: MaterialButton
//    private lateinit var btnConfirm: MaterialButton
//
//    override fun onCreate(savedInstanceState: Bundle?) {
//        super.onCreate(savedInstanceState)
//        requestWindowFeature(Window.FEATURE_NO_TITLE)
//        setContentView(R.layout.dialog_store_selection)
//
//        initViews()
//        setupRecyclerView()
//        setupListeners()
//    }
//
//    private fun initViews() {
//        recyclerView = findViewById(R.id.rv_stores)
//        btnSelectAll = findViewById(R.id.btn_select_all)
//        btnCancel = findViewById(R.id.btn_cancel)
//        btnConfirm = findViewById(R.id.btn_confirm)
//    }
//
//    private fun setupRecyclerView() {
//        adapter = StoreSelectionAdapter()
//        recyclerView.layoutManager = LinearLayoutManager(context)
//        recyclerView.adapter = adapter
//        adapter.setData(stores)
//    }
//
//    private fun setupListeners() {
//        btnSelectAll.setOnClickListener {
//            val isCurrentlySelected = adapter.getSelectedStores().size == stores.size
//            adapter.selectAll(!isCurrentlySelected)
//            btnSelectAll.text = if (!isCurrentlySelected) "取消全选" else "全选"
//        }
//
//        btnCancel.setOnClickListener {
//            dismiss()
//        }
//
//        btnConfirm.setOnClickListener {
//            onConfirm(adapter.getSelectedStores())
//            dismiss()
//        }
//    }
//}