package com.meituaneleme.assistant.security

import android.content.Context
import androidx.security.crypto.EncryptedSharedPreferences
import androidx.security.crypto.MasterKey
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import com.meituaneleme.assistant.model.PlatformAccountModel
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

/**
 * 账号信息存储接口
 */
interface AccountStorageManager {
    suspend fun saveAccount(account: PlatformAccountModel)
    suspend fun getAccounts(platform: String): List<PlatformAccountModel>
    suspend fun getAccountByShopId(platform: String, shopId: String): PlatformAccountModel?
    suspend fun deleteAccount(platform: String, shopId: String)
    suspend fun getAllAccounts(): List<PlatformAccountModel>
}

/**
 * 使用加密SharedPreferences实现账号存储
 */
class EncryptedAccountStorageManager(
    private val context: Context
) : AccountStorageManager {

    private val gson = Gson()
    private val accountsListType = object : TypeToken<List<PlatformAccountModel>>() {}.type
    
    private val masterKey = MasterKey.Builder(context)
        .setKeyScheme(MasterKey.KeyScheme.AES256_GCM)
        .build()
    
    private val encryptedSharedPreferences = EncryptedSharedPreferences.create(
        context,
        "encrypted_account_storage",
        masterKey,
        EncryptedSharedPreferences.PrefKeyEncryptionScheme.AES256_SIV,
        EncryptedSharedPreferences.PrefValueEncryptionScheme.AES256_GCM
    )
    
    override suspend fun saveAccount(account: PlatformAccountModel) = withContext(Dispatchers.IO) {
        val accounts = getAllAccounts().toMutableList()
        
        // 检查是否已存在相同平台和店铺ID的账号
        val existingIndex = accounts.indexOfFirst { 
            it.platform == account.platform && it.shopId == account.shopId 
        }
        
        if (existingIndex != -1) {
            // 更新现有账号
            accounts[existingIndex] = account
        } else {
            // 添加新账号
            accounts.add(account)
        }
        
        // 保存更新后的账号列表
        encryptedSharedPreferences.edit()
            .putString("accounts", gson.toJson(accounts))
            .apply()
    }
    
    override suspend fun getAccounts(platform: String): List<PlatformAccountModel> = withContext(Dispatchers.IO) {
        getAllAccounts().filter { it.platform == platform }
    }
    
    override suspend fun getAccountByShopId(platform: String, shopId: String): PlatformAccountModel? = withContext(Dispatchers.IO) {
        getAllAccounts().find { it.platform == platform && it.shopId == shopId }
    }
    
    override suspend fun deleteAccount(platform: String, shopId: String) = withContext(Dispatchers.IO) {
        val accounts = getAllAccounts().toMutableList()
        accounts.removeIf { it.platform == platform && it.shopId == shopId }
        
        encryptedSharedPreferences.edit()
            .putString("accounts", gson.toJson(accounts))
            .apply()
    }
    
    override suspend fun getAllAccounts(): List<PlatformAccountModel> = withContext(Dispatchers.IO) {
        val accountsJson = encryptedSharedPreferences.getString("accounts", "[]")
        gson.fromJson(accountsJson, accountsListType) ?: emptyList()
    }
} 