@echo off
chcp 65001 >nul
title 京东抢资格助手 v2.0

echo 京东抢资格助手 v2.0
echo ========================================
echo.

REM 检查Python是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo 错误: 未找到Python，请先安装Python 3.7或更高版本
    echo 下载地址: https://www.python.org/downloads/
    pause
    exit /b 1
)

REM 检查依赖包
echo 正在检查依赖包...
python -c "import customtkinter" >nul 2>&1
if errorlevel 1 (
    echo 正在安装customtkinter...
    pip install customtkinter
    if errorlevel 1 (
        echo 安装失败，请手动运行: pip install customtkinter
        pause
        exit /b 1
    )
)

python -c "import requests" >nul 2>&1
if errorlevel 1 (
    echo 正在安装requests...
    pip install requests
    if errorlevel 1 (
        echo 安装失败，请手动运行: pip install requests
        pause
        exit /b 1
    )
)

echo 依赖包检查完成
echo.

REM 启动GUI应用
echo 正在启动图形界面...
python start_gui.py

if errorlevel 1 (
    echo.
    echo 程序异常退出
    pause
)
