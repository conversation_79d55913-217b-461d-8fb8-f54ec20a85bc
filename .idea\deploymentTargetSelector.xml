<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="deploymentTargetSelector">
    <selectionStates>
      <SelectionState runConfigName="app">
        <option name="selectionMode" value="DROPDOWN" />
        <DropdownSelection timestamp="2024-11-26T05:45:04.812836300Z">
          <Target type="DEFAULT_BOOT">
            <handle>
              <DeviceId pluginId="LocalEmulator" identifier="path=C:\Users\<USER>\.android\avd\Pixel_8_API_30.avd" />
            </handle>
          </Target>
        </DropdownSelection>
        <DialogSelection />
      </SelectionState>
      <SelectionState runConfigName="MainActivity">
        <option name="selectionMode" value="DROPDOWN" />
        <DropdownSelection timestamp="2024-11-29T11:38:30.139999100Z">
          <Target type="DEFAULT_BOOT">
            <handle>
              <DeviceId pluginId="PhysicalDevice" identifier="serial=UJN0221107008777" />
            </handle>
          </Target>
        </DropdownSelection>
        <DialogSelection />
      </SelectionState>
    </selectionStates>
  </component>
</project>