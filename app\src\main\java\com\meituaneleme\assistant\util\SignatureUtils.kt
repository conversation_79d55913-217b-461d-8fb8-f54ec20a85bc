package com.meituaneleme.assistant.util

import android.content.Context
import android.content.pm.PackageManager
import android.util.Log
import com.meituaneleme.assistant.BuildConfig

object SignatureUtils {
    private const val TAG = "SignatureUtils"
    
    // Debug 签名片段
    private val DEBUG_SIGNATURE_PARTS = arrayOf(
        "7da8ec",
        "714542",
        "8ca9fb",
        "343a6b",
        "563e29",
        "a7b4ac",
        "5ffe"
    )
    
    // Release 签名片段
    private val RELEASE_SIGNATURE_PARTS = arrayOf(
        "0cd6bc",
        "45c4f8",
        "a68f50",
        "c4e19c",
        "5153cb",
        "53b12d",
        "1879"
    )
    
    private val CORRECT_SIGNATURE by lazy {
        if (BuildConfig.DEBUG) {
            DEBUG_SIGNATURE_PARTS.joinToString("")
        } else {
            RELEASE_SIGNATURE_PARTS.joinToString("")
        }
    }

    fun verifyAppSignature(context: Context): Bo<PERSON>an {
        try {
            val packageInfo = context.packageManager.getPackageInfo(
                context.packageName,
                PackageManager.GET_SIGNATURES
            )
            
            val signatures = packageInfo.signatures
            if (signatures.isNullOrEmpty()) {
                Log.e(TAG, "No signature found")
                return false
            }

            val currentSignature = signatures[0]
            val signatureSHA1 = calculateSHA1(currentSignature.toByteArray())
            
            // 验证签名是否匹配
            val isValid = signatureSHA1 == CORRECT_SIGNATURE
            
            if (!isValid) {
                Log.e(TAG, "Invalid signature detected: $signatureSHA1")
                // 在 Debug 模式下打印更多信息以便调试
                if (BuildConfig.DEBUG) {
                    Log.d(TAG, "Current signature: $signatureSHA1")
                    Log.d(TAG, "Expected signature: $CORRECT_SIGNATURE")
                }
            }
            
            return isValid
        } catch (e: Exception) {
            Log.e(TAG, "Signature verification failed", e)
            return false
        }
    }

    private fun calculateSHA1(signature: ByteArray): String {
        val md = java.security.MessageDigest.getInstance("SHA-1")
        val digest = md.digest(signature)
        return digest.joinToString("") { "%02x".format(it) }
    }
} 