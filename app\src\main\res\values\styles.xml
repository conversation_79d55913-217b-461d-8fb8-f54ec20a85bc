<?xml version="1.0" encoding="utf-8"?>
<resources>
    <!-- 基础主题 -->
    <style name="Theme.assistant" parent="Theme.MaterialComponents.Light.NoActionBar">
        <!-- 自定义主题颜色 -->
        <item name="colorPrimary">@color/colorPrimary</item>
        <item name="colorPrimaryDark">@color/colorPrimaryDark</item>
        <item name="colorAccent">@color/colorAccent</item>
        <item name="colorSecondary">@color/colorSecondary</item>
        <item name="android:colorBackground">@color/background_light</item>
        <item name="android:statusBarColor">@color/colorPrimaryDark</item>
        <item name="android:windowLightStatusBar">true</item>
        
        <!-- Material Design 组件样式 -->
        <item name="materialButtonStyle">@style/Widget.App.Button</item>
        <item name="textInputStyle">@style/Widget.App.TextInputLayout</item>
        <item name="cardViewStyle">@style/Widget.App.CardView</item>
    </style>

    <!-- 卡片样式 -->
    <style name="Widget.App.CardView" parent="Widget.MaterialComponents.CardView">
        <item name="cardElevation">0.5dp</item>
        <item name="cardCornerRadius">10dp</item>
        <item name="cardPreventCornerOverlap">true</item>
        <item name="cardUseCompatPadding">true</item>
        <item name="contentPadding">0dp</item>
    </style>

    <!-- 基础按钮样式 -->
    <style name="Widget.App.Button" parent="Widget.MaterialComponents.Button">
        <item name="android:textSize">14sp</item>
        <item name="android:paddingStart">16dp</item>
        <item name="android:paddingEnd">16dp</item>
        <item name="android:minHeight">48dp</item>
        <item name="backgroundTint">@color/colorPrimary</item>
        <item name="android:textColor">@android:color/white</item>
        <item name="cornerRadius">10dp</item>
        <item name="elevation">0dp</item>
        <item name="rippleColor">#33FFFFFF</item>
    </style>
    
    <!-- 轮廓按钮样式 -->
    <style name="Widget.App.Button.Outlined" parent="Widget.MaterialComponents.Button.OutlinedButton">
        <item name="android:textSize">14sp</item>
        <item name="android:paddingStart">16dp</item>
        <item name="android:paddingEnd">16dp</item>
        <item name="android:minHeight">48dp</item>
        <item name="strokeWidth">0.5dp</item>
        <item name="strokeColor">@color/colorPrimary</item>
        <item name="cornerRadius">10dp</item>
        <item name="rippleColor">@color/colorPrimary_20</item>
        <item name="android:textColor">@color/colorPrimary</item>
    </style>
    
    <!-- 次级按钮样式 -->
    <style name="Widget.App.Button.Secondary" parent="Widget.App.Button.Outlined">
        <item name="strokeColor">@color/colorSecondary</item>
        <item name="android:textColor">@color/colorSecondary</item>
        <item name="rippleColor">@color/colorSecondary_20</item>
    </style>
    
    <!-- 灰色禁用按钮样式 -->
    <style name="Widget.App.Button.Disabled" parent="Widget.App.Button.Outlined">
        <item name="strokeColor">@color/status_unavailable</item>
        <item name="android:textColor">@color/status_unavailable</item>
        <item name="rippleColor">@color/colorPrimary_20</item>
    </style>

    <!-- 输入框样式 -->
    <style name="Widget.App.TextInputLayout" parent="Widget.MaterialComponents.TextInputLayout.OutlinedBox">
        <item name="boxCornerRadiusBottomEnd">10dp</item>
        <item name="boxCornerRadiusBottomStart">10dp</item>
        <item name="boxCornerRadiusTopEnd">10dp</item>
        <item name="boxCornerRadiusTopStart">10dp</item>
        <item name="boxStrokeColor">@color/colorPrimary</item>
        <item name="boxStrokeWidth">0.5dp</item>
        <item name="hintTextColor">@color/colorPrimary</item>
    </style>

    <!-- 圆角图片样式 -->
    <style name="RoundedImageView">
        <item name="cornerFamily">rounded</item>
        <item name="cornerSize">10dp</item>
    </style>
</resources>