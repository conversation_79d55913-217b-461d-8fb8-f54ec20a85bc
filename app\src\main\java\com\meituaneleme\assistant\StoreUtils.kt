package com.meituaneleme.assistant

import com.meituaneleme.assistant.ElemeApi
import com.meituaneleme.assistant.MeiTuanApi
import android.content.Context
import android.content.Intent
import android.widget.Toast
import com.google.gson.Gson
import com.meituaneleme.assistant.api.ApiClient
import com.meituaneleme.assistant.api.CookiesResponse
import com.meituaneleme.assistant.api.CookiesResponseWrapper
import com.meituaneleme.assistant.api.StoreResponse
import com.meituaneleme.assistant.api.StoreResponseWrapper
import retrofit2.Call
import retrofit2.Callback
import retrofit2.Response
import org.json.JSONArray
import org.json.JSONObject
import android.util.Log
import androidx.fragment.app.FragmentActivity
import com.meituaneleme.assistant.api.StoreValidCheck
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

object StoreUtils {
    fun getStoreDetails(context: Context, username: String, isFromStoreManagement: Boolean = false) {
        val whereClause = "{\"owner\":\"$username\"}"
        ApiClient.api.getStores(whereClause).enqueue(object : Callback<StoreResponseWrapper> {
            override fun onResponse(call: Call<StoreResponseWrapper>, response: Response<StoreResponseWrapper>) {
                if (response.isSuccessful) {
                    val storeList = response.body()?.results ?: emptyList()
                    Log.d("StoreUtils", "Store list size: ${storeList.size}")

                    if (storeList.isEmpty() && isFromStoreManagement) {
                        val intent = Intent(context, MainWithTabsActivity::class.java)
                        context.startActivity(intent)
                    } else {
                        // 获取每个门店的 cookies 并检查有效性
                        storeList.parallelStream().forEach { store ->
                            getStoreCookies(context, store.objectId, store.storeName, store.platform)
                        }

//                        if (isFromStoreManagement) {
//                            val intent = Intent(context, MainWithTabsActivity::class.java)
//                            context.startActivity(intent)
//                        }
                    }
                } else {
                    Toast.makeText(context, "获取门店失败: ${response.message()}", Toast.LENGTH_SHORT).show()
                }
            }

            override fun onFailure(call: Call<StoreResponseWrapper>, t: Throwable) {
                Toast.makeText(context, "网络错误: ${t.message}", Toast.LENGTH_SHORT).show()
            }
        })
    }

    private fun getStoreCookies(context: Context, storeId: String, storeName: String, platform: String) {
        val whereClause = "{\"storeId\":\"$storeId\"}"
        ApiClient.api.getStoreCookies(whereClause).enqueue(object : Callback<CookiesResponseWrapper> {
            override fun onResponse(call: Call<CookiesResponseWrapper>, response: Response<CookiesResponseWrapper>) {
                if (response.isSuccessful) {
                    val cookies = response.body()?.results ?: return
                    // 保存到本地

                    saveCookiesToLocal(context, storeName, cookies, platform, false)


                } else {
                    Toast.makeText(context, "获取门店信息失败", Toast.LENGTH_SHORT).show()
                }
            }

            override fun onFailure(call: Call<CookiesResponseWrapper>, t: Throwable) {
                Toast.makeText(context, "网络错误: ${t.message}", Toast.LENGTH_SHORT).show()
            }
        })
    }

    fun saveCookiesToLocal(context: Context, storeName: String, cookies: List<CookiesResponse>, platform: String, isValid: Boolean) {
        val sharedPreferences = context.getSharedPreferences("StoreCookies", Context.MODE_PRIVATE)
        val existingData = sharedPreferences.getString("storeCookiesList", "[]")
        val jsonArray = JSONArray(existingData)

        var exists = false
        for (i in 0 until jsonArray.length()) {
            val existingStore = jsonArray.getJSONObject(i)
            if (existingStore.getString("storeName") == storeName && existingStore.getString("platform") == platform) {
                existingStore.put("cookies", cookies[0].cookies)
                existingStore.put("isValid", isValid)
                existingStore.put("platform", platform)
                exists = true
                break
            }
        }

        if (!exists) {
            val newStoreData = JSONObject().apply {
                put("storeName", storeName)
                put("cookies", cookies[0].cookies)
                put("platform", platform)
                put("isValid", isValid)
            }
            jsonArray.put(newStoreData)
        }

        with(sharedPreferences.edit()) {
            putString("storeCookiesList", jsonArray.toString())
            apply()
        }
 
    }
} 