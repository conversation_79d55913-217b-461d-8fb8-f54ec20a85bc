// RegisterActivity.kt
package com.meituaneleme.assistant

import RegisterViewModel
import android.content.Intent
import android.os.Bundle
import android.widget.Button
import android.widget.EditText
import android.widget.TextView
import android.widget.Toast
import androidx.appcompat.app.AppCompatActivity
import androidx.appcompat.app.AlertDialog
//import androidx.lifecycle.viewModels

import com.meituaneleme.assistant.api.ApiClient
import com.meituaneleme.assistant.api.User
import com.meituaneleme.assistant.api.UserResponse
import retrofit2.Call
import retrofit2.Response
import com.meituaneleme.assistant.databinding.ActivityRegisterBinding
import androidx.activity.viewModels
import androidx.lifecycle.ViewModelProvider

class RegisterActivity : AppCompatActivity() {
    private lateinit var binding: ActivityRegisterBinding
    private val viewModel: RegisterViewModel by viewModels()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityRegisterBinding.inflate(layoutInflater)
        setContentView(binding.root)

        binding.apply {
            btnRegister.setOnClickListener {
                val username = etUsername.text.toString()
                val password = etPassword.text.toString()
                val confirmPassword = etConfirmPassword.text.toString()

                when {
                    username.isEmpty() -> showToast("请输入用户名")
                    password.isEmpty() -> showToast("请输入密码")
                    confirmPassword.isEmpty() -> showToast("请确认密码")
                    password != confirmPassword -> showToast("两次输入的密码不一致")
                    !isValidPassword(password) -> return@setOnClickListener
                    else -> viewModel.register(username, password, this@RegisterActivity)
                }
            }

            loginPrompt.setOnClickListener {
                startActivity(Intent(this@RegisterActivity, LoginActivity::class.java))
                finish()
            }
        }

        // 观察注册结果
        viewModel.registerResult.observe(this) { success ->
            if (success) {
                showToast("注册成功")
                finish()
            }
        }

        // 观察已注册用户信息
        viewModel.registeredUserInfo.observe(this) { info ->
            if (info.isNotEmpty()) {
                AlertDialog.Builder(this)
                    .setTitle("设备已注册")
                    .setMessage(info)
                    .setPositiveButton("去登录") { _, _ ->
                        startActivity(Intent(this, LoginActivity::class.java))
                        finish()
                    }
                    .setNegativeButton("取消", null)
                    .show()
            }
        }
    }

    private fun isValidPassword(password: String): Boolean {
        if (password.length < 6) {
            showToast("密码长度必须大于6位")
            return false
        }
        
        // 检查是否包含字母和数字
        val hasLetter = password.any { it.isLetter() }
        val hasDigit = password.any { it.isDigit() }
        
        if (!hasLetter || !hasDigit) {
            showToast("密码必须包含字母和数字")
            return false
        }
        
        return true
    }

    private fun showToast(message: String) {
        Toast.makeText(this, message, Toast.LENGTH_SHORT).show()
    }
}