package com.meituaneleme.assistant.model

/**
 * 通用营业时间模型
 */
data class BusinessHourModel(
    val startHour: Int = 9,
    val startMinute: Int = 0,
    val endHour: Int = 21,
    val endMinute: Int = 0,
    val weekDays: List<Int> = listOf(1, 2, 3, 4, 5, 6, 7),
    val crossDay: Boolean = false
) {
    /**
     * 将时间转换为字符串
     */
    fun toTimeString(): String {
        val startTimeStr = String.format("%02d:%02d", startHour, startMinute)
        val endTimeStr = String.format("%02d:%02d", endHour, endMinute)
        return "$startTimeStr-$endTimeStr"
    }

    /**
     * 调整时间为5分钟的倍数
     */
    fun adjustToFiveMinuteIntervals(): BusinessHourModel {
        val adjustedStartMinute = (startMinute / 5) * 5
        val adjustedEndMinute = (endMinute / 5) * 5
        
        return this.copy(
            startMinute = adjustedStartMinute,
            endMinute = adjustedEndMinute
        )
    }
}

/**
 * 平台账号信息模型
 */
data class PlatformAccountModel(
    val platform: String, // "meituan" 或 "eleme"
    val shopId: String,
    val username: String,
    val password: String,
    val token: String? = null
)

/**
 * 批量修改请求模型
 */
data class BatchUpdateBusinessHoursRequest(
    val businessHours: BusinessHourModel,
    val platforms: List<String>, // "meituan", "eleme"
    val shopIds: List<String> = emptyList() // 默认为空列表，表示全部门店
)

/**
 * 批量更新结果模型
 */
data class BatchUpdateResult(
    val totalCount: Int,
    val successCount: Int,
    val failedCount: Int,
    val failedDetails: List<FailedDetail>
)

/**
 * 失败详情模型
 */
data class FailedDetail(
    val platform: String,
    val shopName: String,
    val reason: String
) 