package com.meituaneleme.assistant

// ElemeApi.kt
import android.util.Log
import com.google.gson.Gson
import okhttp3.MediaType.Companion.toMediaTypeOrNull
import okhttp3.OkHttpClient
import okhttp3.Request
import okhttp3.RequestBody
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import org.json.JSONObject
import java.io.IOException
import java.util.concurrent.TimeUnit
import java.security.MessageDigest
import com.meituaneleme.assistant.config.AppConfig

data class PageQueryData(
    val cookies: String,
    val shopname: String,
    val productname: String
)

data class PostDataEle(
    val cookies: String,
    val shopname: String,
    val action: String,
    val post_data: String
)

data class EditPriceEle(
    val cookies: String,
    val shopname: String,
    val price: String,
    val itemID: String
)

data class BatchShelfEle(
    val cookies: String,
    val shopname: String,
    val status: Int,
    val itemID: String
)

class ElemeApi(private val cookiesStr: String, private val shopName: String) {

    private val TAG = "ElemeApi"

    /**
     * 通用POST请求方法
     * @param url 请求的URL
     * @param data 请求体数据
     * @param headers 请求头
     * @return 响应字符串
     */
    suspend fun postRequest(url: String, data: String, headers: Map<String, String>): String = withContext(Dispatchers.IO) {
        Log.d(TAG, "发起POST请求: url=$url")
        val client = OkHttpClient.Builder()
            .connectTimeout(30, TimeUnit.SECONDS)
            .readTimeout(30, TimeUnit.SECONDS)
            .writeTimeout(30, TimeUnit.SECONDS)
            .build()
        
        val mediaType = "application/x-www-form-urlencoded".toMediaTypeOrNull()
        val requestBody = RequestBody.create(mediaType, data)
        
        val requestBuilder = Request.Builder()
            .url(url)
            .post(requestBody)
        
        // 添加请求头
        headers.forEach { (key, value) ->
            requestBuilder.addHeader(key, value)
        }
        
        val request = requestBuilder.build()
        
        try {
            val response = client.newCall(request).execute()
            val responseBody = response.body?.string() ?: ""
            Log.d(TAG, "POST请求响应: isSuccessful=${response.isSuccessful}, code=${response.code}")
            
            if (response.isSuccessful) {
                responseBody
            } else {
                Log.e(TAG, "POST请求失败: code=${response.code}, message=${response.message}")
                "请求失败: ${response.code} ${response.message}"
            }
        } catch (e: Exception) {
            Log.e(TAG, "POST请求异常", e)
            "请求异常: ${e.message}"
        }
    }
    
    /**
     * 生成MD5签名
     */
    fun generateMD5(input: String): String {
        try {
            val md = MessageDigest.getInstance("MD5")
            val digest = md.digest(input.toByteArray())
            val hexString = StringBuilder()
            for (b in digest) {
                val hex = Integer.toHexString(0xff and b.toInt())
                if (hex.length == 1) hexString.append('0')
                hexString.append(hex)
            }
            return hexString.toString()
        } catch (e: Exception) {
            Log.e(TAG, "MD5生成失败", e)
            return ""
        }
    }

    // 搜索商品
    suspend fun pageQuery(productName: String): String = withContext(Dispatchers.IO) {
        try {
            val mtgsigData = JSONObject()
            mtgsigData.put("cookies", cookiesStr)
            mtgsigData.put("shopname", shopName)
            mtgsigData.put("productname", productName)

            val requestBody = RequestBody.create(
                "application/json".toMediaTypeOrNull(),
                mtgsigData.toString()
            )
            val request = Request.Builder()
                .url("${AppConfig.API_BASE_URL}/get_eleme_data_pageQuery")
                .post(requestBody)
                .build()
            val client = OkHttpClient.Builder()
                .connectTimeout(10, TimeUnit.SECONDS)
                .build()
            val response = client.newCall(request).execute()
            if (response.isSuccessful) {
                return@withContext response.body?.string() ?: ""
            } else {
                return@withContext "获取 数据 失败"
            }
        } catch (e: IOException) {
            return@withContext "请求失败: ${e.message}"
        }
    }

    // 修改库存的方法
    suspend fun changeStock(quantity: String, itemID: String): String = withContext(Dispatchers.IO) {
        val postData = mapOf(
            "cookies" to cookiesStr,
            "shopname" to shopName,
            "quantity" to quantity,  // 这里使用quantity代替price
            "itemID" to itemID
        )

        val client = OkHttpClient.Builder()
            .connectTimeout(10, TimeUnit.SECONDS)
            .build()

        val requestBody = RequestBody.create("application/json".toMediaTypeOrNull(), JSONObject(postData).toString())
        val request = Request.Builder()
            .url("${AppConfig.API_BASE_URL}/editstock_ele")  // 这里修改为库存API端点
            .post(requestBody)
            .build()

        return@withContext try {
            val response = client.newCall(request).execute()
            if (response.isSuccessful) {
                response.body?.string() ?: "无返回数据"
            } else {
                "请求失败，错误码: ${response.code}"
            }
        } catch (e: IOException) {
            "请求失败: ${e.message}"
        }
    }


    // post_data_ele 方法 获取折扣
    suspend fun postDataEle(action: String, data: String): String = withContext(Dispatchers.IO) {
        val postData = PostDataEle(
            cookies = cookiesStr,
            shopname = shopName,
            action = action,
            post_data = data
        )

        val gson = Gson()
        val jsonString = gson.toJson(postData)
        val requestBody = RequestBody.create("application/json".toMediaTypeOrNull(), jsonString)

        val client = OkHttpClient.Builder()
            .connectTimeout(10, TimeUnit.SECONDS)
            .build()

        val request = Request.Builder()
            .url("${AppConfig.API_BASE_URL}/post_data")  // 你需要替换 `your_host_here` 为实际的 host 地址
            .post(requestBody)
            .build()

        return@withContext try {
            val response = client.newCall(request).execute()
            if (response.isSuccessful) {
                response.body?.string() ?: "无返回数据"
            } else {
                "请求失败，错误码: ${response.code}"
            }
        } catch (e: IOException) {
            "请求失败: ${e.message}"
        }
    }

    // 修改原价的方法
    suspend fun changeOrignPrice(price: String, itemID: String): String = withContext(Dispatchers.IO) {
        val postData = mapOf(
            "cookies" to cookiesStr,
            "shopname" to shopName,
            "price" to price,
            "itemID" to itemID
        )

        val client = OkHttpClient.Builder()
            .connectTimeout(10, TimeUnit.SECONDS)
            .build()

        val requestBody = RequestBody.create("application/json".toMediaTypeOrNull(), JSONObject(postData).toString())
        val request = Request.Builder()
            .url("${AppConfig.API_BASE_URL}/editprice_ele")
            .post(requestBody)
            .build()

        return@withContext try {
            val response = client.newCall(request).execute()
            if (response.isSuccessful) {
                response.body?.string() ?: "无返回数据"
            } else {
                "请求失败，错误码: ${response.code}"
            }
        } catch (e: IOException) {
            "请求失败: ${e.message}"
        }
    }

    suspend fun batchShelf(itemID: String, status: Int): String = withContext(Dispatchers.IO) {
        val postData = mapOf(
            "cookies" to cookiesStr,
            "shopname" to shopName,
            "status" to status,
            "itemID" to itemID
        )

        val client = OkHttpClient.Builder()
            .connectTimeout(10, TimeUnit.SECONDS)
            .build()

        val requestBody = RequestBody.create("application/json".toMediaTypeOrNull(), JSONObject(postData).toString())
        val request = Request.Builder()
            .url("${AppConfig.API_BASE_URL}/batchShelf")
            .post(requestBody)
            .build()

        return@withContext try {
            val response = client.newCall(request).execute()
            if (response.isSuccessful) {
                response.body?.string() ?: "无返回数据"
            } else {
                "请求失败，错误码: ${response.code}"
            }
        } catch (e: IOException) {
            "请求失败: ${e.message}"
        }
    }

    //修改商品名称
    suspend fun item_edit(itemID: String, title: String): String = withContext(Dispatchers.IO) {
        val postData = mapOf(
            "cookies" to cookiesStr,
            "shopname" to shopName,
            "title" to title,
            "itemID" to itemID
        )

        val client = OkHttpClient.Builder()
            .connectTimeout(10, TimeUnit.SECONDS)
            .build()

        val requestBody = RequestBody.create("application/json".toMediaTypeOrNull(), JSONObject(postData).toString())
        val request = Request.Builder()
            .url("${AppConfig.API_BASE_URL}/item_edit")
            .post(requestBody)
            .build()

        return@withContext try {
            val response = client.newCall(request).execute()
            if (response.isSuccessful) {
                response.body?.string() ?: "无返回数据"
            } else {
                "请求失败，错误码: ${response.code}"
            }
        } catch (e: IOException) {
            "请求失败: ${e.message}"
        }
    }

}