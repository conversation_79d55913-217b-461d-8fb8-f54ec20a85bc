<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="com.meituaneleme.assistant">
    <uses-permission android:name="android.permission.INTERNET"/>
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.REQUEST_INSTALL_PACKAGES" />
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE"
        android:maxSdkVersion="28" />

    <application
        android:name=".MyApplication"
        android:allowBackup="true"
        android:icon="@mipmap/logo"
        android:usesCleartextTraffic="true"
        android:label="@string/app_name"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:networkSecurityConfig="@xml/network_security_config"
        android:supportsRtl="true"
        android:theme="@style/Theme.assistant">

        <activity
            android:name=".MainActivity"
            android:exported="true">
        </activity>
        <activity
            android:name=".LoginActivity"
            android:exported="true">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>
        <activity android:name=".ProductDetailsActivity" />
        <activity android:name=".RegisterActivity" />
        <activity android:name=".StoreManagementActivity" />
        <activity android:name=".WebViewActivity" />
        <activity android:name=".MainWithTabsActivity"/>
        <activity android:name=".DeviceFingerprintActivity"
            android:label="设备指纹管理"
            android:exported="false" />

        <activity
            android:name=".BusinessHoursActivity"
            android:label="门店营业时间设置"
            android:exported="false"
            android:parentActivityName=".MainWithTabsActivity">
            <meta-data
                android:name="android.support.PARENT_ACTIVITY"
                android:value=".MainWithTabsActivity" />
        </activity>

        <activity
            android:name=".HelpGuideActivity"
            android:label="使用帮助"
            android:exported="false"
            android:parentActivityName=".MainActivity">
            <meta-data
                android:name="android.support.PARENT_ACTIVITY"
                android:value=".MainActivity" />
        </activity>

        <activity
            android:name=".ImageViewerActivity"
            android:theme="@style/Theme.MaterialComponents.DayNight.NoActionBar"
            android:exported="false">
            <meta-data
                android:name="android.support.PARENT_ACTIVITY"
                android:value=".MainActivity" />
        </activity>

        <provider
            android:name="androidx.core.content.FileProvider"
            android:authorities="${applicationId}.fileprovider"
            android:exported="false"
            android:grantUriPermissions="true">
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/file_paths" />
        </provider>
    </application>

</manifest>