package com.meituaneleme.assistant.adapter

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.meituaneleme.assistant.R
import com.google.android.material.checkbox.MaterialCheckBox

//class StoreSelectionAdapter : RecyclerView.Adapter<StoreSelectionAdapter.ViewHolder>() {
//    private val stores = mutableListOf<Store>()
//    private val selectedStores = mutableSetOf<String>()
//
//    data class Store(
//        val id: String,
//        val name: String,
//        var isSelected: Boolean = false
//    )
//
//    fun setData(newStores: List<Store>) {
//        stores.clear()
//        stores.addAll(newStores)
//        selectedStores.clear()
//        selectedStores.addAll(newStores.filter { it.isSelected }.map { it.id })
//        notifyDataSetChanged()
//    }
//
//    fun getSelectedStores(): List<Store> {
//        return stores.filter { selectedStores.contains(it.id) }
//    }
//
//    fun selectAll(isSelected: Boolean) {
//        stores.forEach { it.isSelected = isSelected }
//        if (isSelected) {
//            selectedStores.addAll(stores.map { it.id })
//        } else {
//            selectedStores.clear()
//        }
//        notifyDataSetChanged()
//    }
//
////    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
////        val view = LayoutInflater.from(parent.context)
////            .inflate(R.layout.item_store_selection, parent, false)
////        return ViewHolder(view)
////    }
//
//    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
//        val store = stores[position]
//        holder.bind(store)
//    }
//
//    override fun getItemCount(): Int = stores.size
//
//    inner class ViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
//        private val checkbox: MaterialCheckBox = itemView.findViewById(R.id.cb_store)
//        private val storeName: TextView = itemView.findViewById(R.id.tv_store_name)
//
//        fun bind(store: Store) {
//            storeName.text = store.name
//            checkbox.isChecked = selectedStores.contains(store.id)
//
//            itemView.setOnClickListener {
//                checkbox.isChecked = !checkbox.isChecked
//                if (checkbox.isChecked) {
//                    selectedStores.add(store.id)
//                } else {
//                    selectedStores.remove(store.id)
//                }
//                store.isSelected = checkbox.isChecked
//            }
//
//            checkbox.setOnCheckedChangeListener { _, isChecked ->
//                if (isChecked) {
//                    selectedStores.add(store.id)
//                } else {
//                    selectedStores.remove(store.id)
//                }
//                store.isSelected = isChecked
//            }
//        }
//    }
//}