<!-- layout/activity_login.xml -->
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:padding="16dp"
    android:background="@color/white">

    <TextView
        android:id="@+id/title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="登录"
        android:textSize="24sp"
        android:textColor="@color/black"
        android:layout_gravity="center"
        android:paddingBottom="24dp"/>

    <EditText
        android:id="@+id/login_username"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:hint="用户名"
        android:padding="12dp"
        android:background="@drawable/edit_text_background"
        android:layout_marginBottom="16dp"/>

    <EditText
        android:id="@+id/login_password"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:hint="密码"
        android:padding="12dp"
        android:background="@drawable/edit_text_background"
        android:inputType="textPassword"
        android:layout_marginBottom="24dp"/>

    <Button
        android:id="@+id/login_button"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="登录"
        android:textColor="@color/white"
        android:background="@drawable/rounded_button"
        android:layout_marginTop="24dp"/>

    <TextView
        android:id="@+id/register_prompt"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="没有账户？注册"
        android:textColor="@color/colorPrimary"
        android:layout_gravity="center"
        android:layout_marginTop="16dp"
        android:clickable="true"
        android:focusable="true"/>



</LinearLayout>