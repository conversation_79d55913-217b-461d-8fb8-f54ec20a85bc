package com.meituaneleme.assistant

import android.content.Intent
import android.os.Bundle
import android.util.Log
import android.widget.Button
import android.widget.EditText
import android.widget.ProgressBar
import android.widget.TextView
import android.widget.Toast
import androidx.appcompat.app.AppCompatActivity
import com.google.gson.Gson
import com.meituaneleme.assistant.api.ApiClient
import com.meituaneleme.assistant.api.User
import com.meituaneleme.assistant.R
import com.meituaneleme.assistant.RegisterActivity
import com.meituaneleme.assistant.StoreManagementActivity
import androidx.lifecycle.lifecycleScope
import cn.leancloud.LeanCloud
import com.meituaneleme.assistant.api.LoginResponse
import com.meituaneleme.assistant.update.UpdateManager
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import retrofit2.Call
import retrofit2.Callback
import retrofit2.Response

class LoginActivity : AppCompatActivity() {
    private lateinit var usernameEditText: EditText
    private lateinit var passwordEditText: EditText
    private lateinit var loginButton: Button
    private lateinit var registerPrompt: TextView
    private lateinit var updateManager: UpdateManager


    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        // 检查是否有保存的登录信息
        val sharedPreferences = getSharedPreferences("UserPrefs", MODE_PRIVATE)
        val sessionToken = sharedPreferences.getString("sessionToken", "")
        val username = sharedPreferences.getString("username", "")


        updateManager = UpdateManager(this)
        // 检查更新按钮点击事件

        lifecycleScope.launch {
            val needsUpdate = updateManager.checkUpdate()
            if (!needsUpdate) {
                if (!sessionToken.isNullOrEmpty() && !username.isNullOrEmpty()) {
                    // 有登录信息，直接进行自动登录
                    lifecycleScope.launch {
                        try {
                            // 显示加载提示（可选）
                            Toast.makeText(this@LoginActivity, "正在自动登录...", Toast.LENGTH_SHORT).show()

                            // 获取门店信息
                            StoreUtils.getStoreDetails(this@LoginActivity, username, true)

                            // 延迟一下确保数据获取完成
                            delay(1000)

                            // 跳转到主界面
                            val intent = Intent(this@LoginActivity, MainWithTabsActivity::class.java)
                            startActivity(intent)
                            finish()
                        } catch (e: Exception) {
                            Log.e("LoginActivity", "自动登录失败: ${e.message}")
                            // 自动登录失败，显示登录界面
                            initializeLoginUI()
                        }
                    }
                } else {
                    // 没有登录信息，显示登录界面
                    initializeLoginUI()
                }
            }
        }
    }

    // 将原来 onCreate 中的 UI 初始化代码移到这个方法中
    private fun initializeLoginUI() {
        setContentView(R.layout.activity_login)
        
        // 原来的 UI 初始化代码
        usernameEditText = findViewById(R.id.login_username)
        passwordEditText = findViewById(R.id.login_password)
        loginButton = findViewById(R.id.login_button)
        registerPrompt = findViewById(R.id.register_prompt)

        
        loginButton.setOnClickListener {
            login()
        }
        
        registerPrompt.setOnClickListener {
            startActivity(Intent(this, RegisterActivity::class.java))
        }
    }

    private fun login() {
        val username = usernameEditText.text.toString()
        val password = passwordEditText.text.toString()
        val user = User(username, password)
        login(user)
    }

    private fun login(user: User) {
        val username =user.username
        val password = user.password

        if (username.isEmpty() || password.isEmpty()) {
            Toast.makeText(this, "请输入用户名和密码", Toast.LENGTH_SHORT).show()
            return
        }

        val user = User(username, password)
        ApiClient.api.loginUser(user).enqueue(object : Callback<LoginResponse> {
            override fun onResponse(call: Call<LoginResponse>, response: Response<LoginResponse>) {
                if (response.isSuccessful) {
                    response.body()?.let { loginResponse ->
                        // 在保存新用户信息前，检查并清除旧的门店缓存
                        val sharedPreferences = getSharedPreferences("UserPrefs", MODE_PRIVATE)
                        val previousUsername = sharedPreferences.getString("username", "")
                        
                        if (previousUsername != "" && previousUsername != loginResponse.username) {
                            // 清除旧的门店缓存
                            val storeCookiesPrefs = getSharedPreferences("StoreCookies", MODE_PRIVATE)
                            storeCookiesPrefs.edit().clear().apply()
                            Log.d("LoginActivity", "检测到用户变更，已清除旧的门店缓存")
                        }
                        
                        // 保存新用户信息
                        sharedPreferences.edit().apply {
                            putString("username", loginResponse.username)
                            putString("sessionToken", loginResponse.sessionToken)
                            putString("userId", loginResponse.objectId)
                            apply()
                        }

                        Toast.makeText(this@LoginActivity, "登录成功", Toast.LENGTH_SHORT).show()
                        
                        // 先获取门店信息，等待获取完成后再跳转
                        lifecycleScope.launch {
                            try {
                                // 显示加载提示
                                Toast.makeText(this@LoginActivity, "正在获取门店信息...", Toast.LENGTH_SHORT).show()
                                
                                // 获取门店信息
                                StoreUtils.getStoreDetails(this@LoginActivity, username, true)
                                
                                // 延迟一下确保数据获取完成
                                delay(1000)
                                
                                // 跳转到主界面
                                val intent = Intent(this@LoginActivity, MainWithTabsActivity::class.java)
                                startActivity(intent)
                                finish()
                            } catch (e: Exception) {
                                Log.e("LoginActivity", "获取门店信息失败: ${e.message}")
                                Toast.makeText(this@LoginActivity, "获取门店信息失败，请重试", Toast.LENGTH_SHORT).show()
                            }
                        }
                    }
                } else {
                    Toast.makeText(this@LoginActivity, "登录失败: ${response.message()}", Toast.LENGTH_SHORT).show()
                }
            }

            override fun onFailure(call: Call<LoginResponse>, t: Throwable) {
                Toast.makeText(this@LoginActivity, "网络错误: ${t.message}", Toast.LENGTH_SHORT).show()
            }
        })
    }
}


