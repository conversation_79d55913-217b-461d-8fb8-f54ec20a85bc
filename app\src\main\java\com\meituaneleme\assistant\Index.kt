package com.meituaneleme.assistant
import com.meituaneleme.assistant.StoreCookie

object Index {
    fun parseCookies(input: String): List<StoreCookie> {
        val result = mutableListOf<StoreCookie>()

        // 按换行符或指定分隔符拆分输入字符串
        val segments = input.split(Regex("\\s+(?=\\S+】)"))

        segments.forEach { segment ->
            // 使用正则表达式匹配店铺名和 cookies 数据
            val match = Regex("^([\\u4e00-\\u9fa5\\w\\-]+】)(.+)$").find(segment)

            if (match != null) {
                val store = match.groupValues[1].replace("】", "") // 去掉 '】'
                val cookies = match.groupValues[2].trim() // 获取 cookies 内容
                result.add(StoreCookie(store, cookies))
            }
        }

        return result
    }
}