<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:padding="16dp"
    android:background="@color/light_gray">

    <Spinner
        android:id="@+id/platform_spinner"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="12dp"
        android:layout_marginBottom="16dp"
        android:background="@drawable/spinner_background"/>

    <EditText
        android:id="@+id/store_name"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="24dp"
        android:hint="门店名称"
        android:padding="12dp"
        android:background="@drawable/edit_text_background"
        android:layout_marginBottom="16dp"/>

    <Button
        android:id="@+id/add_button"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:textColor="@color/white"
        android:layout_marginTop="24dp"
        android:text="添加门店"
        android:background="@drawable/rounded_button"
        android:textSize="20sp"/>
</LinearLayout>