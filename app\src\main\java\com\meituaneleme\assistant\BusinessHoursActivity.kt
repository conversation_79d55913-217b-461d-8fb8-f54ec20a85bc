package com.meituaneleme.assistant

import android.os.Bundle
import androidx.appcompat.app.AppCompatActivity
import com.meituaneleme.assistant.fragments.BusinessHoursFragment

/**
 * 营业时间设置活动
 */
class BusinessHoursActivity : AppCompatActivity() {
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_business_hours)
        
        // 设置标题
        supportActionBar?.title = "门店营业时间设置"
        supportActionBar?.setDisplayHomeAsUpEnabled(true)
        
        // 加载营业时间设置Fragment
        if (savedInstanceState == null) {
            supportFragmentManager.beginTransaction()
                .replace(R.id.fragment_container, BusinessHoursFragment())
                .commit()
        }
    }
    
    override fun onSupportNavigateUp(): Boolean {
        onBackPressed()
        return true
    }
} 