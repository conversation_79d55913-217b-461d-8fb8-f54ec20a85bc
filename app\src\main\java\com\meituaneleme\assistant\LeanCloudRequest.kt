import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import kotlinx.serialization.decodeFromString
import kotlinx.serialization.json.Json
import okhttp3.OkHttpClient
import okhttp3.Request
import okhttp3.RequestBody
import okhttp3.MediaType.Companion.toMediaTypeOrNull
import org.json.JSONObject
import java.io.IOException

@kotlinx.serialization.Serializable
data class GeneratedTypeLiteralInterface_3(
    val endDate: String? = null,
    val endDeliveryDate: String? = null,
    val latestDeliveryDate: String? = null,
    val preSaleType: String? = null,
    val startDate: String? = null,
    val startDeliveryDate: String? = null,
    val results: List<Result> = emptyList()
)

@kotlinx.serialization.Serializable
data class Result(
    val cookies: String? = null
)

class LeanCloudRequest {
    val BASE_URL = "https://UEAVFUYyDCB3ucTQswp5G6Zk-gzGzoHsz.lc-cn-n1-shared.com/1.1/classes/"
    val APP_ID = "UEAVFUYyDCB3ucTQswp5G6Zk-gzGzoHsz"
    val APP_KEY = "AiaVPlM1DrCLpC6siQfXynBQ"
    val client = OkHttpClient()

    suspend inline fun <reified T> sendRequest(className: String, method: String, body: JSONObject? = null): T {
        val url = "$BASE_URL$className"
        val requestBody = body?.let { RequestBody.create("application/json".toMediaTypeOrNull(), it.toString()) }
        val requestBuilder = Request.Builder()
            .url(url)
            .addHeader("X-LC-Id", APP_ID)
            .addHeader("X-LC-Key", APP_KEY)
            .addHeader("Content-Type", "application/json")

        when (method) {
            "GET" -> requestBuilder.get()
            "POST" -> requestBody?.let { requestBuilder.post(it) }
            "PUT" -> requestBody?.let { requestBuilder.put(it) }
            "DELETE" -> requestBuilder.delete(requestBody)
        }

        val request = requestBuilder.build()

        return withContext(Dispatchers.IO) {
            client.newCall(request).execute().use { response ->
                if (!response.isSuccessful) throw IOException("Unexpected code $response")
                val responseBody = response.body?.string() ?: throw IOException("Empty response body")
                val json = Json { ignoreUnknownKeys = true }
                json.decodeFromString(responseBody) // Parse JSON response
            }
        }
    }

    suspend fun getCookieByName(className: String, name: String): String? {
        return try {
            val query = JSONObject().put("where", JSONObject().put("name", name))
            val response = sendRequest<GeneratedTypeLiteralInterface_3>(className, "GET", query)
            if (response.results.isNotEmpty()) {
                response.results[0].cookies
            } else {
                null
            }
        } catch (e: Exception) {
            e.printStackTrace()
            null
        }
    }
}