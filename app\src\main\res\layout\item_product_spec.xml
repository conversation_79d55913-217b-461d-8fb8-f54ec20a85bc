<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:padding="12dp">

    <TextView
        android:id="@+id/tv_spec"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:text="规格"
        android:textSize="16sp"
        android:textStyle="bold"
        android:textColor="@color/text_primary"
        android:layout_marginBottom="12dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <com.google.android.material.card.MaterialCardView
        android:id="@+id/card_price_stock"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        app:cardCornerRadius="8dp"
        app:cardElevation="0dp"
        app:strokeColor="@color/divider"
        app:strokeWidth="1dp"
        app:layout_constraintTop_toBottomOf="@id/tv_spec"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="12dp">
            
            <!-- 原价行 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                android:layout_marginBottom="12dp">

                <TextView
                    android:id="@+id/tv_original_price"
                    android:layout_width="60dp"
                    android:layout_height="wrap_content"
                    android:text="原价"
                    android:textColor="@color/text_primary"
                    android:textSize="14sp" />

                <com.google.android.material.textfield.TextInputLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    style="@style/Widget.App.TextInputLayout"
                    android:layout_marginEnd="8dp">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/et_original_price"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:inputType="numberDecimal"
                        android:paddingVertical="12dp"/>
                </com.google.android.material.textfield.TextInputLayout>

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/btn_save_original_price"
                    android:layout_width="wrap_content"
                    android:layout_height="48dp"
                    android:text="保存"
                    app:cornerRadius="8dp"
                    style="@style/Widget.App.Button"/>
            </LinearLayout>

            <!-- 折扣价行 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                android:layout_marginBottom="12dp">

                <TextView
                    android:id="@+id/tv_discount_price"
                    android:layout_width="60dp"
                    android:layout_height="wrap_content"
                    android:text="折扣价"
                    android:textColor="@color/text_primary"
                    android:textSize="14sp" />

                <com.google.android.material.textfield.TextInputLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    style="@style/Widget.App.TextInputLayout"
                    android:layout_marginEnd="8dp">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/et_discount_price"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:inputType="numberDecimal"
                        android:paddingVertical="12dp"/>
                </com.google.android.material.textfield.TextInputLayout>

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/btn_save_discount_price"
                    android:layout_width="wrap_content"
                    android:layout_height="48dp"
                    android:text="保存"
                    app:cornerRadius="8dp"
                    style="@style/Widget.App.Button"/>
            </LinearLayout>
            
            <!-- 库存行 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical">

                <TextView
                    android:id="@+id/tv_stock"
                    android:layout_width="60dp"
                    android:layout_height="wrap_content"
                    android:text="库存"
                    android:textColor="@color/text_primary"
                    android:textSize="14sp" />

                <com.google.android.material.textfield.TextInputLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    style="@style/Widget.App.TextInputLayout"
                    android:layout_marginEnd="8dp">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/et_stock"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:inputType="number"
                        android:paddingVertical="12dp"/>
                </com.google.android.material.textfield.TextInputLayout>

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/btn_save_stock"
                    android:layout_width="wrap_content"
                    android:layout_height="48dp"
                    android:text="保存"
                    app:cornerRadius="8dp"
                    style="@style/Widget.App.Button"/>
            </LinearLayout>
        </LinearLayout>
    </com.google.android.material.card.MaterialCardView>

</androidx.constraintlayout.widget.ConstraintLayout>