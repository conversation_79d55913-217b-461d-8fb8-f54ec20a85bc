# 美团饿了么助手 Android 应用

## 项目简介
这是一个基于 Android 开发的美团饿了么商家助手应用，帮助商家管理多个店铺账号和商品折扣。

## 技术架构
- 开发语言: Kotlin
- 网络请求: Retrofit2
- 数据存储: LeanCloud 后端
- UI 框架: Android ViewBinding
- 异步处理: Kotlin Coroutines

## 主要功能

### 0. 应用更新
- 自动检查更新
  - 启动时检查新版本
  - 支持强制更新
  - MD5校验保证安全
- 安全机制
  - 签名验证
    - Debug/Release 双签名支持
    - 启动时验证应用签名
    - 更新时验证APK签名
  - 防篡改保护
    - 签名混淆存储
    - 运行时签名校验
    - 非法安装检测
- 更新流程
  1. 检查新版本
  2. 显示更新提示
  3. 下载新版本
  4. 安装新版本
- 版本管理
  - 版本号管理
  - 更新说明
  - 下载地址配置

### 1. 用户管理
- 用户注册
  - 用户名密码注册
  - 设备唯一性验证（一个设备只能注册一个账号）
  - 密码强度验证（必须包含字母和数字，长度大于6位）
- 用户登录
  - 账号密码登录
  - 自动登录（保存登录状态）
  - Session 管理

### 2. 店铺管理
- 店铺列表展示
- 添加新店铺
- 店铺数量限制
- 店铺 Cookie 管理
- 多平台支持
  - 美团（稳定版）
  - 饿了么（测试版）

### 3. 折扣管理
- 查看商品折扣
  - 点击商品名称获取当前折扣信息
- 修改商品折扣
  - 支持修改现有折扣
  - 不支持添加新折扣
- 折扣操作流程
  1. 点击商品名称获取折扣
  2. 等待折扣信息加载
  3. 修改折扣数值
  4. 确认修改

### 4. 帮助支持
- 使用指南
  - 基本功能说明
  - 操作步骤指引
  - 图文示例说明
- 常见问题解答
- 平台状态说明
- 客服支持

## 配置说明
1. LeanCloud 配置 (AppConfig.kt)
   - APP_ID: UEAVFUYyDCB3ucTQswp5G6Zk-gzGzoHsz
   - APP_KEY: AiaVPlM1DrCLpC6siQfXynBQ
   - BASE_URL: https://ueavfuyy.lc-cn-n1-shared.com/1.1/

2. 更新服务器配置
   - ��新检查: LeanCloud AppVersion 类
   - 下载服务器: http://113.44.82.43:3001/downloads/
   - 文件校验: MD5

## 开发注意事项
1. 设备限制
   - 每个设备只能注册一个账号
   - 注册时会记录设备ID
   - 设备ID使用 Android ID

2. 用户限制
   - 用户名不能重复
   - 密码必须包含字母和数字
   - 密码长度大于6位

3. 店铺限制
   - 每个用户的店铺数量有上限
   - 需要验证店铺的有效性

4. 折扣操作
   - 必须先获取折扣信息
   - 仅支持修改现有折扣
   - 不支持添加新折扣

5. 版本更新
   - versionCode 用于版本比较
   - versionName 用于显示给用户
   - 强制更新时需要设置 forceUpdate
   - 更新文件需要提供 MD5 校验

## 联系支持
- 客服微信：WMSHXAD
- 工作时间：9:00-18:00

## 更新日志
### v1.0.1
- 添加应用内自动更新功能
- 支持强制更新
- 添加 MD5 文件校验
- 优化更新提示界面
- 增加应用签名验证机制
- 添加防篡改保护
- 支持 Debug/Release 双签名

### v1.0.0
- 实现基础用户管理功能
- 实现店铺管理功能
- 添加设备限制机制