import requests
import time
import json
import random
import logging
import urllib.parse
import concurrent.futures
import datetime
import os
from logging.handlers import RotatingFileHandler

# 创建logs目录(如果不存在)
os.makedirs('logs', exist_ok=True)

# 配置日志
def setup_logger():
    logger = logging.getLogger()
    logger.setLevel(logging.INFO)
    
    # 清除之前的处理器(如果有)
    if logger.handlers:
        for handler in logger.handlers:
            logger.removeHandler(handler)
    
    # 定义日志格式
    log_format = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
    
    # 控制台输出处理器
    console_handler = logging.StreamHandler()
    console_handler.setFormatter(log_format)
    logger.addHandler(console_handler)
    
    # 文件输出处理器 - 使用按日期命名的文件
    today = datetime.datetime.now().strftime('%Y-%m-%d')
    log_file = f'logs/jd_subsidy_{today}.log'
    
    # 使用RotatingFileHandler限制文件大小，最大10MB，保留5个备份
    file_handler = RotatingFileHandler(
        log_file, maxBytes=10*1024*1024, backupCount=5, encoding='utf-8'
    )
    file_handler.setFormatter(log_format)
    logger.addHandler(file_handler)
    
    return logger

# 初始化日志
logger = setup_logger()
logging.info("日志系统初始化完成，日志将同时保存到本地文件")

# 从curl命令中提取的关键信息
cookies = "qid_sid=38a7837c-5982-4ff1-89f6-3e732a86cad4-1; sdtoken=AAbEsBpEIOVjqTAKCQtvQu17a30snDjSZ114QNvxsPLJYZEITnm3JE6JxI-b-7vXRvOkmF2IsVP1FCGawJleXjjafqq9xc-N_Ai6YAeGN4IvsgSyuhXXbD4TWgc; mba_muid=1750212801034248578200.1.1750213205161; mba_sid=1.26; qid_seq=7; shshshfpa=b9685a5f-59d6-d499-deda-22e47141aa98-1750212802; shshshfpb=BApXSjbnbg_JAnvRPq5E6LWwTS5PKhxtCBhJgLy0X9xJ1OYoOLt6Hx0m83xT3N8ZBe9U; qid_evord=21; __jd_ref_cls=Babel_dev_other_government_subsidies_bind; 3AB9D23F7A4B3C9B=Y54SWZIJ2LMBQRRJF46KM4T3P7YQA3V6JFX4LLGULEOQTTINJUD3RR436HRRDESU5NWYI3QOO4FTR7VUG45QXWF6M4; 3AB9D23F7A4B3CSS=jdd03Y54SWZIJ2LMBQRRJF46KM4T3P7YQA3V6JFX4LLGULEOQTTINJUD3RR436HRRDESU5NWYI3QOO4FTR7VUG45QXWF6M4AAAAMXQDJS5VIAAAAADK7FGWF7DOMT44X; unionwsws=%7B%22devicefinger%22%3A%22eidIf27181220csevDlJSKpkTHOErnilb9F5qLw5SuArMtYC2zEXhZR1B3FIUQnmqf7ZT1Vv%2FnQlGO6%2FQ6%2FQo0JsePSHTgItxCJ5gZWQPwRag%2BNpV2tY%22%7D; __jda=122270672.1750212801034248578200.1750212801.1750212801.1750212801.1; __jdb=122270672.6.1750212801034248578200|1.1750212801; __jdv=122270672%7Ckong%7Ct_2018512525_ios_pay%7Ctuiguang%7C17502127151925019140001%7C1750212716000; pre_seq=16; cid=8; qid_fs=1750212802065; qid_ls=1750212802065; qid_ts=1750212802073; qid_uid=38a7837c-5982-4ff1-89f6-3e732a86cad4; qid_vis=1; shshshfpx=b9685a5f-59d6-d499-deda-22e47141aa98-1750212802; b_avif=1; b_dh=804; b_dpr=2; b_dw=414; b_webp=1; __jdc=122270672; pre_session=6c791b36591d9ac7a11cc916e58f48445b9a11c3|1; pt_key=app_openAAJoUiC-ADBDquOye2Q-pJR_tQ9mvAdw_1YlZLwvdQJHvY4jA4ECykjgHx3WfyUOmHGDfQswnXU; pt_pin=653815373_m; pwdt_id=653815373_m"

user_agent = "jdapp;iPhone;15.1.55;;;M/5.0;appBuild/169899;jdSupportDarkMode/0;ef/1;ep/%7B%22ciphertype%22%3A5%2C%22cipher%22%3A%7B%22ud%22%3A%22DwC3EJPsCzY1EJPuEWPtD2OnCWDtEJO2ZJU4ZtG4DNG1YtvrCJPtCm%3D%3D%22%2C%22sv%22%3A%22CJukCK%3D%3D%22%2C%22iad%22%3A%22%22%7D%2C%22ts%22%3A1750212817%2C%22hdid%22%3A%22JM9F1ywUPwflvMIpYPok0tt5k9kW4ArJEU3lfLhxBqw%3D%22%2C%22version%22%3A%221.0.3%22%2C%22appname%22%3A%22com.360buy.jdmobile%22%2C%22ridx%22%3A-1%7D;Mozilla/5.0 (iPhone; CPU iPhone OS 19_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148;supportJDSHWK/1;"

# 全局变量
SUCCESS_FLAG = False  # 用于标记是否已经抢购成功
RANDOM_DELAY_MIN = 5  # 普通时段最小随机延迟（秒）
RANDOM_DELAY_MAX = 10  # 普通时段最大随机延迟（秒）
NEAR_HOUR_DELAY_MIN = 0.5  # 接近整点时最小随机延迟（秒）
NEAR_HOUR_DELAY_MAX = 2  # 接近整点时最大随机延迟（秒）
MAX_RETRIES = 5  # 整点时刻的最大重试次数

# 构建请求头
headers = {
    "Host": "api.m.jd.com",
    "Cookie": cookies,
    "content-type": "application/x-www-form-urlencoded",
    "accept": "application/json, text/plain, */*",
    "sec-fetch-site": "same-site",
    "x-rp-client": "h5_1.0.0",
    "accept-language": "zh-CN,zh-Hans;q=0.9",
    "sec-fetch-mode": "cors",
    "origin": "https://pro.m.jd.com",
    "user-agent": user_agent,
    "x-referer-page": "https://pro.m.jd.com/mall/active/3MdZQ8N8oi7xJj4pLj7Z8SSw3ZtP/index.html",
    "referer": "https://pro.m.jd.com/mall/active/3MdZQ8N8oi7xJj4pLj7Z8SSw3ZtP/index.html?stath=48&navh=44&tttparams=D3jkOTeyJyZnMiOiIwMDAwIiwicG9zTG5nIjoiMTA0LjAzNjAyOSIsInVlbXBzIjoiMC0wLTAiLCJnTG5nIjoiMTA0LjAzNjAyOSIsImdMYXQiOiIzMC42ODk3ODkiLCJsbmciOiIwLjAwMDAwMCIsIm9yaWVudCI6InAiLCJvcyI6IjE5LjAiLCJsYnNMYXQiOiIzMC42ODk3ODkiLCJkTG5nIjoiIiwiZExhdCI6IiIsImxic0xuZyI6IjEwNC4wMzYwMjkiLCJwcnN0YXRlIjoiMCIsImdwc19hcmVhIjoiMF8wXzBfMCIsInNjYWxlIjoiMiIsImFkZHJlc3NJZCI6IjEzOTY3OTUzMTA1IiwidW5fYXJlYSI6IjIyXzE5MzBfNTA5NDZfNTcwNjIiLCJ3aWR0aCI6IjgyOCIsImxic0FyZWEiOiIyMl8xOTMwXzUwOTQ2XzU3MDYyIiwibGF0IjoiMC4wMDAwMDAiLCJtb2RlbCI6ImlQaG9uZTEyLDEiLCJjb3JuZXIiOjEsImFyZWFDb2RlIjoiMCIsInBvc0xhdCI6IjMwLjY4OTc4OS6J9",
    "sec-fetch-dest": "empty",
    "x-api-eid-token": "jdd03Y54SWZIJ2LMBQRRJF46KM4T3P7YQA3V6JFX4LLGULEOQTTINJUD3RR436HRRDESU5NWYI3QOO4FTR7VUG45QXWF6M4AAAAMXQDH7O5AAAAAAD7BMIIF2M32AHUX"
}

# 获取动态h5st值
def get_dynamic_h5st():
    # 接口所需参数
    appid = "gov-subsidy-h5"
    functionId = "bindingQualification"

    # 生成当前时间戳（毫秒）用于updateTime
    current_update_time = int(time.time() * 1000)

    # 构建动态的otherPos JSON字符串
    other_pos = {
        "code": 0,
        "message": "ok",
        "region": "中国",
        "regionid": "0",
        "province": "四川",
        "provinceid": "22",
        "city": "成都市",
        "cityid": "1930",
        "district": "金牛区",
        "districtid": "50946",
        "town": "茶店子街道",
        "townid": "57062",
        "detailaddr": "",
        "fullAddress": "四川成都市金牛区茶店子街道",
        "oversea": "0",
        "callType": "GisService",
        "srclng": 104.035929,
        "srclat": 30.689673,
        "updateTime": current_update_time,  # 使用当前时间戳
        "encryptLng": "pcVARfhoPe3P3kzlCWPccg",
        "encryptLat": "ucsSa4q10_cadb1jbZjd_A",
        "gridId": 0,
        "poi": "",
        "accuracy": 15
    }

    # 构建body JSON
    body_data = {
        "cateId": "B01",
        "cateName": "手机",
        "subCateId": "B01",
        "provinceId": 22,
        "cityId": 1930,
        "channelId": "2025_22_1930_8",
        "cateType": None,
        "qualificationRegionLevel": 1,
        "locProvinceId": 22,
        "loCityId": 1930,
        "locCountyId": 50946,
        "locTownId": 57062,
        "otherPos": json.dumps(other_pos, separators=(',', ':'), ensure_ascii=False),
        "paymentType": None,
        "clientVersion": "15.1.55",
        "sourceChannelId": 3
    }

    body = json.dumps(body_data, separators=(',', ':'), ensure_ascii=False)
    clientVersion = "15.1.55"
    client = "wh5"
    t = str(int(time.time() * 1000))
    
    # 从Cookie中提取pin值
    pin = ""
    for item in cookies.split(';'):
        if 'pt_pin=' in item:
            pin = item.strip().split('=')[1]
            break
    
    # 从user-agent中提取sua信息（第一个括号内的内容）
    sua = ""
    if "(" in user_agent and ")" in user_agent:
        start_idx = user_agent.find("(")
        end_idx = user_agent.find(")", start_idx)
        if start_idx != -1 and end_idx != -1:
            sua = user_agent[start_idx+1:end_idx]
    
    # 如果没有成功提取，使用默认值
    if not sua:
        sua = "iPhone; CPU iPhone OS 19_0 like Mac OS X"
    
    # logging.info(f"使用sua参数: {sua}")
    
    # 构建h5st API请求URL
    h5st_api_url = f"http://**************:6666/jd/h5st?appid={appid}&functionId={functionId}&body={urllib.parse.quote(body)}&clientVersion={clientVersion}&client=&t={t}&ai=1365e&pin={urllib.parse.quote(pin)}&sua={urllib.parse.quote(sua)}"
    
    try:
        response = requests.get(h5st_api_url, timeout=10)
        if response.status_code == 200:
            h5st = response.text
            
            # logging.info(f"成功获取h5st: {h5st[:30]}...")  # 只显示前30个字符
            return h5st, t

        else:
            logging.error(f"获取h5st请求失败，状态码: {response.status_code}")
    except Exception as e:
        logging.error(f"获取h5st时发生异常: {e}")
    
    return None, t

# 构建请求体数据
def build_request_data():
    # 添加随机字符串使每次请求不完全相同
    random_str = str(random.random())
    # 获取当前时间戳和动态h5st
    h5st, timestamp = get_dynamic_h5st()
    if not h5st:
        logging.error("无法获取h5st，使用请求将可能失败")
        # 即使没有h5st，也尝试继续运行
        h5st = ""

    # 生成当前时间戳（毫秒）用于updateTime
    current_update_time = int(time.time() * 1000)

    # 构建动态的otherPos JSON字符串
    other_pos = {
        "code": 0,
        "message": "ok",
        "region": "中国",
        "regionid": "0",
        "province": "四川",
        "provinceid": "22",
        "city": "成都市",
        "cityid": "1930",
        "district": "金牛区",
        "districtid": "50946",
        "town": "茶店子街道",
        "townid": "57062",
        "detailaddr": "",
        "fullAddress": "四川成都市金牛区茶店子街道",
        "oversea": "0",
        "callType": "GisService",
        "srclng": 104.035929,
        "srclat": 30.689673,
        "updateTime": current_update_time,  # 使用当前时间戳
        "encryptLng": "pcVARfhoPe3P3kzlCWPccg",
        "encryptLat": "ucsSa4q10_cadb1jbZjd_A",
        "gridId": 0,
        "poi": "",
        "accuracy": 15
    }

    # 构建body JSON
    body_data = {
        "cateId": "B01",
        "cateName": "手机",
        "subCateId": "B01",
        "provinceId": 22,
        "cityId": 1930,
        "channelId": "2025_22_1930_8",
        "cateType": None,
        "qualificationRegionLevel": 1,
        "locProvinceId": 22,
        "loCityId": 1930,
        "locCountyId": 50946,
        "locTownId": 57062,
        "otherPos": json.dumps(other_pos, separators=(',', ':'), ensure_ascii=False),
        "paymentType": None,
        "clientVersion": "15.1.55",
        "sourceChannelId": 3
    }

    # 构建请求体
    data = {
        "appid": "gov-subsidy-h5",
        "loginType": "null",
        "loginWQBiz": "",
        "functionId": "bindingQualification",
        "body": json.dumps(body_data, separators=(',', ':'), ensure_ascii=False),
        "channelId": "2025_22_1930_8",
        "t": timestamp,
        "h5st": h5st,
        "x-api-eid-token": "jdd03Y54SWZIJ2LMBQRRJF46KM4T3P7YQA3V6JFX4LLGULEOQTTINJUD3RR436HRRDESU5NWYI3QOO4FTR7VUG45QXWF6M4AAAAMXQDH7O5AAAAAAD7BMIIF2M32AHUX"
    }

    return data

# 抢购函数
def grab_subsidy():
    global SUCCESS_FLAG
    if SUCCESS_FLAG:
        return True
        
    url = "https://api.m.jd.com/client.action?functionId=bindingQualification"
    data = build_request_data()
    
    try:
        # 将超时时间延长到10秒，以应对网络波动和h5st服务器可能的延迟
        response = requests.post(url, headers=headers, data=data, timeout=10)
        # 检查请求是否成功 (例如，状态码 200)
        response.raise_for_status()
        
        # 尝试解析JSON，如果失败则说明被风控
        try:
            result = response.json()
            logging.info(f"抢购结果: {result}")
            
            # 修正成功判断逻辑：严格检查 'success' 字段是否为 True
            if result.get("success") is True:
                logging.info(f"恭喜！抢购成功!")
                SUCCESS_FLAG = True
                return True
            else:
                error_msg = result.get('message', '未知错误')
                logging.info(f"抢购失败: {error_msg}")
                return False
        except json.JSONDecodeError:
            logging.error(f"响应非JSON格式，可能参数已失效或被风控。响应内容(前500字符)如下:")
            logging.error(response.text[:500])
            return False
            
    except requests.exceptions.RequestException as e:
        logging.error(f"请求网络异常: {e}")
        return False
    except Exception as e:
        logging.error(f"发生未知异常: {e}")
        return False

# 检查是否接近整点
def is_near_hour():
    now = datetime.datetime.now()
    # 59分钟30秒以后或00分钟内都算接近整点
    return (now.minute == 59 and now.second >= 30) or (now.minute == 0 and now.second < 15)

# 检查是否整点
def is_exactly_hour():
    now = datetime.datetime.now()
    return now.minute == 0 and now.second < 10  # 整点的前10秒内

# 主函数
def main():
    global SUCCESS_FLAG
    logging.info("脚本启动，开始单线程抢购... 按 Ctrl+C 可以手动停止。")
    
    while not SUCCESS_FLAG:
        try:
            # 当前时间
            now = datetime.datetime.now()
            
            # 计算距离下一个整点的时间
            minutes_to_hour = 60 - now.minute - 1 if now.minute < 59 else 0
            seconds_to_hour = 60 - now.second if now.minute < 59 else 60 - now.second
            seconds_to_next_hour = minutes_to_hour * 60 + seconds_to_hour
            
            # 如果已经是整点时刻（00分00秒到00分10秒之间），立即进行密集尝试
            if now.minute == 0 and now.second < 10:
                logging.info("⏰ 整点时刻到，进行密集尝试！")
                for attempt in range(MAX_RETRIES):
                    if grab_subsidy():
                        logging.info("整点抢购成功，程序结束。")
                        return
                    time.sleep(NEAR_HOUR_DELAY_MIN)  # 整点时使用最小延迟
                
                # 整点密集尝试后，等待一个普通间隔
                sleep_duration = random.uniform(RANDOM_DELAY_MIN, RANDOM_DELAY_MAX)
                logging.info(f"整点密集尝试结束，等待 {sleep_duration:.2f} 秒后继续...")
                time.sleep(sleep_duration)
                continue
            
            # 如果接近整点（距离不到60秒），精确计算等待时间并准确在整点发起请求
            elif seconds_to_next_hour < 60:
                logging.info(f"接近整点，距离整点还有 {seconds_to_next_hour} 秒")
                
                # 如果距离整点非常近（小于10秒），使用精确等待
                if seconds_to_next_hour <= 10:
                    # 精确等待至整点
                    logging.info(f"精确等待 {seconds_to_next_hour} 秒到整点...")
                    time.sleep(seconds_to_next_hour)
                    
                    # 整点立即密集发送请求
                    logging.info("⏰ 整点到，立即开始密集请求！")
                    for attempt in range(MAX_RETRIES):
                        if grab_subsidy():
                            logging.info("整点抢购成功，程序结束。")
                            return
                        time.sleep(NEAR_HOUR_DELAY_MIN)  # 整点时使用最小延迟
                    
                    # 尝试结束后等待
                    sleep_duration = random.uniform(RANDOM_DELAY_MIN, RANDOM_DELAY_MAX)
                    logging.info(f"整点密集尝试结束，等待 {sleep_duration:.2f} 秒后继续...")
                    time.sleep(sleep_duration)
                    continue
                
                else:
                    # 离整点还有一段时间但小于60秒，使用较短的等待时间
                    sleep_duration = random.uniform(NEAR_HOUR_DELAY_MIN, NEAR_HOUR_DELAY_MAX)
                    # 确保等待后还剩至少5秒进入精确等待阶段
                    if seconds_to_next_hour - sleep_duration < 5:
                        sleep_duration = seconds_to_next_hour - 5
                    
                    logging.info(f"接近整点，等待 {sleep_duration:.2f} 秒后继续...")
                    time.sleep(sleep_duration)
                    continue
            
            # 普通时段，单次尝试
            if grab_subsidy():
                logging.info("常规抢购成功，程序结束。")
                return
            
            # 普通时段等待
            sleep_duration = random.uniform(RANDOM_DELAY_MIN, RANDOM_DELAY_MAX)
            logging.info(f"等待 {sleep_duration:.2f} 秒后重试... 距离下一个整点还有{seconds_to_next_hour}秒")
            time.sleep(sleep_duration)

        except KeyboardInterrupt:
            logging.info("用户手动停止脚本。")
            break
        except Exception as e:
            logging.error(f"主循环发生未知错误: {e}")
            time.sleep(2)  # 发生错误后等待2秒

if __name__ == "__main__":
    main()
