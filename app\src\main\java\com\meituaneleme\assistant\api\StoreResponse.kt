package com.meituaneleme.assistant.api

import android.os.Parcel
import android.os.Parcelable


data class StoreValidCheck(
    val platform: String,
    val storeName: String,
    var isValid: Boolean? = null
    )

data class StoreResponse(
    val owner: String,
    val platform: String,
    val storeName: String,
    val createdAt: String,
    val updatedAt: String,
    val objectId: String
) : Parcelable {
    constructor(parcel: Parcel) : this(
        parcel.readString() ?: "",
        parcel.readString() ?: "",
        parcel.readString() ?: "",
        parcel.readString() ?: "",
        parcel.readString() ?: "",
        parcel.readString() ?: ""
    )

    override fun writeToParcel(parcel: Parcel, flags: Int) {
        parcel.writeString(owner)
        parcel.writeString(platform)
        parcel.writeString(storeName)
        parcel.writeString(createdAt)
        parcel.writeString(updatedAt)
        parcel.writeString(objectId)
    }

    override fun describeContents(): Int {
        return 0
    }

    companion object CREATOR : Parcelable.Creator<StoreResponse> {
        override fun createFromParcel(parcel: Parcel): StoreResponse {
            return StoreResponse(parcel)
        }

        override fun newArray(size: Int): Array<StoreResponse?> {
            return arrayOfNulls(size)
        }
    }
}