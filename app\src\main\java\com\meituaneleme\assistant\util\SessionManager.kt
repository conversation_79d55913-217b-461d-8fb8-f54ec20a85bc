package com.meituaneleme.assistant.util

import android.content.Context
import android.content.SharedPreferences
import android.webkit.CookieManager
import android.webkit.WebView
import org.json.JSONArray
import org.json.JSONObject
import java.util.*

/**
 * 会话管理器
 * 用于管理登录会话的持久性，减少重复登录验证
 */
class SessionManager private constructor(private val context: Context) {
    
    companion object {
        private const val PREFS_NAME = "session_manager"
        private const val KEY_SESSION_DATA = "session_data"
        private const val SESSION_EXPIRY_HOURS = 24 * 7 // 7天过期
        
        @Volatile
        private var INSTANCE: SessionManager? = null
        
        fun getInstance(context: Context): SessionManager {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: SessionManager(context.applicationContext).also { INSTANCE = it }
            }
        }
    }
    
    private val prefs: SharedPreferences = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
    private val deviceFingerprintManager = DeviceFingerprintManager.getInstance(context)
    
    /**
     * 保存会话信息
     */
    fun saveSession(platform: String, storeName: String, cookies: String, additionalData: Map<String, String> = emptyMap()) {
        val sessionData = getSessionData()
        val sessionKey = "${platform}_$storeName"
        
        val sessionInfo = JSONObject().apply {
            put("platform", platform)
            put("storeName", storeName)
            put("cookies", cookies)
            put("timestamp", System.currentTimeMillis())
            put("deviceFingerprint", deviceFingerprintManager.getPlatformFingerprint(platform))
            put("userAgent", deviceFingerprintManager.getUserAgent())
            
            // 添加额外数据
            additionalData.forEach { (key, value) ->
                put(key, value)
            }
        }
        
        sessionData.put(sessionKey, sessionInfo)
        saveSessionData(sessionData)
    }
    
    /**
     * 获取会话信息
     */
    fun getSession(platform: String, storeName: String): SessionInfo? {
        val sessionData = getSessionData()
        val sessionKey = "${platform}_$storeName"
        
        return if (sessionData.has(sessionKey)) {
            val sessionInfo = sessionData.getJSONObject(sessionKey)
            val timestamp = sessionInfo.getLong("timestamp")
            
            // 检查是否过期
            if (isSessionValid(timestamp)) {
                SessionInfo(
                    platform = sessionInfo.getString("platform"),
                    storeName = sessionInfo.getString("storeName"),
                    cookies = sessionInfo.getString("cookies"),
                    timestamp = timestamp,
                    deviceFingerprint = sessionInfo.optString("deviceFingerprint"),
                    userAgent = sessionInfo.optString("userAgent"),
                    additionalData = extractAdditionalData(sessionInfo)
                )
            } else {
                // 会话过期，删除
                removeSession(platform, storeName)
                null
            }
        } else {
            null
        }
    }
    
    /**
     * 移除会话信息
     */
    fun removeSession(platform: String, storeName: String) {
        val sessionData = getSessionData()
        val sessionKey = "${platform}_$storeName"
        sessionData.remove(sessionKey)
        saveSessionData(sessionData)
    }
    
    /**
     * 清除所有会话
     */
    fun clearAllSessions() {
        prefs.edit().clear().apply()
    }
    
    /**
     * 清除过期会话
     */
    fun clearExpiredSessions() {
        val sessionData = getSessionData()
        val keysToRemove = mutableListOf<String>()
        
        sessionData.keys().forEach { key ->
            val sessionInfo = sessionData.getJSONObject(key)
            val timestamp = sessionInfo.getLong("timestamp")
            if (!isSessionValid(timestamp)) {
                keysToRemove.add(key)
            }
        }
        
        keysToRemove.forEach { key ->
            sessionData.remove(key)
        }
        
        if (keysToRemove.isNotEmpty()) {
            saveSessionData(sessionData)
        }
    }
    
    /**
     * 恢复WebView会话
     */
    fun restoreWebViewSession(webView: WebView, platform: String, storeName: String): Boolean {
        val session = getSession(platform, storeName)
        return if (session != null) {
            try {
                // 设置用户代理
                if (session.userAgent.isNotEmpty()) {
                    webView.settings.userAgentString = session.userAgent
                }
                
                // 恢复Cookies
                val cookieManager = CookieManager.getInstance()
                cookieManager.removeAllCookies(null)
                
                // 解析并设置cookies
                val url = when (platform) {
                    "美团" -> "https://waimaie.meituan.com"
                    "饿了么" -> "https://nr.ele.me"
                    else -> return false
                }
                
                session.cookies.split(";").forEach { cookie ->
                    val trimmedCookie = cookie.trim()
                    if (trimmedCookie.isNotEmpty()) {
                        cookieManager.setCookie(url, trimmedCookie)
                    }
                }
                
                cookieManager.flush()
                true
            } catch (e: Exception) {
                false
            }
        } else {
            false
        }
    }
    
    /**
     * 获取所有有效会话
     */
    fun getAllValidSessions(): List<SessionInfo> {
        val sessionData = getSessionData()
        val validSessions = mutableListOf<SessionInfo>()
        
        sessionData.keys().forEach { key ->
            val sessionInfo = sessionData.getJSONObject(key)
            val timestamp = sessionInfo.getLong("timestamp")
            
            if (isSessionValid(timestamp)) {
                validSessions.add(
                    SessionInfo(
                        platform = sessionInfo.getString("platform"),
                        storeName = sessionInfo.getString("storeName"),
                        cookies = sessionInfo.getString("cookies"),
                        timestamp = timestamp,
                        deviceFingerprint = sessionInfo.optString("deviceFingerprint"),
                        userAgent = sessionInfo.optString("userAgent"),
                        additionalData = extractAdditionalData(sessionInfo)
                    )
                )
            }
        }
        
        return validSessions
    }
    
    private fun getSessionData(): JSONObject {
        val dataString = prefs.getString(KEY_SESSION_DATA, "{}")
        return try {
            JSONObject(dataString!!)
        } catch (e: Exception) {
            JSONObject()
        }
    }
    
    private fun saveSessionData(sessionData: JSONObject) {
        prefs.edit().putString(KEY_SESSION_DATA, sessionData.toString()).apply()
    }
    
    private fun isSessionValid(timestamp: Long): Boolean {
        val currentTime = System.currentTimeMillis()
        val expiryTime = timestamp + (SESSION_EXPIRY_HOURS * 60 * 60 * 1000)
        return currentTime < expiryTime
    }
    
    private fun extractAdditionalData(sessionInfo: JSONObject): Map<String, String> {
        val additionalData = mutableMapOf<String, String>()
        val excludeKeys = setOf("platform", "storeName", "cookies", "timestamp", "deviceFingerprint", "userAgent")
        
        sessionInfo.keys().forEach { key ->
            if (!excludeKeys.contains(key)) {
                additionalData[key] = sessionInfo.optString(key)
            }
        }
        
        return additionalData
    }
}

/**
 * 会话信息数据类
 */
data class SessionInfo(
    val platform: String,
    val storeName: String,
    val cookies: String,
    val timestamp: Long,
    val deviceFingerprint: String,
    val userAgent: String,
    val additionalData: Map<String, String>
)
