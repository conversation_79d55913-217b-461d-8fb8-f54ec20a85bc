package com.meituaneleme.assistant.fragments

import android.app.AlertDialog
import android.app.TimePickerDialog
import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.CheckBox
import android.widget.Toast
import androidx.core.view.isVisible
import androidx.fragment.app.Fragment
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import com.google.android.material.chip.Chip
import com.google.android.material.dialog.MaterialAlertDialogBuilder
import com.meituaneleme.assistant.R
import com.meituaneleme.assistant.adapter.ShopCheckboxAdapter
import com.meituaneleme.assistant.api.BusinessHoursRepositoryImpl
import com.meituaneleme.assistant.api.ShopModel
import com.meituaneleme.assistant.model.BusinessHourModel
import com.meituaneleme.assistant.model.FailedDetail
import com.meituaneleme.assistant.model.PlatformAccountModel
import com.meituaneleme.assistant.security.EncryptedAccountStorageManager
import com.meituaneleme.assistant.viewmodel.BusinessHoursIntent
import com.meituaneleme.assistant.viewmodel.BusinessHoursViewModel
import com.meituaneleme.assistant.viewmodel.ShopAccountInfo
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.launch
import java.util.Calendar

/**
 * 营业时间设置Fragment
 */
class BusinessHoursFragment : Fragment() {
    
    private lateinit var viewModel: BusinessHoursViewModel
    private lateinit var shopAdapter: ShopCheckboxAdapter
    private val TAG = "BusinessHoursFragment"
    
    // 视图引用
    private lateinit var startTimeText: View
    private lateinit var endTimeText: View
    private lateinit var selectAllCheckbox: CheckBox
    private lateinit var updateButton: View
    private lateinit var progressBar: View
    
    // 当前设置的时间
    private var startHour = 8
    private var startMinute = 30
    private var endHour = 21
    private var endMinute = 30
    
    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        return inflater.inflate(R.layout.fragment_business_hours, container, false)
    }
    
    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        Log.d(TAG, "onViewCreated: 初始化视图")
        
        // 初始化视图引用
        startTimeText = view.findViewById(R.id.start_time_text)
        endTimeText = view.findViewById(R.id.end_time_text)
        selectAllCheckbox = view.findViewById(R.id.select_all_checkbox)
        updateButton = view.findViewById(R.id.update_button)
        progressBar = view.findViewById(R.id.progress_bar)
        
        // 初始化平台选择Chips
        val meituanChip = view.findViewById<Chip>(R.id.meituan_chip)
        val elemeChip = view.findViewById<Chip>(R.id.eleme_chip)
        
        // 初始化RecyclerView
        val recyclerView = view.findViewById<androidx.recyclerview.widget.RecyclerView>(R.id.shops_recycler_view)
        recyclerView.layoutManager = LinearLayoutManager(requireContext())
        
        // 初始化适配器
        shopAdapter = ShopCheckboxAdapter { shop, isChecked ->
            Log.d(TAG, "门店选择变更: ${shop.name}, 选中状态: $isChecked")
            viewModel.handleIntent(BusinessHoursIntent.SelectShop(shop, isChecked))
            updateSelectAllCheckboxState()
        }
        recyclerView.adapter = shopAdapter
        
        // 创建ViewModel
        val accountManager = EncryptedAccountStorageManager(requireContext())
        val repository = BusinessHoursRepositoryImpl(requireContext(), accountManager)
        viewModel = ViewModelProvider(
            this,
            BusinessHoursViewModelFactory(repository, accountManager)
        )[BusinessHoursViewModel::class.java]
        
        // 设置时间选择器
        setupTimeSelectors()
        
        // 设置平台选择
        setupPlatformSelectors(meituanChip, elemeChip)
        
        // 设置全选功能
        setupSelectAll()
        
        // 设置更新按钮
        setupUpdateButton()
        
        // 观察ViewModel状态
        observeViewModelState()
    }
    
    private fun setupTimeSelectors() {
        // 设置开始时间选择
        startTimeText.setOnClickListener {
            showTimePickerDialog(startHour, startMinute) { hour, minute ->
                startHour = hour
                startMinute = minute
                (startTimeText as android.widget.TextView).text = String.format("%02d:%02d", hour, minute)
                updateBusinessHoursModel()
            }
        }
        
        // 设置结束时间选择
        endTimeText.setOnClickListener {
            showTimePickerDialog(endHour, endMinute) { hour, minute ->
                endHour = hour
                endMinute = minute
                (endTimeText as android.widget.TextView).text = String.format("%02d:%02d", hour, minute)
                updateBusinessHoursModel()
            }
        }
        
        // 初始化营业时间模型
        updateBusinessHoursModel()
    }
    
    private fun setupPlatformSelectors(meituanChip: Chip, elemeChip: Chip) {
        val platformChangeListener = { buttonView: android.widget.CompoundButton, isChecked: Boolean ->
            val selectedPlatforms = mutableListOf<String>()
            if (meituanChip.isChecked) selectedPlatforms.add("美团")
            if (elemeChip.isChecked) selectedPlatforms.add("饿了么")
            
            Log.d(TAG, "平台选择变更: 美团=${meituanChip.isChecked}, 饿了么=${elemeChip.isChecked}")
            
            selectedPlatforms.forEach { platform ->
                viewModel.handleIntent(BusinessHoursIntent.SelectPlatform(platform, true))
            }
            
            val unselectedPlatforms = listOf("美团", "饿了么").filter { !selectedPlatforms.contains(it) }
            unselectedPlatforms.forEach { platform ->
                viewModel.handleIntent(BusinessHoursIntent.SelectPlatform(platform, false))
            }
        }
        
        meituanChip.setOnCheckedChangeListener(platformChangeListener)
        elemeChip.setOnCheckedChangeListener(platformChangeListener)
    }
    
    private fun setupSelectAll() {
        selectAllCheckbox.setOnCheckedChangeListener { _, isChecked ->
            Log.d(TAG, "全选状态变更: $isChecked")
            viewModel.handleIntent(BusinessHoursIntent.SelectAllShops(isChecked))
            shopAdapter.setAllSelected(isChecked)
        }
    }
    
    private fun setupUpdateButton() {
        updateButton.setOnClickListener {
            Log.d(TAG, "点击批量更新按钮")
            viewModel.handleIntent(BusinessHoursIntent.UpdateBusinessHours)
        }
    }
    
    private fun observeViewModelState() {
        lifecycleScope.launch {
            viewModel.state.collectLatest { state ->
                // 更新加载状态
                progressBar.isVisible = state.isLoading
                
                // 更新门店列表
                if (state.allShops.isNotEmpty()) {
                    Log.d(TAG, "更新门店列表: 总数=${state.allShops.size}, 选中=${state.selectedShops.size}")
                    shopAdapter.submitList(state.allShops)
                    shopAdapter.setSelectedShops(state.selectedShops)
                    updateSelectAllCheckboxState()
                }
                
                // 处理错误
                state.error?.let { error ->
                    Log.e(TAG, "显示错误: $error")
                    showErrorDialog(error)
                    viewModel.handleIntent(BusinessHoursIntent.DismissError)
                }
                
                // 处理账号设置需求 - 只在用户点击了更新按钮后才显示账号设置对话框
                if (state.needAccountSetup && state.missingAccounts.isNotEmpty() && state.updateResult == null) {
                    // 获取当前选中的门店中是否有美团平台的门店
                    val hasMeituanShops = state.selectedShops.any { it.platform == "美团" }
                    
                    // 只处理美团平台的账号设置需求，并且确保当前选中的门店包含美团门店
                    val missingAccount = state.missingAccounts.first()
                    if (missingAccount.platform == "美团" && hasMeituanShops) {
                        Log.d(TAG, "显示账号设置对话框: 平台=${missingAccount.platform}, 门店=${missingAccount.shopName}")
                        showAccountSetupDialog(missingAccount)
                    } else {
                        Log.d(TAG, "跳过账号设置: 非美团平台或未选择美团门店")
                        // 清除不需要的账号设置状态
                        viewModel.handleIntent(BusinessHoursIntent.ClearMissingAccounts)
                    }
                }
                
                // 处理更新结果
                state.updateResult?.let { result ->
                    Log.d(TAG, "显示更新结果: 成功=${result.successCount}, 失败=${result.failedCount}")
                    showResultDialog(result.successCount, result.failedCount, result.failedDetails)
                }
            }
        }
    }
    
    private fun updateBusinessHoursModel() {
        val businessHours = BusinessHourModel(
            startHour = startHour,
            startMinute = startMinute,
            endHour = endHour,
            endMinute = endMinute
        )
        
        Log.d(TAG, "更新营业时间模型: ${businessHours.toTimeString()}")
        viewModel.handleIntent(BusinessHoursIntent.SetBusinessHours(businessHours))
    }
    
    private fun showTimePickerDialog(hour: Int, minute: Int, callback: (Int, Int) -> Unit) {
        val timePickerDialog = TimePickerDialog(
            requireContext(),
            { _, selectedHour, selectedMinute ->
                // 将分钟调整为5的倍数
                val adjustedMinute = (selectedMinute / 5) * 5
                callback(selectedHour, adjustedMinute)
            },
            hour,
            minute,
            true
        )
        
        // 设置标题
        timePickerDialog.setTitle("选择时间 (分钟将调整为5的倍数)")
        timePickerDialog.show()
    }
    
    private fun showAccountSetupDialog(accountInfo: ShopAccountInfo) {
        val dialogView = LayoutInflater.from(requireContext()).inflate(R.layout.dialog_account_setup, null)
        
        // 确保显示正确的平台和门店名称
        Log.d(TAG, "显示账号设置对话框: 平台=${accountInfo.platform}, 门店=${accountInfo.shopName}, ID=${accountInfo.shopId}")
        
        // 设置门店信息
        dialogView.findViewById<android.widget.TextView>(R.id.platform_name_text).text = "平台: ${accountInfo.platform}"
        dialogView.findViewById<android.widget.TextView>(R.id.shop_name_text).text = "门店: ${accountInfo.shopName}"
        
        // 获取输入框
        val usernameEdit = dialogView.findViewById<com.google.android.material.textfield.TextInputEditText>(R.id.username_edit)
        val passwordEdit = dialogView.findViewById<com.google.android.material.textfield.TextInputEditText>(R.id.password_edit)
        
        // 设置标题
        val titleText = dialogView.findViewById<android.widget.TextView>(R.id.title_text)
        titleText.text = "设置${accountInfo.platform}账号"
        
        // 更新提示信息
        val descriptionText = dialogView.findViewById<android.widget.TextView>(R.id.description_text)
        descriptionText.text = "请输入${accountInfo.platform}平台「${accountInfo.shopName}」的账号密码"
        
        val dialog = MaterialAlertDialogBuilder(requireContext())
            .setView(dialogView)
            .setCancelable(false)
            .create()
        
        // 设置按钮点击事件
        dialogView.findViewById<View>(R.id.cancel_button).setOnClickListener {
            Log.d(TAG, "取消设置账号")
            // 通知ViewModel用户已取消账号设置，避免再次弹出
            viewModel.handleIntent(BusinessHoursIntent.DismissError)
            viewModel.handleIntent(BusinessHoursIntent.ClearMissingAccounts)
            dialog.dismiss()
        }
        
        dialogView.findViewById<View>(R.id.save_button).setOnClickListener {
            val username = usernameEdit.text.toString().trim()
            val password = passwordEdit.text.toString().trim()
            
            if (username.isEmpty() || password.isEmpty()) {
                Toast.makeText(requireContext(), "请输入用户名和密码", Toast.LENGTH_SHORT).show()
                return@setOnClickListener
            }
            
            Log.d(TAG, "更新账号信息: 平台=${accountInfo.platform}, 门店ID=${accountInfo.shopId}")
            // 使用UpdateAccount意图而不是SaveAccount
            viewModel.handleIntent(BusinessHoursIntent.UpdateAccount(
                platform = accountInfo.platform,
                shopId = accountInfo.shopId,
                username = username,
                password = password
            ))
            dialog.dismiss()
        }
        
        dialog.show()
    }
    
    private fun showErrorDialog(error: String) {
        AlertDialog.Builder(requireContext())
            .setTitle("操作失败")
            .setMessage(error)
            .setPositiveButton("确定", null)
            .show()
    }

    private fun showResultDialog(successCount: Int, failedCount: Int, failedDetails: List<FailedDetail>) {
        val message = StringBuilder()
            .append("成功更新: $successCount 个门店\n")
            .append("失败: $failedCount 个门店\n")

        if (failedCount > 0) {
            message.append("\n失败门店详情:\n")
            failedDetails.forEachIndexed { index, detail ->
                message.append("${index + 1}. ${detail.shopName} (${detail.platform})\n")
                message.append("   失败原因: ${detail.reason}\n\n")
            }
        }

        Log.d(TAG, "显示更新结果对话框: 成功=$successCount, 失败=$failedCount")

        AlertDialog.Builder(requireContext())
            .setTitle("更新结果")
            .setMessage(message.toString())
            .setPositiveButton("确定") { _, _ ->
                // 清除更新结果状态，避免重复显示
                viewModel.handleIntent(BusinessHoursIntent.ClearUpdateResult)
            }
            .show()
    }
    
    private fun updateSelectAllCheckboxState() {
        selectAllCheckbox.isChecked = shopAdapter.isAllSelected()
    }
    
    /**
     * ViewModel工厂
     */
    class BusinessHoursViewModelFactory(
        private val repository: BusinessHoursRepositoryImpl,
        private val accountManager: EncryptedAccountStorageManager
    ) : ViewModelProvider.Factory {
        override fun <T : androidx.lifecycle.ViewModel> create(modelClass: Class<T>): T {
            if (modelClass.isAssignableFrom(BusinessHoursViewModel::class.java)) {
                @Suppress("UNCHECKED_CAST")
                return BusinessHoursViewModel(repository, accountManager) as T
            }
            throw IllegalArgumentException("Unknown ViewModel class")
        }
    }
} 