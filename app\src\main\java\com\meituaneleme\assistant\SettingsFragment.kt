package com.meituaneleme.assistant

import android.os.Bundle
import androidx.fragment.app.Fragment
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Button
import android.widget.Toast
import android.app.AlertDialog
import android.content.Context
import android.content.Intent

class SettingsFragment : Fragment() {
    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        val view = inflater.inflate(R.layout.fragment_settings, container, false)
        
        view.findViewById<Button>(R.id.logout_button).setOnClickListener {
            // 显示确认对话框
            AlertDialog.Builder(requireContext())
                .setTitle("确认注销")
                .setMessage("确定要注销账号吗？")
                .setPositiveButton("确定") { _, _ ->
                    logout()
                }
                .setNegativeButton("取消", null)
                .show()
        }
        
        // 添加营业时间设置入口
        view.findViewById<View>(R.id.business_hours_item).setOnClickListener {
            startActivity(Intent(requireContext(), BusinessHoursActivity::class.java))
        }
        
        // 添加帮助指南入口
        view.findViewById<View>(R.id.help_guide_item).setOnClickListener {
            startActivity(Intent(requireContext(), HelpGuideActivity::class.java))
        }
        
        return view
    }
    
    private fun logout() {
        // 清除用户信息
        val userPrefs = requireContext().getSharedPreferences("UserPrefs", Context.MODE_PRIVATE)
        userPrefs.edit().clear().apply()
        
        // 清除门店缓存
        val storeCookiesPrefs = requireContext().getSharedPreferences("StoreCookies", Context.MODE_PRIVATE)
        storeCookiesPrefs.edit().clear().apply()
        
        // 清除其他可能的缓存数据
        requireContext().cacheDir.deleteRecursively()
        
        // 显示提示
        Toast.makeText(requireContext(), "已注销", Toast.LENGTH_SHORT).show()
        
        // 跳转到登录页面
        val intent = Intent(requireContext(), LoginActivity::class.java).apply {
            flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK
        }
        startActivity(intent)
        requireActivity().finish()
    }
} 