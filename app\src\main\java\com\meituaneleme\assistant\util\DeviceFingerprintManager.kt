package com.meituaneleme.assistant.util

import android.content.Context
import android.content.SharedPreferences
import android.os.Build
import android.provider.Settings
import android.webkit.WebSettings
import android.webkit.WebView
import java.util.*

/**
 * 设备指纹管理器
 * 用于生成和管理设备指纹信息，减少平台登录时的验证码验证
 */
class DeviceFingerprintManager private constructor(private val context: Context) {
    
    companion object {
        private const val PREFS_NAME = "device_fingerprint"
        private const val KEY_DEVICE_ID = "device_id"
        private const val KEY_USER_AGENT = "user_agent"
        private const val KEY_SCREEN_RESOLUTION = "screen_resolution"
        private const val KEY_TIMEZONE = "timezone"
        private const val KEY_LANGUAGE = "language"
        private const val KEY_PLATFORM_FINGERPRINT = "platform_fingerprint"
        
        @Volatile
        private var INSTANCE: DeviceFingerprintManager? = null
        
        fun getInstance(context: Context): DeviceFingerprintManager {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: DeviceFingerprintManager(context.applicationContext).also { INSTANCE = it }
            }
        }
    }
    
    private val prefs: SharedPreferences = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
    
    /**
     * 获取设备ID
     */
    fun getDeviceId(): String {
        var deviceId = prefs.getString(KEY_DEVICE_ID, null)
        if (deviceId == null) {
            deviceId = generateDeviceId()
            prefs.edit().putString(KEY_DEVICE_ID, deviceId).apply()
        }
        return deviceId ?: generateDeviceId()
    }
    
    /**
     * 获取用户代理字符串
     */
    fun getUserAgent(): String {
        var userAgent = prefs.getString(KEY_USER_AGENT, null)
        if (userAgent == null) {
            userAgent = generateUserAgent()
            prefs.edit().putString(KEY_USER_AGENT, userAgent).apply()
        }
        return userAgent ?: generateUserAgent()
    }
    
    /**
     * 获取屏幕分辨率
     */
    fun getScreenResolution(): String {
        var resolution = prefs.getString(KEY_SCREEN_RESOLUTION, null)
        if (resolution == null) {
            resolution = generateScreenResolution()
            prefs.edit().putString(KEY_SCREEN_RESOLUTION, resolution).apply()
        }
        return resolution ?: generateScreenResolution()
    }
    
    /**
     * 获取时区信息
     */
    fun getTimezone(): String {
        var timezone = prefs.getString(KEY_TIMEZONE, null)
        if (timezone == null) {
            timezone = TimeZone.getDefault().id
            prefs.edit().putString(KEY_TIMEZONE, timezone).apply()
        }
        return timezone ?: TimeZone.getDefault().id
    }
    
    /**
     * 获取语言信息
     */
    fun getLanguage(): String {
        var language = prefs.getString(KEY_LANGUAGE, null)
        if (language == null) {
            language = Locale.getDefault().toString()
            prefs.edit().putString(KEY_LANGUAGE, language).apply()
        }
        return language ?: Locale.getDefault().toString()
    }
    
    /**
     * 获取平台特定的设备指纹
     */
    fun getPlatformFingerprint(platform: String): String {
        val key = "${KEY_PLATFORM_FINGERPRINT}_$platform"
        var fingerprint = prefs.getString(key, null)
        if (fingerprint == null) {
            fingerprint = generatePlatformFingerprint(platform)
            prefs.edit().putString(key, fingerprint).apply()
        }
        return fingerprint ?: generatePlatformFingerprint(platform)
    }
    
    /**
     * 配置WebView的设备指纹
     */
    fun configureWebView(webView: WebView, platform: String) {
        val settings = webView.settings
        
        // 设置用户代理
        settings.userAgentString = getUserAgent()
        
        // 启用必要的功能
        settings.javaScriptEnabled = true
        settings.domStorageEnabled = true
        settings.databaseEnabled = true
        settings.cacheMode = WebSettings.LOAD_DEFAULT
        
        // 设置缩放
        settings.setSupportZoom(true)
        settings.builtInZoomControls = true
        settings.displayZoomControls = false
        
        // 设置其他属性以保持一致性
        settings.allowFileAccess = false
        settings.allowContentAccess = false
        settings.allowFileAccessFromFileURLs = false
        settings.allowUniversalAccessFromFileURLs = false
        
        // 平台特定配置
        when (platform) {
            "美团" -> configureMeituanWebView(settings)
            "饿了么" -> configureElemeWebView(settings)
        }
    }
    
    /**
     * 生成设备ID
     */
    private fun generateDeviceId(): String {
        return try {
            Settings.Secure.getString(context.contentResolver, Settings.Secure.ANDROID_ID)
        } catch (e: Exception) {
            UUID.randomUUID().toString()
        }
    }
    
    /**
     * 生成用户代理字符串
     */
    private fun generateUserAgent(): String {
        val webView = WebView(context)
        val defaultUserAgent = webView.settings.userAgentString
        webView.destroy()
        
        // 自定义用户代理，模拟常见的桌面浏览器
        return "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/122.0.6261.95 Safari/537.36"
    }
    
    /**
     * 生成屏幕分辨率
     */
    private fun generateScreenResolution(): String {
        val displayMetrics = context.resources.displayMetrics
        return "${displayMetrics.widthPixels}x${displayMetrics.heightPixels}"
    }
    
    /**
     * 生成平台特定的设备指纹
     */
    private fun generatePlatformFingerprint(platform: String): String {
        val deviceId = getDeviceId()
        val userAgent = getUserAgent()
        val resolution = getScreenResolution()
        val timezone = getTimezone()
        val language = getLanguage()
        
        return when (platform) {
            "美团" -> "MT_${deviceId.take(8)}_${resolution}_${timezone.hashCode()}"
            "饿了么" -> "ELE_${deviceId.take(8)}_${language}_${userAgent.hashCode()}"
            else -> "${platform}_${deviceId.take(8)}_${System.currentTimeMillis()}"
        }
    }
    
    /**
     * 配置美团WebView
     */
    private fun configureMeituanWebView(settings: WebSettings) {
        // 美团特定配置
        settings.mixedContentMode = WebSettings.MIXED_CONTENT_ALWAYS_ALLOW
    }
    
    /**
     * 配置饿了么WebView
     */
    private fun configureElemeWebView(settings: WebSettings) {
        // 饿了么特定配置
        settings.mixedContentMode = WebSettings.MIXED_CONTENT_COMPATIBILITY_MODE
    }
    
    /**
     * 清除设备指纹（用于重置）
     */
    fun clearFingerprint() {
        prefs.edit().clear().apply()
    }
    
    /**
     * 获取完整的设备指纹信息
     */
    fun getFullFingerprint(): Map<String, String> {
        return mapOf(
            "deviceId" to getDeviceId(),
            "userAgent" to getUserAgent(),
            "screenResolution" to getScreenResolution(),
            "timezone" to getTimezone(),
            "language" to getLanguage(),
            "androidVersion" to Build.VERSION.RELEASE,
            "deviceModel" to Build.MODEL,
            "deviceBrand" to Build.BRAND
        )
    }
}
