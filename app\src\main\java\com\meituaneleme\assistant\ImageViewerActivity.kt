package com.meituaneleme.assistant

import android.Manifest
import android.content.ContentValues
import android.content.pm.PackageManager
import android.graphics.Bitmap
import android.graphics.Color
import android.graphics.drawable.Drawable
import android.os.Build
import android.os.Bundle
import android.os.Environment
import android.provider.MediaStore
import android.util.Log
import android.view.Menu
import android.view.MenuItem
import android.view.View
import android.widget.ProgressBar
import android.widget.Toast
import androidx.appcompat.app.AppCompatActivity
import androidx.appcompat.widget.Toolbar
import androidx.core.app.ActivityCompat
import androidx.core.content.ContextCompat
import androidx.lifecycle.lifecycleScope
import com.bumptech.glide.Glide
import com.bumptech.glide.load.DataSource
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.bumptech.glide.load.engine.GlideException
import com.bumptech.glide.request.RequestListener
import com.bumptech.glide.request.RequestOptions
import com.bumptech.glide.request.target.Target
import com.github.chrisbanes.photoview.PhotoView
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.io.File
import java.io.FileOutputStream

class ImageViewerActivity : AppCompatActivity() {
    private lateinit var photoView: PhotoView
    private lateinit var progressBar: ProgressBar
    private lateinit var toolbar: Toolbar
    private var imageUrl: String? = null
    
    companion object {
        private const val PERMISSION_REQUEST_CODE = 100
        private const val MIME_TYPE = "image/jpeg"
        private const val COMPRESS_QUALITY = 100
        private const val TAG = "ImageViewer"
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_image_viewer)

        setupToolbar()
        initializeViews()
        loadImage()
    }

    private fun setupToolbar() {
        toolbar = findViewById(R.id.toolbar)
        setSupportActionBar(toolbar)
        supportActionBar?.apply {
            setDisplayHomeAsUpEnabled(true)
            setHomeAsUpIndicator(R.drawable.ic_arrow_back)
            title = getString(R.string.product_image)
        }
        toolbar.apply {
            setBackgroundColor(Color.BLACK)
            setTitleTextColor(Color.WHITE)
            setNavigationOnClickListener { finish() }
        }
    }

    private fun initializeViews() {
        photoView = findViewById(R.id.photo_view)
        progressBar = findViewById(R.id.progress_bar)
        imageUrl = intent.getStringExtra("image_url")?.replace("http://", "https://")
            ?.also { url -> Log.d(TAG, "Image URL: $url") }

        photoView.setOnLongClickListener {
            showSaveDialog()
            true
        }
    }

    private fun loadImage() {
        progressBar.visibility = View.VISIBLE
        Glide.with(this)
            .load(imageUrl)
            .apply(RequestOptions().apply {
                diskCacheStrategy(DiskCacheStrategy.ALL)
                error(R.drawable.img_erro)
                placeholder(R.drawable.zhanwei)
                fitCenter()
                override(Target.SIZE_ORIGINAL)
            })
            .listener(createGlideRequestListener())
            .into(photoView)
    }

    private fun createGlideRequestListener() = object : RequestListener<Drawable> {
        override fun onLoadFailed(
            e: GlideException?,
            model: Any?,
            target: Target<Drawable>?,
            isFirstResource: Boolean
        ): Boolean {
            progressBar.visibility = View.GONE
            e?.let {
                Log.e(TAG, "Image load failed: ${it.message}")
                it.logRootCauses(TAG)
                showToast("图片加载失败: ${it.message}")
            }
            return false
        }

        override fun onResourceReady(
            resource: Drawable?,
            model: Any?,
            target: Target<Drawable>?,
            dataSource: DataSource?,
            isFirstResource: Boolean
        ): Boolean {
            progressBar.visibility = View.GONE
            Log.d(TAG, "Image loaded successfully")
            return false
        }
    }

    private fun showToast(message: String) {
        Toast.makeText(this, message, Toast.LENGTH_SHORT).show()
    }

    override fun onCreateOptionsMenu(menu: Menu): Boolean {
        menuInflater.inflate(R.menu.menu_image_viewer, menu)
        return true
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        return when (item.itemId) {
            android.R.id.home -> {
                finish()
                true
            }
            R.id.action_save -> {
                checkPermissionAndSaveImage()
                true
            }
            else -> super.onOptionsItemSelected(item)
        }
    }

    private fun checkPermissionAndSaveImage() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
            // Android 10及以上使用MediaStore API
            saveImageToMediaStore()
        } else {
            // Android 9及以下需要存储权限
            if (checkPermission()) {
                saveImageToExternalStorage()
            } else {
                requestPermission()
            }
        }
    }

    private fun checkPermission(): Boolean {
        return ContextCompat.checkSelfPermission(
            this,
            Manifest.permission.WRITE_EXTERNAL_STORAGE
        ) == PackageManager.PERMISSION_GRANTED
    }

    private fun requestPermission() {
        ActivityCompat.requestPermissions(
            this,
            arrayOf(Manifest.permission.WRITE_EXTERNAL_STORAGE),
            PERMISSION_REQUEST_CODE
        )
    }

    override fun onRequestPermissionsResult(
        requestCode: Int,
        permissions: Array<out String>,
        grantResults: IntArray
    ) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults)
        when (requestCode) {
            PERMISSION_REQUEST_CODE -> {
                if (grantResults.isNotEmpty() && grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                    saveImageToExternalStorage()
                } else {
                    Toast.makeText(this, "需要存储权限才能保存图片", Toast.LENGTH_SHORT).show()
                }
            }
        }
    }

    private fun saveImageToMediaStore() {
        lifecycleScope.launch(Dispatchers.IO) {
            try {
                val bitmap = getBitmapFromUrl()
                saveBitmapToMediaStore(bitmap)
            } catch (e: Exception) {
                handleSaveError(e)
            }
        }
    }

    private suspend fun getBitmapFromUrl(): Bitmap {
        return Glide.with(this@ImageViewerActivity)
            .asBitmap()
            .load(imageUrl)
            .submit()
            .get()
    }

    private suspend fun saveBitmapToMediaStore(bitmap: Bitmap) {
        val filename = "IMG_${System.currentTimeMillis()}.jpg"
        val contentValues = ContentValues().apply {
            put(MediaStore.MediaColumns.DISPLAY_NAME, filename)
            put(MediaStore.MediaColumns.MIME_TYPE, MIME_TYPE)
            put(MediaStore.MediaColumns.RELATIVE_PATH, Environment.DIRECTORY_PICTURES)
        }

        val uri = contentResolver.insert(MediaStore.Images.Media.EXTERNAL_CONTENT_URI, contentValues)
        uri?.let {
            contentResolver.openOutputStream(it)?.use { os ->
                bitmap.compress(Bitmap.CompressFormat.JPEG, COMPRESS_QUALITY, os)
            }
            showSaveSuccess()
        }
    }

    private suspend fun handleSaveError(e: Exception) {
        withContext(Dispatchers.Main) {
            Toast.makeText(this@ImageViewerActivity, "保存失败: ${e.message}", Toast.LENGTH_SHORT).show()
        }
    }

    private suspend fun showSaveSuccess() {
        withContext(Dispatchers.Main) {
            Toast.makeText(this@ImageViewerActivity, "图片已保存到相册", Toast.LENGTH_SHORT).show()
        }
    }

    private fun saveImageToExternalStorage() {
        lifecycleScope.launch(Dispatchers.IO) {
            try {
                val bitmap = getBitmapFromUrl()
                saveBitmapToStorage(bitmap)
            } catch (e: Exception) {
                handleSaveError(e)
            }
        }
    }

    private suspend fun saveBitmapToStorage(bitmap: Bitmap) {
        val filename = "IMG_${System.currentTimeMillis()}.jpg"
        val directory = Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_PICTURES)
        val file = File(directory, filename)
        
        FileOutputStream(file).use { out ->
            bitmap.compress(Bitmap.CompressFormat.JPEG, COMPRESS_QUALITY, out)
        }
        showSaveSuccess()
    }

    private fun showSaveDialog() {
        androidx.appcompat.app.AlertDialog.Builder(this)
            .setTitle("保存图片")
            .setMessage("是否将图片保存到相册？")
            .setPositiveButton("保存") { _, _ ->
                checkPermissionAndSaveImage()
            }
            .setNegativeButton("取消", null)
            .show()
    }
} 