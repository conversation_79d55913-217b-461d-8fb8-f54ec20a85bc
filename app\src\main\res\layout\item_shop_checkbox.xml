<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginBottom="8dp"
    app:cardCornerRadius="4dp"
    app:cardElevation="1dp"
    app:strokeColor="#DDDDDD"
    app:strokeWidth="1dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:padding="12dp">

        <ImageView
            android:id="@+id/platform_icon"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:contentDescription="平台图标" />

        <TextView
            android:id="@+id/shop_name_text"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="12dp"
            android:layout_weight="1"
            android:ellipsize="end"
            android:maxLines="1"
            android:text="门店名称"
            android:textColor="@color/black"
            android:textSize="14sp" />

        <com.google.android.material.checkbox.MaterialCheckBox
            android:id="@+id/shop_checkbox"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:checked="true" />

    </LinearLayout>

</com.google.android.material.card.MaterialCardView> 