<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background_light"
    tools:context=".MainActivity">

    <!-- AppBarLayout包含可折叠部分 -->
    <com.google.android.material.appbar.AppBarLayout
        android:id="@+id/appbar_layout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/background_light"
        app:elevation="0dp">

        <!-- 搜索历史容器 -->
        <androidx.cardview.widget.CardView
            android:id="@+id/search_history_container"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:cardElevation="0dp"
            app:cardBackgroundColor="@color/background_light"
            app:layout_scrollFlags="scroll|enterAlways">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="8dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center_vertical"
                    android:layout_marginBottom="4dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="搜索历史"
                        android:textColor="@color/text_primary"
                        android:textSize="13sp"
                        android:textStyle="bold" />

                    <View
                        android:layout_width="0dp"
                        android:layout_height="1dp"
                        android:layout_weight="1" />

                    <TextView
                        android:id="@+id/tv_clear_history"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="清空"
                        android:textColor="@color/colorPrimary"
                        android:textSize="13sp"
                        android:padding="4dp" />
                </LinearLayout>

                <HorizontalScrollView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:scrollbars="none"
                    android:layout_marginTop="4dp"
                    android:paddingBottom="2dp">

                    <com.google.android.material.chip.ChipGroup
                        android:id="@+id/chip_group_search_history"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        app:singleLine="true" />
                </HorizontalScrollView>
            </LinearLayout>
        </androidx.cardview.widget.CardView>
    </com.google.android.material.appbar.AppBarLayout>

    <!-- 商品列表 -->
    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/recycler_view"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:clipToPadding="false"
        android:paddingTop="2dp"
        android:paddingBottom="56dp"
        app:layout_behavior="@string/appbar_scrolling_view_behavior"/>

    <!-- 灰色遮罩层 -->
    <FrameLayout
        android:id="@+id/loading_overlay"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="#80000000"
        android:visibility="gone">

        <androidx.cardview.widget.CardView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            app:cardCornerRadius="8dp"
            app:cardElevation="1dp">
            
            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:gravity="center"
                android:padding="20dp">
                
                <ProgressBar
                    android:layout_width="48dp"
                    android:layout_height="48dp"
                    android:indeterminateTint="@color/colorPrimary"/>

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="搜索中..."
                    android:textColor="@color/text_primary"
                    android:textSize="16sp"
                    android:layout_marginTop="12dp"/>
            </LinearLayout>
        </androidx.cardview.widget.CardView>
</FrameLayout>

    <!-- 底部搜索栏 -->
    <androidx.cardview.widget.CardView
        android:id="@+id/bottom_search_card"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:cardElevation="1dp"
        android:layout_gravity="bottom">

        <LinearLayout
            android:id="@+id/bottom_search_bar"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:background="@color/card_background"
            android:gravity="center_vertical"
            android:padding="6dp">

            <com.google.android.material.textfield.TextInputLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                app:boxCornerRadiusBottomEnd="10dp"
                app:boxCornerRadiusBottomStart="10dp"
                app:boxCornerRadiusTopEnd="10dp"
                app:boxCornerRadiusTopStart="10dp"
                app:startIconDrawable="@drawable/ic_search"
                app:startIconTint="@color/colorPrimary"
                app:endIconMode="clear_text"
                app:hintEnabled="false"
                style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox">

                <com.google.android.material.textfield.TextInputEditText
            android:id="@+id/et_search"
                    android:layout_width="match_parent"
                    android:layout_height="40dp"
            android:hint="请输入商品名称搜索"
                    android:textSize="14sp"
                    android:imeOptions="actionSearch"
                    android:inputType="text"
                    android:maxLines="1"
                    android:paddingTop="8dp"
                    android:paddingBottom="8dp"
                    android:includeFontPadding="true"
                    android:gravity="center_vertical" />
            </com.google.android.material.textfield.TextInputLayout>

            <com.google.android.material.button.MaterialButton
            android:id="@+id/btn_search"
                android:layout_width="40dp"
                android:layout_height="40dp"
                app:icon="@drawable/ic_search"
                app:iconGravity="textStart"
                app:iconPadding="0dp"
                app:cornerRadius="8dp"
                android:insetLeft="0dp"
                android:insetTop="0dp"
                android:insetRight="0dp"
                android:insetBottom="0dp"
                android:padding="8dp"
                style="@style/Widget.App.Button"
                android:contentDescription="搜索"
                android:layout_marginStart="4dp"/>
    </LinearLayout>
    </androidx.cardview.widget.CardView>

</androidx.coordinatorlayout.widget.CoordinatorLayout>