package com.meituaneleme.assistant.update

import android.app.Activity
import android.app.AlertDialog
import android.content.Intent
import android.net.Uri
import android.os.Build
import android.os.Environment
import android.provider.Settings
import android.text.Html
import android.util.Log
import android.widget.Toast
import androidx.core.content.FileProvider
import cn.leancloud.LCObject
import cn.leancloud.LCQuery
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import okhttp3.Call
import okhttp3.OkHttpClient
import okhttp3.Request
import java.io.File
import java.io.FileOutputStream
import java.io.IOException
import java.security.MessageDigest
import android.os.Handler
import android.os.Looper
import android.content.pm.PackageManager
import com.meituaneleme.assistant.util.SignatureUtils

class UpdateManager(private val activity: Activity) {
    companion object {
        private const val TAG = "UpdateManager"
        private const val BUFFER_SIZE = 8192
        
        // 添加版本检查结果常量
        private const val VERSION_OK = 0  // 版本正常
        private const val VERSION_NEED_UPDATE = 1  // 需要更新
        private const val VERSION_FORCE_UPDATE = 2  // 需要强制更新
        private const val VERSION_NOT_SUPPORTED = 3  // 版本不再支持
    }

    private val okHttpClient = OkHttpClient()
    private var isDownloading = false
    private var currentCall: Call? = null

    suspend fun checkUpdate(): Boolean {
        return try {
            withContext(Dispatchers.IO) {
                val query = LCQuery<LCObject>("AppVersion")
                query.whereEqualTo("platform", "android")
                query.orderByDescending("versionCode")
                query.limit = 1

                val latestVersion = query.find().firstOrNull()
                Log.d(TAG, "Latest version: ${latestVersion?.getInt("versionCode")}")

                if (latestVersion != null) {
                    withContext(Dispatchers.Main) {
                        val currentVersionCode = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.P) {
                            activity.packageManager.getPackageInfo(activity.packageName, 0).longVersionCode.toInt()
                        } else {
                            @Suppress("DEPRECATION")
                            activity.packageManager.getPackageInfo(activity.packageName, 0).versionCode
                        }
                        Log.d(TAG, "Current version: $currentVersionCode")

                        val versionStatus = checkVersionStatus(
                            currentVersionCode,
                            latestVersion.getInt("versionCode"),
                            latestVersion.getInt("minSupportVersion")
                        )

                        when (versionStatus) {
                            VERSION_NOT_SUPPORTED -> {
                                showUnsupportedDialog(
                                    latestVersion.getString("downloadUrl"),
                                    latestVersion.getString("md5")
                                )
                                return@withContext true
                            }
                            VERSION_FORCE_UPDATE, VERSION_NEED_UPDATE -> {
                                showUpdateDialog(
                                    latestVersion.getString("updateMessage"),
                                    latestVersion.getString("downloadUrl"),
                                    latestVersion.getString("md5"),
                                    latestVersion.getBoolean("forceUpdate"),
                                )
                                return@withContext true
                            }
                            VERSION_OK -> {
                                // 版本正常，不需要操作
                                return@withContext false
                            }

                            else -> {
                                Log.e(TAG, "Unknown version status: $versionStatus")
                                return@withContext false
                            }
                        }
                    }
                } else {
                    false
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Check update failed", e)
            false
        }
    }

    private fun checkVersionStatus(
        currentVersion: Int,
        latestVersion: Int,
        minSupportVersion: Int
    ): Int {
        return when {
            currentVersion < minSupportVersion -> VERSION_NOT_SUPPORTED
            currentVersion < latestVersion -> {
                if (currentVersion < minSupportVersion) {
                    VERSION_FORCE_UPDATE
                } else {
                    VERSION_NEED_UPDATE
                }
            }
            else -> VERSION_OK
        }
    }

    private fun showUnsupportedDialog(downloadUrl: String, md5: String) {
        AlertDialog.Builder(activity)
            .setTitle("版本不再支持")
            .setMessage("当前版本已不再支持，请立即更新到最新版本。")
            .setCancelable(false)
            .setPositiveButton("立即更新") { _, _ ->
                // 强制更新
                startDownload(downloadUrl, md5)
            }
            .setNegativeButton("退出应用") { _, _ ->
                activity.finish()
                android.os.Process.killProcess(android.os.Process.myPid())
            }
            .show()
    }

    private fun showUpdateDialog(
        updateMessage: String,
        downloadUrl: String,
        md5: String,
        forceUpdate: Boolean
    ) {
        Log.d(TAG, "Showing update dialog: $updateMessage")
        activity.runOnUiThread {
            val formattedMessage = updateMessage.replace("\n", "<br>")
            val spannedMessage = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
                Html.fromHtml(formattedMessage, Html.FROM_HTML_MODE_LEGACY)
            } else {
                @Suppress("DEPRECATION")
                Html.fromHtml(formattedMessage)
            }

            val builder = AlertDialog.Builder(activity)
                .setTitle("发现新版本")
                .setMessage(spannedMessage)
                .setPositiveButton("立即更新") { _, _ ->
                    startDownload(downloadUrl, md5)
                }

            if (!forceUpdate) {
                builder.setNegativeButton("稍后再说") { dialog, _ ->
                    dialog.dismiss()
                }
            }

            val dialog = builder.create()
            dialog.setCancelable(!forceUpdate)
            dialog.show()
        }
    }

    private fun startDownload(downloadUrl: String, md5: String) {
        // 再次验证应用签名
        if (SignatureUtils.verifyAppSignature(activity)) {
            Toast.makeText(
                activity,
                "应用签名验证失败，请从官方渠道下载安装",
                Toast.LENGTH_LONG
            ).show()
            return
        }
        
        if (isDownloading) {
            Toast.makeText(activity, "正在下载中...", Toast.LENGTH_SHORT).show()
            return
        }

        val handler = Handler(Looper.getMainLooper())

        // 创建进度对话框
        val progressDialog = AlertDialog.Builder(activity)
            .setTitle("正在下载更新")
            .setMessage("已下载: 0%")
            .setNegativeButton("取消") { dialog, _ ->
                currentCall?.cancel()
                isDownloading = false
                dialog.dismiss()
                Toast.makeText(activity, "下载已取消，应用将关闭", Toast.LENGTH_SHORT).show()
                // 延迟1秒后关闭应用，让用户看到提示
                handler.postDelayed({ 
                    activity.finish()
                    android.os.Process.killProcess(android.os.Process.myPid())
                }, 1000)
            }
            .setCancelable(false)
            .create()

        Thread {
            try {
                isDownloading = true
                val fullUrl = if (!downloadUrl.startsWith("http")) {
                    "http://113.44.82.43:3001/downloads/$downloadUrl"
                } else {
                    downloadUrl
                }
                
                try {
                    Uri.parse(fullUrl)
                } catch (e: Exception) {
                    throw IllegalArgumentException("Invalid URL format: $fullUrl")
                }
                
                val request = Request.Builder().url(fullUrl).build()
                currentCall = okHttpClient.newCall(request)
                val response = currentCall?.execute()
                val body = response?.body

                if (body != null) {
                    val contentLength = body.contentLength()
                    var downloadedLength = 0L

                    val apkFile = File(
                        activity.getExternalFilesDir(Environment.DIRECTORY_DOWNLOADS),
                        "update.apk"
                    )

                    activity.runOnUiThread { progressDialog.show() }

                    FileOutputStream(apkFile).use { output ->
                        body.byteStream().use { input ->
                            val buffer = ByteArray(BUFFER_SIZE)
                            var read: Int
                            while (input.read(buffer).also { read = it } != -1) {
                                if (!isDownloading) {
                                    // 如果下载被取消，删除未完成的文件
                                    apkFile.delete()
                                    return@Thread
                                }
                                output.write(buffer, 0, read)
                                downloadedLength += read

                                // 更新进度
                                val progress = (downloadedLength * 100 / contentLength).toInt()
                                activity.runOnUiThread {
                                    progressDialog.setMessage("已下载: $progress%")
                                }
                            }
                            output.flush()
                        }
                    }

                    activity.runOnUiThread { progressDialog.dismiss() }

                    if (isDownloading && calculateMD5(apkFile) == md5) {
                        installApk(apkFile)
                    } else if (isDownloading) {
                        activity.runOnUiThread {
                            Toast.makeText(activity, "文件校验失败", Toast.LENGTH_SHORT).show()
                        }
                        apkFile.delete()
                    }
                } else {
                    activity.runOnUiThread {
                        Toast.makeText(activity, "下载失败：响应为空", Toast.LENGTH_LONG).show()
                    }
                }
            } catch (e: Exception) {
                if (e is IOException && !isDownloading) {
                    // 下载被取消的情况已在上面的取消按钮处理中处理
                    return@Thread
                }
                Log.e(TAG, "Download failed", e)
                activity.runOnUiThread {
                    progressDialog.dismiss()
                    Toast.makeText(activity, "下载失败：${e.message}", Toast.LENGTH_LONG).show()
                    // 延迟2秒后关闭应用，让用户看到错误信息
                    Handler(Looper.getMainLooper()).postDelayed({ 
                        activity.finish()
                        android.os.Process.killProcess(android.os.Process.myPid())
                    }, 2000)
                }
            } finally {
                isDownloading = false
                currentCall = null
            }
        }.start()
    }

    private fun calculateMD5(file: File): String {
        val digest = MessageDigest.getInstance("MD5")
        file.inputStream().use { input ->
            val buffer = ByteArray(BUFFER_SIZE)
            var read: Int
            while (input.read(buffer).also { read = it } != -1) {
                digest.update(buffer, 0, read)
            }
        }
        return digest.digest().joinToString("") { "%02x".format(it) }
    }

    private fun installApk(apkFile: File) {
        try {
            // 基本验证
            val packageInfo = activity.packageManager.getPackageArchiveInfo(
                apkFile.absolutePath,
                PackageManager.GET_SIGNATURES
            ) ?: throw Exception("Invalid APK file")

            // 安装新版本
            val intent = Intent(Intent.ACTION_VIEW)
            val uri = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
                FileProvider.getUriForFile(
                    activity,
                    "${activity.packageName}.fileprovider",
                    apkFile
                )
            } else {
                Uri.fromFile(apkFile)
            }

            intent.setDataAndType(uri, "application/vnd.android.package-archive")
            intent.addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION)
            intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            activity.startActivity(intent)
        } catch (e: Exception) {
            Log.e(TAG, "Install APK failed", e)
            // 确保在主线程中显示 Toast
            activity.runOnUiThread {
                Toast.makeText(activity, "安装失败：${e.message}", Toast.LENGTH_LONG).show()
            }
        }
    }
} 