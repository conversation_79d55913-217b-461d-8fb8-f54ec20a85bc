package com.meituaneleme.assistant

import android.app.Application
import android.os.Process
import cn.leancloud.LeanCloud
import com.meituaneleme.assistant.util.SignatureUtils

class MyApplication : Application() {
    override fun onCreate() {
        super.onCreate()
        // 初始化 LeanCloud SDK
        LeanCloud.initialize(
            this,
            "UEAVFUYyDCB3ucTQswp5G6Zk-gzGzoHsz",
            "AiaVPlM1DrCLpC6siQfXynBQ",
            "https://ueavfuyy.lc-cn-n1-shared.com"
        )
        
        // 验证应用签名
        if (SignatureUtils.verifyAppSignature(this)) {
            // 签名不匹配，应用可能被篡改
            android.os.Handler().post {
                android.widget.Toast.makeText(
                    this,
                    "应用签名验证失败，请从官方渠道下载安装",
                    android.widget.Toast.LENGTH_LONG
                ).show()
            }
            
            // 延迟一秒后退出应用
            android.os.Handler().postDelayed({
                Process.killProcess(Process.myPid())
            }, 1000)
        }
    }
} 