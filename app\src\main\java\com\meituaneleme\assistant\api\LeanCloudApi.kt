package com.meituaneleme.assistant.api

import retrofit2.Call
import retrofit2.Response
import retrofit2.http.Body
import retrofit2.http.GET
import retrofit2.http.Header
import retrofit2.http.Headers
import retrofit2.http.POST
import retrofit2.http.PUT
import retrofit2.http.Path
import retrofit2.http.Query

data class User(
    val username: String,
    val password: String,
    val deviceId: String? = null,  // 设备ID
    val ipAddress: String? = null,  // IP地址
    val registrationTime: Long = System.currentTimeMillis()
)
data class Store(
    val storeName: String,
    val owner: String,
    val platform: String

) // owner 是 userId
data class StoreCookies(
    val userId: String,
    val storeId: String,
    val cookies: String
)

data class PlatformStore(val platform: String, val name: String)

data class CookiesResponse(
    val updatedAt: String,
    val cookies: String,
    val createdAt: String,
    val storeId: String,
    val userId: String,
    val objectId: String,
)

data class CookiesResponseWrapper(
    val results: List<CookiesResponse> // results 是一个 StoreResponse 对象的列表
)



interface LeanCloudApi {
    // @Headers("X-LC-Id: UEAVFUYyDCB3ucTQswp5G6Zk-gzGzoHsz", "X-LC-Key: AiaVPlM1DrCLpC6siQfXynBQ", "Content-Type: application/json")
    // @POST("classes/_User")
    // fun registerUser(@Body user: User): Call<UserResponse> // 返回 UserResponse 包含 userId

    @Headers("X-LC-Id: UEAVFUYyDCB3ucTQswp5G6Zk-gzGzoHsz", "X-LC-Key: AiaVPlM1DrCLpC6siQfXynBQ", "Content-Type: application/json")
    @POST("classes/Store") // 替换为你的门店类名
    fun createStore(@Body store: Store): Call<StoreResponse> // 返回 StoreResponse 包含 storeId

    @Headers("X-LC-Id: UEAVFUYyDCB3ucTQswp5G6Zk-gzGzoHsz", "X-LC-Key: AiaVPlM1DrCLpC6siQfXynBQ", "Content-Type: application/json")
    @POST("classes/Cookies")
    fun saveStoreCookies(@Body storeCookies: StoreCookies): Call<Void>

    // 添加 addStore 方法
    @Headers("X-LC-Id: UEAVFUYyDCB3ucTQswp5G6Zk-gzGzoHsz", "X-LC-Key: AiaVPlM1DrCLpC6siQfXynBQ", "Content-Type: application/json")
    @POST("classes/Store") // 替换为你的门店类名
    fun addStore(@Body store: PlatformStore): Call<Void> // 返回 Void

    @Headers("X-LC-Id: UEAVFUYyDCB3ucTQswp5G6Zk-gzGzoHsz", "X-LC-Key: AiaVPlM1DrCLpC6siQfXynBQ", "Content-Type: application/json")
    @POST("login") // LeanCloud 登录接口
    fun loginUser(@Body user: User): Call<LoginResponse> // 改为返回 LoginResponse

    @Headers("X-LC-Id: UEAVFUYyDCB3ucTQswp5G6Zk-gzGzoHsz", "X-LC-Key: AiaVPlM1DrCLpC6siQfXynBQ", "Content-Type: application/json")
    @GET("classes/Store") //
    fun getStores(@Query("where") where: String): Call<StoreResponseWrapper> // 返回 StoreResponseWrapper

    @Headers("X-LC-Id: UEAVFUYyDCB3ucTQswp5G6Zk-gzGzoHsz", "X-LC-Key: AiaVPlM1DrCLpC6siQfXynBQ", "Content-Type: application/json")
    @GET("classes/Cookies") // 替换为你的 Cookies 类名
    fun getStoreCookies(@Query("where") where: String): Call<CookiesResponseWrapper> // 返回 StoreResponseWrapper

    @Headers("X-LC-Id: UEAVFUYyDCB3ucTQswp5G6Zk-gzGzoHsz", "X-LC-Key: AiaVPlM1DrCLpC6siQfXynBQ", "Content-Type: application/json")
    @GET("classes/Cookies")
    fun getCookiesByStoreId(@Query("where") where: String): Call<CookiesResponseWrapper>

    @Headers("X-LC-Id: UEAVFUYyDCB3ucTQswp5G6Zk-gzGzoHsz", "X-LC-Key: AiaVPlM1DrCLpC6siQfXynBQ", "Content-Type: application/json")
    @PUT("classes/Cookies/{objectId}")
    fun updateStoreCookies(@Path("objectId") objectId: String, @Body request: UpdateCookiesRequest): Call<Void>

    @Headers("X-LC-Id: UEAVFUYyDCB3ucTQswp5G6Zk-gzGzoHsz", "X-LC-Key: AiaVPlM1DrCLpC6siQfXynBQ", "Content-Type: application/json")
    @GET("users/me")
    fun getUserInfo(@Header("X-LC-Session") sessionToken: String): Call<UserInfoResponse>

    @Headers("X-LC-Id: UEAVFUYyDCB3ucTQswp5G6Zk-gzGzoHsz", "X-LC-Key: AiaVPlM1DrCLpC6siQfXynBQ", "Content-Type: application/json")
    @GET("classes/Store")
    fun getStoreCount(@Query("where") where: String): Call<StoreCountResponse>

    @Headers("X-LC-Id: UEAVFUYyDCB3ucTQswp5G6Zk-gzGzoHsz", "X-LC-Key: AiaVPlM1DrCLpC6siQfXynBQ", "Content-Type: application/json")
    @GET("/1.1/users")
    suspend fun checkDeviceRegistration(
        @Query("where") where: String
    ): Response<QueryResponse>

    @Headers("X-LC-Id: UEAVFUYyDCB3ucTQswp5G6Zk-gzGzoHsz", "X-LC-Key: AiaVPlM1DrCLpC6siQfXynBQ", "Content-Type: application/json")
    @POST("/1.1/users")
    suspend fun registerUser(@Body user: User): Response<UserResponse>
}

// 定义响应数据类
data class UserResponse(
    val objectId: String,
    val username: String,
    val createdAt: String? = null
)


data class UpdateCookiesRequest(
//    val storeId: String,
    val cookies: String
)

data class UserInfoResponse(
    val maxStoreNumber: Int,
    val objectId: String,
    val username: String
)

data class StoreCountResponse(
    val count: Int
)

// 添加登录响应数据类
data class LoginResponse(
    val sessionToken: String,
    val objectId: String,
    val username: String
)

// 添加查询响应数据类
data class QueryResponse(
    val results: List<UserResponse>
)
