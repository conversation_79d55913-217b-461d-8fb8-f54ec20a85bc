<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:padding="16dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <!-- 标题 -->
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="设备指纹管理"
            android:textSize="24sp"
            android:textStyle="bold"
            android:gravity="center"
            android:layout_marginBottom="24dp" />

        <!-- 设备指纹信息卡片 -->
        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            app:cardCornerRadius="8dp"
            app:cardElevation="4dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="设备指纹信息"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:layout_marginBottom="12dp" />

                <TextView
                    android:id="@+id/tv_device_id"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="设备ID: 加载中..."
                    android:textSize="14sp"
                    android:layout_marginBottom="8dp" />

                <TextView
                    android:id="@+id/tv_user_agent"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="用户代理: 加载中..."
                    android:textSize="14sp"
                    android:layout_marginBottom="8dp" />

                <TextView
                    android:id="@+id/tv_screen_resolution"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="屏幕分辨率: 加载中..."
                    android:textSize="14sp"
                    android:layout_marginBottom="8dp" />

                <TextView
                    android:id="@+id/tv_timezone"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="时区: 加载中..."
                    android:textSize="14sp"
                    android:layout_marginBottom="8dp" />

                <TextView
                    android:id="@+id/tv_language"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="语言: 加载中..."
                    android:textSize="14sp" />

            </LinearLayout>
        </androidx.cardview.widget.CardView>

        <!-- 会话信息卡片 -->
        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            app:cardCornerRadius="8dp"
            app:cardElevation="4dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="登录会话信息"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:layout_marginBottom="12dp" />

                <TextView
                    android:id="@+id/tv_sessions"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="加载中..."
                    android:textSize="14sp"
                    android:maxLines="10"
                    android:scrollbars="vertical" />

            </LinearLayout>
        </androidx.cardview.widget.CardView>

        <!-- 操作按钮 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <Button
                android:id="@+id/btn_refresh"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="刷新数据"
                android:layout_marginBottom="8dp"
                android:background="@drawable/rounded_button"
                android:textColor="@android:color/white" />

            <Button
                android:id="@+id/btn_clear_fingerprint"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="清除设备指纹"
                android:layout_marginBottom="8dp"
                android:backgroundTint="@color/red"
                android:textColor="@android:color/white" />

            <Button
                android:id="@+id/btn_clear_sessions"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="清除登录会话"
                android:backgroundTint="@color/red"
                android:textColor="@android:color/white" />

        </LinearLayout>

    </LinearLayout>
</ScrollView>
