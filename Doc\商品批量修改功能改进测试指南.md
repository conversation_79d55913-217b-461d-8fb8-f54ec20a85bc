# 商品批量修改功能改进测试指南

## 改进概述

本次改进主要针对美团饿了么平台商品批量修改程序的折扣价管理功能进行了全面升级，新增了以下核心功能：

### 1. 智能折扣价管理
- **自动判断逻辑**：系统现在能够智能判断商品是否已有折扣价
- **修改现有折扣**：如果商品已设置折扣价，使用修改接口更新价格
- **创建新折扣**：如果商品未设置折扣价，使用创建接口新建折扣
- **支持平台**：美团和饿了么平台均支持

### 2. 美团折扣排序功能
- **排序修改**：支持修改美团平台商品的折扣排序
- **平台限制**：仅限美团平台，饿了么不支持此功能
- **前置条件**：商品必须已设置折扣价才能修改排序

### 3. 用户界面优化
- **新增UI元素**：添加了折扣排序修改的输入框和按钮
- **智能显示**：根据商品平台动态显示/隐藏相关功能
- **用户反馈**：提供详细的操作结果反馈和错误信息

## 测试场景

### 场景1：创建新折扣价
**测试步骤：**
1. 选择一个没有设置折扣价的商品
2. 在折扣价输入框中输入新的折扣价格
3. 点击"保存"按钮
4. 观察系统反馈

**预期结果：**
- 系统自动识别商品未设置折扣
- 调用创建折扣接口
- 显示"创建折扣价：XX 成功"的提示
- 商品折扣价信息更新

### 场景2：修改现有折扣价
**测试步骤：**
1. 选择一个已有折扣价的商品
2. 在折扣价输入框中输入新的价格
3. 点击"保存"按钮
4. 观察系统反馈

**预期结果：**
- 系统自动识别商品已有折扣
- 调用修改折扣接口
- 显示"修改折扣价：XX 成功"的提示
- 商品折扣价信息更新

### 场景3：美团折扣排序修改
**测试步骤：**
1. 选择一个美团平台且已设置折扣的商品
2. 在排序输入框中输入排序值（数字）
3. 点击"修改排序"按钮
4. 观察系统反馈

**预期结果：**
- 系统验证商品已有折扣
- 调用美团排序修改接口
- 显示"折扣排序修改为：XX 成功"的提示

### 场景4：错误处理测试
**测试步骤：**
1. 尝试为多规格商品修改折扣价
2. 尝试为未设置折扣的商品修改排序
3. 输入无效的价格或排序值
4. 观察错误提示

**预期结果：**
- 多规格商品：提示"请在商品规格中修改"
- 未设置折扣：提示"商品未设置折扣"
- 无效输入：提示"请输入有效的数字/价格"

## 功能验证清单

### API功能验证
- [ ] 美团创建折扣API (`MeiTuanApi.createDiscount`)
- [ ] 美团修改折扣排序API (`MeiTuanApi.updateDiscountSort`)
- [ ] 饿了么创建折扣API (`ElemeApi.createDiscount`)
- [ ] 饿了么修改折扣API (`ElemeApi.updateDiscount`)

### UI功能验证
- [ ] 折扣排序输入框显示/隐藏逻辑
- [ ] 智能判断折扣状态逻辑
- [ ] 用户输入验证
- [ ] 操作结果反馈

### 兼容性验证
- [ ] 现有功能保持正常工作
- [ ] 多规格商品处理
- [ ] 不同平台商品处理
- [ ] 错误情况处理

## 注意事项

1. **测试环境**：确保在测试环境中进行，避免影响生产数据
2. **登录状态**：确保相关门店账号处于登录状态
3. **网络连接**：确保网络连接稳定
4. **数据备份**：测试前建议备份重要数据

## 已知限制

1. **排序功能**：仅美团平台支持，饿了么平台不支持排序修改
2. **多规格商品**：需要在商品规格页面单独处理
3. **折扣前置条件**：修改排序需要商品已设置折扣价

## 问题反馈

如果在测试过程中发现问题，请记录以下信息：
- 操作步骤
- 预期结果
- 实际结果
- 错误信息
- 商品信息（平台、规格数量等）
- 系统日志
