package com.meituaneleme.assistant


import android.annotation.SuppressLint
import android.content.res.ColorStateList
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import android.widget.ImageView
import android.widget.PopupMenu
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.RecyclerView
import androidx.recyclerview.widget.LinearLayoutManager
import com.meituaneleme.assistant.api.StoreCookies
import com.meituaneleme.assistant.api.StoreResponse
import com.meituaneleme.assistant.R
import com.meituaneleme.assistant.api.StoreValidCheck
import android.graphics.Color

class StoreAdapter(
    private val storeList: List<StoreValidCheck>,
    private val onStoreRefreshListener: OnStoreRefreshListener? = null
) : RecyclerView.Adapter<StoreAdapter.StoreViewHolder>() {

    // 定义门店操作的接口
    interface OnStoreRefreshListener {
        fun onRefreshStore(store: StoreValidCheck, position: Int)
        fun onUpdateCookies(store: StoreValidCheck, position: Int)
    }

    class StoreViewHolder(view: View) : RecyclerView.ViewHolder(view) {
        val storeNameTextView: TextView = view.findViewById(R.id.tv_store_name)
        val storeStatusTextView: TextView = view.findViewById(R.id.tv_status)
        val platformImageView: ImageView = view.findViewById(R.id.iv_platform)
        val storeItemContainer: View = view.findViewById(R.id.store_item_container)
        val refreshButton: ImageView = view.findViewById(R.id.iv_refresh)

        fun bind(store: StoreValidCheck) {
            // 设置门店名称
            storeNameTextView.text = store.storeName
            
            // 设置平台图标和背景色
            if (store.platform == "美团") {
                platformImageView.setImageResource(R.drawable.ic_meituan_logo)
                storeItemContainer.setBackgroundTintList(ColorStateList.valueOf(
                    ContextCompat.getColor(itemView.context, R.color.meituan_bg)))
                storeNameTextView.setTextColor(ContextCompat.getColor(itemView.context, R.color.meituan_text))
            } else {
                platformImageView.setImageResource(R.drawable.ic_eleme_logo)
                storeItemContainer.setBackgroundTintList(ColorStateList.valueOf(
                    ContextCompat.getColor(itemView.context, R.color.eleme_bg)))
                storeNameTextView.setTextColor(ContextCompat.getColor(itemView.context, R.color.eleme_text))
            }
            
            // 设置状态标签
            storeStatusTextView.text = when (store.isValid) {
                null -> "状态"
                true -> "有效"
                false -> "失效"
            }
            
            storeStatusTextView.setBackgroundResource(
                when (store.isValid) {
                    null -> R.drawable.bg_status_gray
                    true -> R.drawable.bg_status_green
                    false -> R.drawable.bg_status_red
                }
            )
        }
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): StoreViewHolder {
        val view = LayoutInflater.from(parent.context).inflate(R.layout.item_store, parent, false)
        return StoreViewHolder(view)
    }

    override fun onBindViewHolder(holder: StoreViewHolder, position: Int) {
        val store = storeList[position]
        holder.bind(store)
        
        // 设置操作按钮点击事件，显示弹出菜单
        holder.refreshButton.setOnClickListener { view ->
            showStoreOperationMenu(view, store, position)
        }
    }

    override fun getItemCount(): Int {
        return storeList.size
    }

    /**
     * 显示门店操作菜单
     */
    private fun showStoreOperationMenu(view: View, store: StoreValidCheck, position: Int) {
        val popupMenu = PopupMenu(view.context, view)
        popupMenu.menuInflater.inflate(R.menu.store_operation_menu, popupMenu.menu)

        popupMenu.setOnMenuItemClickListener { menuItem ->
            when (menuItem.itemId) {
                R.id.action_refresh_status -> {
                    // 刷新有效状态
                    onStoreRefreshListener?.onRefreshStore(store, position)
                    true
                }
                R.id.action_update_cookies -> {
                    // 更新Cookies
                    onStoreRefreshListener?.onUpdateCookies(store, position)
                    true
                }
                else -> false
            }
        }

        popupMenu.show()
    }
} 