#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
京东抢资格助手 - 启动脚本
现代化图形用户界面版本
"""

import sys
import os

def check_dependencies():
    """检查依赖包"""
    missing_packages = []
    
    try:
        import customtkinter
    except ImportError:
        missing_packages.append("customtkinter")
    
    try:
        import requests
    except ImportError:
        missing_packages.append("requests")
    
    if missing_packages:
        print("缺少以下依赖包:")
        for pkg in missing_packages:
            print(f"  - {pkg}")
        print("\n请运行以下命令安装:")
        print("pip install " + " ".join(missing_packages))
        return False
    
    return True

def main():
    """主函数"""
    print("京东抢资格助手 v2.0")
    print("=" * 40)
    
    # 检查依赖
    if not check_dependencies():
        input("按回车键退出...")
        return
    
    # 检查原脚本文件
    if not os.path.exists("抢资格.py"):
        print("错误: 找不到原脚本文件 '抢资格.py'")
        print("请确保该文件与此启动脚本在同一目录下")
        input("按回车键退出...")
        return
    
    try:
        # 启动GUI应用
        from gui_app import main as gui_main
        print("正在启动图形界面...")
        gui_main()
    except Exception as e:
        print(f"启动失败: {e}")
        input("按回车键退出...")

if __name__ == "__main__":
    main()
