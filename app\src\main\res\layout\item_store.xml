<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginHorizontal="10dp"
    android:layout_marginVertical="5dp"
    app:cardCornerRadius="10dp"
    app:cardElevation="2dp">

    <LinearLayout
        android:id="@+id/store_item_container"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        android:padding="12dp">

        <!-- 平台图标 -->
        <androidx.cardview.widget.CardView
            android:layout_width="40dp"
            android:layout_height="40dp"
            app:cardCornerRadius="20dp"
            app:cardElevation="0dp">
            
            <ImageView
                android:id="@+id/iv_platform"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:contentDescription="平台图标"
                android:src="@drawable/ic_meituan_logo"
                android:scaleType="centerCrop"
                android:padding="2dp"/>
        </androidx.cardview.widget.CardView>

        <!-- 门店名称 -->
        <TextView
            android:id="@+id/tv_store_name"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:layout_marginStart="10dp"
            android:text="门店名称"
            android:textColor="@color/text_primary"
            android:textSize="16sp"
            android:textStyle="bold"/>

        <!-- 门店状态标签 -->
        <TextView
            android:id="@+id/tv_status"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="有效"
            android:textColor="@android:color/white"
            android:textSize="14sp"
            android:paddingHorizontal="12dp"
            android:paddingVertical="4dp"
            android:background="@drawable/bg_status_tag"
            android:layout_marginStart="8dp"/>

        <!-- 刷新按钮 -->
        <ImageView
            android:id="@+id/iv_refresh"
            android:layout_width="36dp"
            android:layout_height="36dp"
            android:src="@drawable/ic_refresh"
            android:padding="6dp"
            android:contentDescription="刷新门店数据"/>
    </LinearLayout>
</androidx.cardview.widget.CardView>
