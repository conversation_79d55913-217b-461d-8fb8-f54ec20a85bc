package com.meituaneleme.assistant


import android.content.Intent
import android.os.Bundle
import android.widget.ArrayAdapter
import android.widget.Button
import android.widget.Spinner
import android.widget.Toast
import androidx.appcompat.app.AppCompatActivity
import androidx.recyclerview.widget.RecyclerView
import androidx.recyclerview.widget.LinearLayoutManager
import com.meituaneleme.assistant.StoreAdapter
import com.meituaneleme.assistant.api.ApiClient
import com.meituaneleme.assistant.api.CookiesResponse
import com.meituaneleme.assistant.api.CookiesResponseWrapper
import com.meituaneleme.assistant.api.StoreCookies
import org.json.JSONArray
import org.json.JSONObject
import com.meituaneleme.assistant.api.StoreResponse
import com.meituaneleme.assistant.api.StoreResponseWrapper
import retrofit2.Call
import retrofit2.Callback
import retrofit2.Response
import android.util.Log
import com.meituaneleme.assistant.api.StoreValidCheck

class StoreManagementActivity : AppCompatActivity() {
    private lateinit var addStoreButton: Button
//    private lateinit var searchButton: Button
    private lateinit var recyclerView: RecyclerView
    private lateinit var storeList: MutableList<StoreResponse>
    private lateinit var cookiesList: MutableList<StoreCookies>
    private lateinit var storeAdapter: StoreAdapter
    private lateinit var storevalidcheckList: MutableList<StoreValidCheck>
    private lateinit var platformSpinner: Spinner


    public override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_store_management)

        Log.d("StoreManagementActivity", "StoreManagementActivity created")

        addStoreButton = findViewById(R.id.add_store_button)
//        searchButton = findViewById(R.id.search_button)
        recyclerView = findViewById(R.id.recycler_view)
        platformSpinner = findViewById(R.id.platform_spinner2)
        storeList = mutableListOf()
        cookiesList = mutableListOf()

        // 初始化 RecyclerView 和适配器
        storeAdapter = StoreAdapter(storevalidcheckList)
        recyclerView.layoutManager = LinearLayoutManager(this)
        recyclerView.adapter = storeAdapter

        val platforms = arrayOf("美团", "饿了么")
        val adapter = ArrayAdapter(this, android.R.layout.simple_spinner_item, platforms)
        adapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item)
        platformSpinner.adapter = adapter

//        val refreshButton: Button = findViewById(R.id.refresh_button)
//        refreshButton.setOnClickListener {
//            getStoreDetails()
//        }

        addStoreButton.setOnClickListener {
            val selectedItem = platformSpinner.selectedItem
            if (selectedItem == null) {
                Toast.makeText(this, "请选择一个平台", Toast.LENGTH_SHORT).show()
                return@setOnClickListener
            }
            val platform = selectedItem.toString()
            // 直接打开 WebViewActivity
            val intent = Intent(this, WebViewActivity::class.java)
            //将storeList传递给WebViewActivity
            intent.putParcelableArrayListExtra("storeList", ArrayList(storeList))
            intent.putExtra("platform", platform)
            startActivity(intent)
        }

//        searchButton.setOnClickListener {
//            val intent = Intent(this, MainActivity::class.java)
//            startActivity(intent)
//        }

        // 获取门店明细和 Cookies
//        getStoreDetails()
    }


//    fun getStoreDetails() {
//        // 获取当前用户名称
//        val sharedPreferences = getSharedPreferences("UserPrefs", MODE_PRIVATE)
//        val username = sharedPreferences.getString("username", "") ?: ""
//
//        // 调用抽离的方法，传递 true 表示来自 StoreManagementActivity
//        StoreUtils.getStoreDetails(this, username, false)
//    }

    // 更新 RecyclerView 的数据
    fun updateStoreList(newStores: List<StoreResponse>) {
        storeList.clear()
        storeList.addAll(newStores)
        storeAdapter.notifyDataSetChanged() // 通知适配器数据已更改


    }

} 