# 抢资格GUI应用程序主文件
# 现代化图形用户界面版本

from tkinter import messagebox, filedialog
import threading
import queue
import json
import os
import time
import random
import datetime

# 尝试导入customtkinter，如果失败则提示安装
try:
    import customtkinter as ctk
except ImportError:
    print("请安装customtkinter: pip install customtkinter")
    exit(1)

# 导入原脚本的核心功能
try:
    from 抢资格 import *
except ImportError:
    print("无法导入原脚本，请确保抢资格.py文件存在")
    exit(1)

# 设置customtkinter主题
ctk.set_appearance_mode("light")  # 默认浅色主题
ctk.set_default_color_theme("green")  # 绿色主题，更清新

class GrabQualificationGUI:
    def __init__(self):
        self.root = ctk.CTk()
        self.root.title("京东抢资格助手 v2.0")
        self.root.geometry("900x700")
        self.root.minsize(800, 600)

        # 设置窗口背景色为纯白色
        self.root.configure(fg_color="white")

        # 定义颜色主题
        self.colors = {
            "primary": "#2E7D32",      # 深绿色 - 主要按钮
            "secondary": "#4CAF50",    # 绿色 - 次要按钮
            "success": "#4CAF50",      # 成功绿色
            "warning": "#FF9800",      # 警告橙色
            "error": "#F44336",        # 错误红色
            "info": "#2196F3",         # 信息蓝色
            "background": "white",     # 背景白色
            "surface": "#F5F5F5",      # 表面浅灰
            "text_primary": "#212121", # 主要文字深灰
            "text_secondary": "#757575", # 次要文字中灰
            "border": "#E0E0E0"        # 边框浅灰
        }
        
        # 应用程序状态
        self.is_running = False
        self.grab_thread = None
        self.log_queue = queue.Queue()

        # 统计信息
        self.attempt_count = 0
        self.success_count = 0

        # 配置文件路径
        self.config_file = "config.json"
        
        # 初始化界面
        self.setup_ui()
        self.setup_shortcuts()
        self.load_config()

        # 启动日志监听和时间更新
        self.root.after(100, self.process_log_queue)
        self.root.after(1000, self.update_time_display)
        
    def setup_ui(self):
        """设置用户界面"""
        # 创建主框架 - 使用白色背景
        self.main_frame = ctk.CTkFrame(self.root, fg_color=self.colors["background"],
                                     corner_radius=10, border_width=1,
                                     border_color=self.colors["border"])
        self.main_frame.pack(fill="both", expand=True, padx=20, pady=20)

        # 创建标签页 - 使用更清晰的颜色
        self.notebook = ctk.CTkTabview(self.main_frame,
                                     fg_color=self.colors["background"],
                                     segmented_button_fg_color=self.colors["surface"],
                                     segmented_button_selected_color=self.colors["primary"],
                                     segmented_button_selected_hover_color=self.colors["secondary"])
        self.notebook.pack(fill="both", expand=True, padx=10, pady=10)
        
        # 添加标签页
        self.setup_monitor_tab()
        self.setup_config_tab()
        self.setup_log_tab()
        self.setup_about_tab()

    def setup_shortcuts(self):
        """设置快捷键"""
        # 全局快捷键
        self.root.bind("<Control-s>", lambda e: self.save_config())
        self.root.bind("<Control-o>", lambda e: self.load_config())
        self.root.bind("<Control-l>", lambda e: self.clear_logs())
        self.root.bind("<Control-e>", lambda e: self.export_logs())
        self.root.bind("<F1>", lambda e: self.show_help())
        self.root.bind("<F5>", lambda e: self.start_grab() if not self.is_running else None)
        self.root.bind("<F6>", lambda e: self.stop_grab() if self.is_running else None)
        self.root.bind("<F9>", lambda e: self.test_connection())
        self.root.bind("<Control-t>", lambda e: self.toggle_theme())
        self.root.bind("<Control-q>", lambda e: self.on_closing())

        # 标签页切换快捷键
        self.root.bind("<Control-1>", lambda e: self.notebook.set("监控"))
        self.root.bind("<Control-2>", lambda e: self.notebook.set("配置"))
        self.root.bind("<Control-3>", lambda e: self.notebook.set("日志"))
        self.root.bind("<Control-4>", lambda e: self.notebook.set("关于"))
        
    def setup_monitor_tab(self):
        """设置监控页面"""
        monitor_tab = self.notebook.add("监控")

        # 状态显示区域 - 使用白色背景和边框
        status_frame = ctk.CTkFrame(monitor_tab, fg_color=self.colors["background"],
                                  corner_radius=10, border_width=2,
                                  border_color=self.colors["primary"])
        status_frame.pack(fill="x", padx=10, pady=10)

        # 状态标题 - 使用深色文字
        status_title = ctk.CTkLabel(status_frame, text="运行状态",
                                  font=ctk.CTkFont(size=18, weight="bold"),
                                  text_color=self.colors["text_primary"])
        status_title.pack(pady=10)

        # 状态指示器和时间显示
        status_info_frame = ctk.CTkFrame(status_frame, fg_color=self.colors["surface"],
                                       corner_radius=8)
        status_info_frame.pack(fill="x", padx=10, pady=5)

        self.status_indicator = ctk.CTkLabel(status_info_frame, text="● 已停止",
                                           text_color=self.colors["error"],
                                           font=ctk.CTkFont(size=16, weight="bold"))
        self.status_indicator.pack(side="left", padx=10)

        self.time_label = ctk.CTkLabel(status_info_frame, text="",
                                     font=ctk.CTkFont(size=14),
                                     text_color=self.colors["text_primary"])
        self.time_label.pack(side="right", padx=10)

        # 统计信息
        stats_frame = ctk.CTkFrame(status_frame, fg_color=self.colors["surface"],
                                 corner_radius=8)
        stats_frame.pack(fill="x", padx=10, pady=5)

        self.attempt_count_label = ctk.CTkLabel(stats_frame, text="尝试次数: 0",
                                              font=ctk.CTkFont(size=14),
                                              text_color=self.colors["text_primary"])
        self.attempt_count_label.pack(side="left", padx=10)

        self.success_count_label = ctk.CTkLabel(stats_frame, text="成功次数: 0",
                                              font=ctk.CTkFont(size=14),
                                              text_color=self.colors["success"])
        self.success_count_label.pack(side="left", padx=10)

        self.next_hour_label = ctk.CTkLabel(stats_frame, text="距离下个整点: --",
                                          font=ctk.CTkFont(size=14),
                                          text_color=self.colors["info"])
        self.next_hour_label.pack(side="right", padx=10)

        # 进度条 - 使用主题色
        self.progress_bar = ctk.CTkProgressBar(status_frame,
                                             progress_color=self.colors["primary"],
                                             fg_color=self.colors["surface"])
        self.progress_bar.pack(fill="x", padx=10, pady=10)
        self.progress_bar.set(0)

        # 控制按钮区域
        control_frame = ctk.CTkFrame(monitor_tab, fg_color=self.colors["background"],
                                   corner_radius=10, border_width=1,
                                   border_color=self.colors["border"])
        control_frame.pack(fill="x", padx=10, pady=10)

        # 开始/停止按钮
        self.start_button = ctk.CTkButton(control_frame, text="🚀 开始抢购",
                                        command=self.start_grab, width=140, height=45,
                                        font=ctk.CTkFont(size=16, weight="bold"),
                                        fg_color=self.colors["success"],
                                        hover_color=self.colors["primary"],
                                        text_color="white")
        self.start_button.pack(side="left", padx=10, pady=10)

        self.stop_button = ctk.CTkButton(control_frame, text="⏹ 停止抢购",
                                       command=self.stop_grab, width=140, height=45,
                                       font=ctk.CTkFont(size=16, weight="bold"),
                                       state="disabled", fg_color=self.colors["error"],
                                       hover_color="#D32F2F", text_color="white")
        self.stop_button.pack(side="left", padx=10, pady=10)

        # 测试按钮
        self.test_button = ctk.CTkButton(control_frame, text="🔧 测试连接",
                                       command=self.test_connection, width=140, height=45,
                                       font=ctk.CTkFont(size=16),
                                       fg_color=self.colors["info"],
                                       hover_color="#1976D2", text_color="white")
        self.test_button.pack(side="left", padx=10, pady=10)

        # 信息显示区域
        info_frame = ctk.CTkFrame(monitor_tab, fg_color=self.colors["background"],
                                corner_radius=10, border_width=1,
                                border_color=self.colors["border"])
        info_frame.pack(fill="both", expand=True, padx=10, pady=10)

        info_title = ctk.CTkLabel(info_frame, text="实时信息",
                                font=ctk.CTkFont(size=16, weight="bold"),
                                text_color=self.colors["text_primary"])
        info_title.pack(pady=5)

        # 实时信息显示 - 使用白色背景，深色文字
        self.info_text = ctk.CTkTextbox(info_frame, height=200,
                                      font=ctk.CTkFont(family="Consolas", size=12),
                                      fg_color="white", text_color=self.colors["text_primary"],
                                      border_width=1, border_color=self.colors["border"])
        self.info_text.pack(fill="both", expand=True, padx=10, pady=10)
        
    def setup_config_tab(self):
        """设置配置页面"""
        config_tab = self.notebook.add("配置")
        
        # 创建滚动框架
        scrollable_frame = ctk.CTkScrollableFrame(config_tab)
        scrollable_frame.pack(fill="both", expand=True, padx=10, pady=10)
        
        # 基本配置
        basic_frame = ctk.CTkFrame(scrollable_frame)
        basic_frame.pack(fill="x", padx=10, pady=10)
        
        basic_title = ctk.CTkLabel(basic_frame, text="基本配置", font=ctk.CTkFont(size=16, weight="bold"))
        basic_title.pack(pady=10)
        
        # Cookie配置
        cookie_label = ctk.CTkLabel(basic_frame, text="Cookie:")
        cookie_label.pack(anchor="w", padx=10)
        
        self.cookie_entry = ctk.CTkTextbox(basic_frame, height=80)
        self.cookie_entry.pack(fill="x", padx=10, pady=5)
        
        # User-Agent配置
        ua_label = ctk.CTkLabel(basic_frame, text="User-Agent:")
        ua_label.pack(anchor="w", padx=10, pady=(10,0))
        
        self.ua_entry = ctk.CTkTextbox(basic_frame, height=60)
        self.ua_entry.pack(fill="x", padx=10, pady=5)
        
        # 时间配置
        time_frame = ctk.CTkFrame(scrollable_frame)
        time_frame.pack(fill="x", padx=10, pady=10)
        
        time_title = ctk.CTkLabel(time_frame, text="时间配置", font=ctk.CTkFont(size=16, weight="bold"))
        time_title.pack(pady=10)
        
        # 延迟配置
        delay_frame = ctk.CTkFrame(time_frame)
        delay_frame.pack(fill="x", padx=10, pady=5)
        
        # 普通延迟
        normal_delay_label = ctk.CTkLabel(delay_frame, text="普通延迟 (秒):")
        normal_delay_label.grid(row=0, column=0, padx=10, pady=5, sticky="w")
        
        self.normal_delay_min = ctk.CTkEntry(delay_frame, width=80, placeholder_text="最小")
        self.normal_delay_min.grid(row=0, column=1, padx=5, pady=5)
        
        self.normal_delay_max = ctk.CTkEntry(delay_frame, width=80, placeholder_text="最大")
        self.normal_delay_max.grid(row=0, column=2, padx=5, pady=5)
        
        # 整点延迟
        hour_delay_label = ctk.CTkLabel(delay_frame, text="整点延迟 (秒):")
        hour_delay_label.grid(row=1, column=0, padx=10, pady=5, sticky="w")
        
        self.hour_delay_min = ctk.CTkEntry(delay_frame, width=80, placeholder_text="最小")
        self.hour_delay_min.grid(row=1, column=1, padx=5, pady=5)
        
        self.hour_delay_max = ctk.CTkEntry(delay_frame, width=80, placeholder_text="最大")
        self.hour_delay_max.grid(row=1, column=2, padx=5, pady=5)
        
        # 重试次数
        retry_label = ctk.CTkLabel(delay_frame, text="最大重试次数:")
        retry_label.grid(row=2, column=0, padx=10, pady=5, sticky="w")
        
        self.max_retries = ctk.CTkEntry(delay_frame, width=80, placeholder_text="次数")
        self.max_retries.grid(row=2, column=1, padx=5, pady=5)
        
        # 配置按钮
        config_button_frame = ctk.CTkFrame(scrollable_frame)
        config_button_frame.pack(fill="x", padx=10, pady=10)
        
        save_button = ctk.CTkButton(config_button_frame, text="保存配置", command=self.save_config)
        save_button.pack(side="left", padx=10, pady=10)
        
        load_button = ctk.CTkButton(config_button_frame, text="加载配置", command=self.load_config)
        load_button.pack(side="left", padx=10, pady=10)
        
        reset_button = ctk.CTkButton(config_button_frame, text="重置默认", command=self.reset_config)
        reset_button.pack(side="left", padx=10, pady=10)
        
    def setup_log_tab(self):
        """设置日志页面"""
        log_tab = self.notebook.add("日志")

        # 日志控制区域
        log_control_frame = ctk.CTkFrame(log_tab)
        log_control_frame.pack(fill="x", padx=10, pady=10)

        # 第一行控制按钮
        control_row1 = ctk.CTkFrame(log_control_frame)
        control_row1.pack(fill="x", padx=5, pady=5)

        clear_button = ctk.CTkButton(control_row1, text="🗑 清空日志", command=self.clear_logs, width=100)
        clear_button.pack(side="left", padx=5, pady=5)

        export_button = ctk.CTkButton(control_row1, text="📁 导出日志", command=self.export_logs, width=100)
        export_button.pack(side="left", padx=5, pady=5)

        auto_scroll_var = ctk.BooleanVar(value=True)
        self.auto_scroll_checkbox = ctk.CTkCheckBox(control_row1, text="自动滚动", variable=auto_scroll_var)
        self.auto_scroll_checkbox.pack(side="left", padx=10, pady=5)
        self.auto_scroll_var = auto_scroll_var

        # 第二行过滤控件
        control_row2 = ctk.CTkFrame(log_control_frame)
        control_row2.pack(fill="x", padx=5, pady=5)

        filter_label = ctk.CTkLabel(control_row2, text="日志过滤:")
        filter_label.pack(side="left", padx=5)

        self.log_filter_entry = ctk.CTkEntry(control_row2, placeholder_text="输入关键词过滤日志...", width=200)
        self.log_filter_entry.pack(side="left", padx=5)
        self.log_filter_entry.bind("<KeyRelease>", self.filter_logs)

        filter_clear_button = ctk.CTkButton(control_row2, text="清除过滤", command=self.clear_filter, width=80)
        filter_clear_button.pack(side="left", padx=5)

        # 日志级别选择
        level_label = ctk.CTkLabel(control_row2, text="级别:")
        level_label.pack(side="left", padx=(20, 5))

        self.log_level_var = ctk.StringVar(value="全部")
        log_level_menu = ctk.CTkOptionMenu(control_row2, values=["全部", "信息", "警告", "错误", "成功"],
                                         variable=self.log_level_var, command=self.filter_logs_by_level, width=80)
        log_level_menu.pack(side="left", padx=5)

        # 日志显示区域 - 使用白色背景，深色文字
        self.log_text = ctk.CTkTextbox(log_tab, font=ctk.CTkFont(family="Consolas", size=11),
                                     fg_color="white", text_color=self.colors["text_primary"],
                                     border_width=1, border_color=self.colors["border"])
        self.log_text.pack(fill="both", expand=True, padx=10, pady=10)

        # 存储所有日志消息用于过滤
        self.all_log_messages = []
        
    def setup_about_tab(self):
        """设置关于页面"""
        about_tab = self.notebook.add("关于")
        
        about_frame = ctk.CTkFrame(about_tab)
        about_frame.pack(fill="both", expand=True, padx=10, pady=10)
        
        # 应用信息
        app_title = ctk.CTkLabel(about_frame, text="京东抢资格助手", 
                               font=ctk.CTkFont(size=24, weight="bold"))
        app_title.pack(pady=20)
        
        version_label = ctk.CTkLabel(about_frame, text="版本: 2.0.0", 
                                   font=ctk.CTkFont(size=16))
        version_label.pack(pady=5)
        
        description = ctk.CTkLabel(about_frame, 
                                 text="现代化图形界面的京东政府补贴资格抢购工具\n"
                                      "支持智能时间调度、实时状态监控、配置管理等功能",
                                 font=ctk.CTkFont(size=14))
        description.pack(pady=20)
        
        # 快捷键说明
        shortcuts_frame = ctk.CTkFrame(about_frame)
        shortcuts_frame.pack(fill="x", pady=20, padx=20)

        shortcuts_title = ctk.CTkLabel(shortcuts_frame, text="快捷键", font=ctk.CTkFont(size=16, weight="bold"))
        shortcuts_title.pack(pady=10)

        shortcuts_text = """
Ctrl+S: 保存配置    Ctrl+O: 加载配置    Ctrl+L: 清空日志
Ctrl+E: 导出日志    Ctrl+T: 切换主题    Ctrl+Q: 退出程序
F1: 显示帮助       F5: 开始抢购       F6: 停止抢购
F9: 测试连接       Ctrl+1-4: 切换标签页
        """

        shortcuts_label = ctk.CTkLabel(shortcuts_frame, text=shortcuts_text,
                                     font=ctk.CTkFont(family="Consolas", size=12))
        shortcuts_label.pack(pady=10)

        # 主题切换
        theme_frame = ctk.CTkFrame(about_frame)
        theme_frame.pack(pady=20)

        theme_label = ctk.CTkLabel(theme_frame, text="界面主题:")
        theme_label.pack(side="left", padx=10)

        self.theme_switch = ctk.CTkSwitch(theme_frame, text="深色模式", command=self.toggle_theme)
        self.theme_switch.pack(side="left", padx=10)
        # 默认浅色模式，所以不选中开关

        # 帮助按钮
        help_button = ctk.CTkButton(about_frame, text="📖 显示详细帮助", command=self.show_help)
        help_button.pack(pady=10)
        
    def start_grab(self):
        """开始抢购"""
        if not self.is_running:
            # 验证配置
            if not self.validate_config():
                return

            self.is_running = True
            self.start_button.configure(state="disabled")
            self.stop_button.configure(state="normal")
            self.test_button.configure(state="disabled")
            self.status_indicator.configure(text="● 运行中", text_color=self.colors["success"])

            # 重置统计
            self.attempt_count = 0
            self.success_count = 0

            # 启动抢购线程
            self.grab_thread = threading.Thread(target=self.grab_worker, daemon=True)
            self.grab_thread.start()

            self.add_log("🚀 抢购程序已启动")
            self.show_notification("抢购已开始", "程序正在后台运行，请保持网络连接")
            
    def stop_grab(self):
        """停止抢购"""
        if self.is_running:
            self.is_running = False
            self.start_button.configure(state="normal")
            self.stop_button.configure(state="disabled")
            self.test_button.configure(state="normal")
            self.status_indicator.configure(text="● 已停止", text_color=self.colors["error"])
            self.progress_bar.set(0)

            self.add_log("⏹ 抢购程序已停止")
            self.show_notification("抢购已停止", f"总共尝试 {self.attempt_count} 次，成功 {self.success_count} 次")
            
    def grab_worker(self):
        """抢购工作线程 - 集成原脚本逻辑"""
        global SUCCESS_FLAG, RANDOM_DELAY_MIN, RANDOM_DELAY_MAX, NEAR_HOUR_DELAY_MIN, NEAR_HOUR_DELAY_MAX, MAX_RETRIES

        # 从配置更新全局变量
        self.update_global_config()

        SUCCESS_FLAG = False  # 重置成功标志
        self.add_log("抢购程序已启动，开始智能抢购...")

        while self.is_running and not SUCCESS_FLAG:
            try:
                # 当前时间
                now = datetime.datetime.now()

                # 计算距离下一个整点的时间
                minutes_to_hour = 60 - now.minute - 1 if now.minute < 59 else 0
                seconds_to_hour = 60 - now.second if now.minute < 59 else 60 - now.second
                seconds_to_next_hour = minutes_to_hour * 60 + seconds_to_hour

                # 如果已经是整点时刻（00分00秒到00分10秒之间），立即进行密集尝试
                if now.minute == 0 and now.second < 10:
                    self.add_log("⏰ 整点时刻到，进行密集尝试！")
                    for attempt in range(MAX_RETRIES):
                        if not self.is_running:
                            break
                        self.update_stats(attempt=True)
                        if self.grab_subsidy_gui():
                            self.add_log("🎉 整点抢购成功，程序结束。")
                            self.update_stats(success=True)
                            self.stop_grab()
                            return
                        time.sleep(NEAR_HOUR_DELAY_MIN)  # 整点时使用最小延迟

                    # 整点密集尝试后，等待一个普通间隔
                    sleep_duration = random.uniform(RANDOM_DELAY_MIN, RANDOM_DELAY_MAX)
                    self.add_log(f"整点密集尝试结束，等待 {sleep_duration:.2f} 秒后继续...")
                    time.sleep(sleep_duration)
                    continue

                # 如果接近整点（距离不到60秒），精确计算等待时间并准确在整点发起请求
                elif seconds_to_next_hour < 60:
                    self.add_log(f"接近整点，距离整点还有 {seconds_to_next_hour} 秒")

                    # 如果距离整点非常近（小于10秒），使用精确等待
                    if seconds_to_next_hour <= 10:
                        # 精确等待至整点
                        self.add_log(f"精确等待 {seconds_to_next_hour} 秒到整点...")
                        time.sleep(seconds_to_next_hour)

                        # 整点立即密集发送请求
                        self.add_log("⏰ 整点到，立即开始密集请求！")
                        for attempt in range(MAX_RETRIES):
                            if not self.is_running:
                                break
                            self.update_stats(attempt=True)
                            if self.grab_subsidy_gui():
                                self.add_log("🎉 整点抢购成功，程序结束。")
                                self.update_stats(success=True)
                                self.stop_grab()
                                return
                            time.sleep(NEAR_HOUR_DELAY_MIN)  # 整点时使用最小延迟

                        # 尝试结束后等待
                        sleep_duration = random.uniform(RANDOM_DELAY_MIN, RANDOM_DELAY_MAX)
                        self.add_log(f"整点密集尝试结束，等待 {sleep_duration:.2f} 秒后继续...")
                        time.sleep(sleep_duration)
                        continue

                    else:
                        # 离整点还有一段时间但小于60秒，使用较短的等待时间
                        sleep_duration = random.uniform(NEAR_HOUR_DELAY_MIN, NEAR_HOUR_DELAY_MAX)
                        # 确保等待后还剩至少5秒进入精确等待阶段
                        if seconds_to_next_hour - sleep_duration < 5:
                            sleep_duration = seconds_to_next_hour - 5

                        self.add_log(f"接近整点，等待 {sleep_duration:.2f} 秒后继续...")
                        time.sleep(sleep_duration)
                        continue

                # 普通时段，单次尝试
                self.update_stats(attempt=True)
                if self.grab_subsidy_gui():
                    self.add_log("🎉 常规抢购成功，程序结束。")
                    self.update_stats(success=True)
                    self.stop_grab()
                    return

                # 普通时段等待
                sleep_duration = random.uniform(RANDOM_DELAY_MIN, RANDOM_DELAY_MAX)
                self.add_log(f"等待 {sleep_duration:.2f} 秒后重试... 距离下一个整点还有{seconds_to_next_hour}秒")
                time.sleep(sleep_duration)

            except Exception as e:
                self.add_log(f"主循环发生未知错误: {e}")
                time.sleep(2)  # 发生错误后等待2秒
                
    def add_log(self, message, level="INFO"):
        """添加日志消息"""
        timestamp = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        # 根据消息内容自动判断级别
        if "成功" in message or "✅" in message or "🎉" in message:
            level = "SUCCESS"
        elif "失败" in message or "错误" in message or "❌" in message:
            level = "ERROR"
        elif "警告" in message or "⚠️" in message:
            level = "WARNING"

        log_message = f"[{timestamp}] [{level}] {message}\n"

        # 添加到队列，由主线程处理UI更新
        self.log_queue.put(log_message)
        
    def process_log_queue(self):
        """处理日志队列"""
        try:
            while True:
                message = self.log_queue.get_nowait()

                # 存储到所有日志消息列表
                self.all_log_messages.append(message)

                # 限制存储的日志数量，避免内存过度使用
                if len(self.all_log_messages) > 1000:
                    self.all_log_messages = self.all_log_messages[-800:]  # 保留最新800条

                # 检查是否需要过滤显示
                should_display = True
                filter_text = self.log_filter_entry.get().lower() if hasattr(self, 'log_filter_entry') else ""
                level_filter = self.log_level_var.get() if hasattr(self, 'log_level_var') else "全部"

                if filter_text and filter_text not in message.lower():
                    should_display = False

                if level_filter != "全部" and should_display:
                    level_keywords = {
                        "信息": ["INFO", "信息", "正在", "等待", "启动", "停止"],
                        "警告": ["WARNING", "警告", "⚠️", "异常"],
                        "错误": ["ERROR", "错误", "失败", "❌"],
                        "成功": ["SUCCESS", "成功", "✅", "🎉"]
                    }
                    keywords = level_keywords.get(level_filter, [])
                    if not any(keyword in message for keyword in keywords):
                        should_display = False

                # 更新日志显示
                if should_display:
                    self.log_text.insert("end", message)
                    if self.auto_scroll_var.get() if hasattr(self, 'auto_scroll_var') else True:
                        self.log_text.see("end")

                # 更新信息显示（只显示最新几条，不受过滤影响）
                self.info_text.insert("end", message)
                # 保持信息区域只显示最新的内容
                lines = self.info_text.get("1.0", "end").split("\n")
                if len(lines) > 10:  # 只保留最新10行
                    self.info_text.delete("1.0", "end")
                    self.info_text.insert("1.0", "\n".join(lines[-10:]))
                self.info_text.see("end")

        except queue.Empty:
            pass

        # 继续监听
        self.root.after(100, self.process_log_queue)
        
    def save_config(self):
        """保存配置"""
        config = {
            "cookie": self.cookie_entry.get("1.0", "end-1c"),
            "user_agent": self.ua_entry.get("1.0", "end-1c"),
            "normal_delay_min": self.normal_delay_min.get(),
            "normal_delay_max": self.normal_delay_max.get(),
            "hour_delay_min": self.hour_delay_min.get(),
            "hour_delay_max": self.hour_delay_max.get(),
            "max_retries": self.max_retries.get(),
            "theme": "dark" if self.theme_switch.get() else "light",
            "window_geometry": self.root.geometry(),
            "auto_save_logs": True,
            "log_level": "INFO",
            "last_saved": datetime.datetime.now().isoformat()
        }

        try:
            # 创建配置目录（如果不存在）
            os.makedirs(os.path.dirname(self.config_file) if os.path.dirname(self.config_file) else ".", exist_ok=True)

            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, ensure_ascii=False, indent=2)
            messagebox.showinfo("成功", "配置已保存")
            self.add_log("配置文件已保存")
        except Exception as e:
            messagebox.showerror("错误", f"保存配置失败: {e}")
            self.add_log(f"保存配置失败: {e}")
            
    def load_config(self):
        """加载配置"""
        if os.path.exists(self.config_file):
            try:
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)

                # 加载配置到界面
                self.cookie_entry.delete("1.0", "end")
                self.cookie_entry.insert("1.0", config.get("cookie", ""))

                self.ua_entry.delete("1.0", "end")
                self.ua_entry.insert("1.0", config.get("user_agent", ""))

                self.normal_delay_min.delete(0, "end")
                self.normal_delay_min.insert(0, config.get("normal_delay_min", "5"))

                self.normal_delay_max.delete(0, "end")
                self.normal_delay_max.insert(0, config.get("normal_delay_max", "10"))

                self.hour_delay_min.delete(0, "end")
                self.hour_delay_min.insert(0, config.get("hour_delay_min", "0.5"))

                self.hour_delay_max.delete(0, "end")
                self.hour_delay_max.insert(0, config.get("hour_delay_max", "2"))

                self.max_retries.delete(0, "end")
                self.max_retries.insert(0, config.get("max_retries", "5"))

                # 加载主题设置
                theme = config.get("theme", "light")  # 默认浅色主题
                if theme == "dark":
                    self.theme_switch.select()
                    ctk.set_appearance_mode("dark")
                else:
                    self.theme_switch.deselect()
                    ctk.set_appearance_mode("light")

                # 加载窗口几何信息
                geometry = config.get("window_geometry", "900x700")
                try:
                    self.root.geometry(geometry)
                except:
                    self.root.geometry("900x700")  # 使用默认大小

                self.add_log("配置文件已加载")

            except Exception as e:
                messagebox.showerror("错误", f"加载配置失败: {e}")
                self.add_log(f"加载配置失败: {e}")
                self.reset_config()
        else:
            # 加载默认配置
            self.reset_config()
            
    def reset_config(self):
        """重置为默认配置"""
        # 从原脚本加载默认值
        self.cookie_entry.delete("1.0", "end")
        self.cookie_entry.insert("1.0", cookies)
        
        self.ua_entry.delete("1.0", "end")
        self.ua_entry.insert("1.0", user_agent)
        
        self.normal_delay_min.delete(0, "end")
        self.normal_delay_min.insert(0, str(RANDOM_DELAY_MIN))
        
        self.normal_delay_max.delete(0, "end")
        self.normal_delay_max.insert(0, str(RANDOM_DELAY_MAX))
        
        self.hour_delay_min.delete(0, "end")
        self.hour_delay_min.insert(0, str(NEAR_HOUR_DELAY_MIN))
        
        self.hour_delay_max.delete(0, "end")
        self.hour_delay_max.insert(0, str(NEAR_HOUR_DELAY_MAX))
        
        self.max_retries.delete(0, "end")
        self.max_retries.insert(0, str(MAX_RETRIES))
        
    def clear_logs(self):
        """清空日志"""
        self.log_text.delete("1.0", "end")
        self.info_text.delete("1.0", "end")
        self.all_log_messages.clear()
        self.add_log("日志已清空")

    def export_logs(self):
        """导出日志"""
        try:
            filename = filedialog.asksaveasfilename(
                defaultextension=".txt",
                filetypes=[("文本文件", "*.txt"), ("所有文件", "*.*")],
                initialname=f"抢购日志_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
            )
            if filename:
                with open(filename, 'w', encoding='utf-8') as f:
                    # 导出所有日志消息
                    for msg in self.all_log_messages:
                        f.write(msg)
                messagebox.showinfo("成功", f"日志已导出到: {filename}")
                self.add_log(f"日志已导出到: {filename}")
        except Exception as e:
            messagebox.showerror("错误", f"导出日志失败: {e}")

    def filter_logs(self, event=None):
        """过滤日志显示"""
        filter_text = self.log_filter_entry.get().lower()
        if not filter_text:
            # 如果没有过滤条件，显示所有日志
            self.refresh_log_display()
            return

        # 过滤日志消息
        filtered_messages = []
        for msg in self.all_log_messages:
            if filter_text in msg.lower():
                filtered_messages.append(msg)

        # 更新显示
        self.log_text.delete("1.0", "end")
        for msg in filtered_messages:
            self.log_text.insert("end", msg)

        if self.auto_scroll_var.get():
            self.log_text.see("end")

    def filter_logs_by_level(self, level):
        """按级别过滤日志"""
        if level == "全部":
            self.refresh_log_display()
            return

        level_keywords = {
            "信息": ["INFO", "信息", "正在", "等待", "启动", "停止"],
            "警告": ["WARNING", "警告", "⚠️", "异常"],
            "错误": ["ERROR", "错误", "失败", "❌"],
            "成功": ["SUCCESS", "成功", "✅", "🎉"]
        }

        keywords = level_keywords.get(level, [])
        if not keywords:
            return

        # 过滤日志消息
        filtered_messages = []
        for msg in self.all_log_messages:
            if any(keyword in msg for keyword in keywords):
                filtered_messages.append(msg)

        # 更新显示
        self.log_text.delete("1.0", "end")
        for msg in filtered_messages:
            self.log_text.insert("end", msg)

        if self.auto_scroll_var.get():
            self.log_text.see("end")

    def clear_filter(self):
        """清除过滤条件"""
        self.log_filter_entry.delete(0, "end")
        self.log_level_var.set("全部")
        self.refresh_log_display()

    def refresh_log_display(self):
        """刷新日志显示"""
        self.log_text.delete("1.0", "end")
        for msg in self.all_log_messages:
            self.log_text.insert("end", msg)

        if self.auto_scroll_var.get():
            self.log_text.see("end")
            
    def toggle_theme(self):
        """切换主题"""
        if self.theme_switch.get():
            ctk.set_appearance_mode("dark")
        else:
            ctk.set_appearance_mode("light")

    def test_connection(self):
        """测试网络连接"""
        def test_worker():
            try:
                self.add_log("正在测试网络连接...")
                # 测试h5st服务器连接
                h5st, _ = get_dynamic_h5st()
                if h5st:
                    self.add_log("✅ h5st服务器连接正常")
                else:
                    self.add_log("❌ h5st服务器连接失败")

                # 测试京东API连接
                import requests
                response = requests.get("https://api.m.jd.com", timeout=5)
                if response.status_code == 200:
                    self.add_log("✅ 京东API连接正常")
                else:
                    self.add_log(f"⚠️ 京东API响应异常: {response.status_code}")

            except Exception as e:
                self.add_log(f"❌ 连接测试失败: {e}")

        # 在后台线程中执行测试
        threading.Thread(target=test_worker, daemon=True).start()

    def update_time_display(self):
        """更新时间显示"""
        now = datetime.datetime.now()
        current_time = now.strftime("%Y-%m-%d %H:%M:%S")
        self.time_label.configure(text=f"当前时间: {current_time}")

        # 计算距离下个整点的时间
        minutes_to_hour = 60 - now.minute - 1 if now.minute < 59 else 0
        seconds_to_hour = 60 - now.second if now.minute < 59 else 60 - now.second
        total_seconds = minutes_to_hour * 60 + seconds_to_hour

        if total_seconds > 0:
            minutes = total_seconds // 60
            seconds = total_seconds % 60
            self.next_hour_label.configure(text=f"距离下个整点: {minutes:02d}:{seconds:02d}")

            # 更新进度条（接近整点时进度增加）
            if total_seconds <= 300:  # 5分钟内
                progress = 1 - (total_seconds / 300)
                self.progress_bar.set(progress)
            else:
                self.progress_bar.set(0)
        else:
            self.next_hour_label.configure(text="距离下个整点: 00:00")
            self.progress_bar.set(1)

        # 更新统计信息
        self.attempt_count_label.configure(text=f"尝试次数: {self.attempt_count}")
        self.success_count_label.configure(text=f"成功次数: {self.success_count}")

        # 继续更新
        self.root.after(1000, self.update_time_display)

    def update_stats(self, attempt=False, success=False):
        """更新统计信息"""
        if attempt:
            self.attempt_count += 1
        if success:
            self.success_count += 1

    def update_global_config(self):
        """从GUI配置更新全局变量"""
        global cookies, user_agent, RANDOM_DELAY_MIN, RANDOM_DELAY_MAX
        global NEAR_HOUR_DELAY_MIN, NEAR_HOUR_DELAY_MAX, MAX_RETRIES

        try:
            # 更新Cookie和User-Agent
            cookies = self.cookie_entry.get("1.0", "end-1c").strip()
            user_agent = self.ua_entry.get("1.0", "end-1c").strip()

            # 更新延迟配置
            RANDOM_DELAY_MIN = float(self.normal_delay_min.get() or "5")
            RANDOM_DELAY_MAX = float(self.normal_delay_max.get() or "10")
            NEAR_HOUR_DELAY_MIN = float(self.hour_delay_min.get() or "0.5")
            NEAR_HOUR_DELAY_MAX = float(self.hour_delay_max.get() or "2")
            MAX_RETRIES = int(self.max_retries.get() or "5")

            self.add_log("配置已更新到运行时")
        except ValueError as e:
            self.add_log(f"配置更新失败，使用默认值: {e}")

    def grab_subsidy_gui(self):
        """GUI版本的抢购函数"""
        global SUCCESS_FLAG
        if SUCCESS_FLAG:
            return True

        url = "https://api.m.jd.com/client.action?functionId=bindingQualification"
        data = build_request_data()

        # 记录时间戳更新信息
        current_time = datetime.datetime.now().strftime("%H:%M:%S")
        self.add_log(f"🕐 {current_time} 发起请求，时间戳已更新")

        try:
            # 将超时时间延长到10秒，以应对网络波动和h5st服务器可能的延迟
            response = requests.post(url, headers=headers, data=data, timeout=10)
            # 检查请求是否成功 (例如，状态码 200)
            response.raise_for_status()

            # 尝试解析JSON，如果失败则说明被风控
            try:
                result = response.json()
                self.add_log(f"抢购结果: {result}")

                # 修正成功判断逻辑：严格检查 'success' 字段是否为 True
                if result.get("success") is True:
                    message = result.get('message', '')
                    # 额外检查消息内容，确保真正成功
                    if any(keyword in message for keyword in ['失败', '回退', '错误', '异常']):
                        self.add_log(f"⚠️ 资格获取但后续失败: {message}")
                        return False
                    else:
                        self.add_log(f"🎉 恭喜！抢购成功! {message}")
                        SUCCESS_FLAG = True
                        return True
                else:
                    error_msg = result.get('message', '未知错误')
                    # 特殊处理回退情况
                    if '回退' in error_msg:
                        self.add_log(f"📋 资格预占成功但优惠券发放失败，已自动回退: {error_msg}")
                    else:
                        self.add_log(f"❌ 抢购失败: {error_msg}")
                    return False
            except json.JSONDecodeError:
                self.add_log(f"响应非JSON格式，可能参数已失效或被风控。响应内容(前500字符)如下:")
                self.add_log(response.text[:500])
                return False

        except requests.exceptions.RequestException as e:
            self.add_log(f"请求网络异常: {e}")
            return False
        except Exception as e:
            self.add_log(f"发生未知异常: {e}")
            return False

    def validate_config(self):
        """验证配置是否有效"""
        cookie = self.cookie_entry.get("1.0", "end-1c").strip()
        ua = self.ua_entry.get("1.0", "end-1c").strip()

        if not cookie:
            messagebox.showerror("配置错误", "请填写Cookie信息")
            self.notebook.set("配置")
            return False

        if not ua:
            messagebox.showerror("配置错误", "请填写User-Agent信息")
            self.notebook.set("配置")
            return False

        try:
            float(self.normal_delay_min.get() or "5")
            float(self.normal_delay_max.get() or "10")
            float(self.hour_delay_min.get() or "0.5")
            float(self.hour_delay_max.get() or "2")
            int(self.max_retries.get() or "5")
        except ValueError:
            messagebox.showerror("配置错误", "延迟时间和重试次数必须是有效数字")
            self.notebook.set("配置")
            return False

        return True

    def show_notification(self, title, message):
        """显示通知消息"""
        # 在这里可以添加系统通知或弹窗
        self.add_log(f"📢 {title}: {message}")

        # 可以选择性地显示弹窗
        # messagebox.showinfo(title, message)

    def show_help(self):
        """显示帮助信息"""
        help_text = """
京东抢资格助手 - 使用说明

【基本功能】
1. 监控页面：查看运行状态、控制抢购开始/停止
2. 配置页面：设置Cookie、User-Agent和时间参数
3. 日志页面：查看详细日志、过滤和导出日志
4. 关于页面：查看软件信息和快捷键

【使用步骤】
1. 在配置页面填写有效的Cookie和User-Agent
2. 根据需要调整延迟时间和重试次数
3. 点击"保存配置"保存设置
4. 在监控页面点击"开始抢购"
5. 程序会智能调度，在整点时刻进行密集尝试

【时间策略】
- 普通时段：使用较长的随机延迟
- 接近整点：使用较短的延迟
- 整点时刻：进行密集尝试

【注意事项】
- 请确保Cookie和User-Agent信息有效
- 保持网络连接稳定
- 建议在整点前几分钟启动程序
- 成功后程序会自动停止

【快捷键】
Ctrl+S: 保存配置    F5: 开始抢购
Ctrl+L: 清空日志    F6: 停止抢购
Ctrl+E: 导出日志    F9: 测试连接
        """

        # 创建帮助窗口
        help_window = ctk.CTkToplevel(self.root)
        help_window.title("使用帮助")
        help_window.geometry("600x500")
        help_window.transient(self.root)
        help_window.grab_set()

        # 帮助内容
        help_textbox = ctk.CTkTextbox(help_window, font=ctk.CTkFont(family="Microsoft YaHei", size=12))
        help_textbox.pack(fill="both", expand=True, padx=20, pady=20)
        help_textbox.insert("1.0", help_text)
        help_textbox.configure(state="disabled")

        # 关闭按钮
        close_button = ctk.CTkButton(help_window, text="关闭", command=help_window.destroy)
        close_button.pack(pady=10)

    def update_progress(self, progress):
        """更新进度条"""
        self.progress_bar.set(progress)

    def run(self):
        """运行应用程序"""
        # 设置窗口关闭事件
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
        self.root.mainloop()

    def on_closing(self):
        """窗口关闭事件处理"""
        if self.is_running:
            if messagebox.askokcancel("退出确认", "抢购程序正在运行，确定要退出吗？"):
                self.stop_grab()
                # 保存配置
                self.save_config()
                self.root.destroy()
        else:
            # 保存配置
            self.save_config()
            self.root.destroy()

def main():
    """主函数"""
    app = GrabQualificationGUI()
    app.run()

if __name__ == "__main__":
    main()
