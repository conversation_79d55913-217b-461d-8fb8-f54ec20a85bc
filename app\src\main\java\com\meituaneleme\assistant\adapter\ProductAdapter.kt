//package com.meituaneleme.assistant.adapter
//
//import android.content.Context
//import android.view.LayoutInflater
//import android.view.View
//import android.view.ViewGroup
//import android.widget.TextView
//import androidx.recyclerview.widget.RecyclerView
//import com.meituaneleme.assistant.R
//import com.meituaneleme.assistant.dialog.StoreSelectionDialog
//import com.google.android.material.button.MaterialButton
//import com.google.android.material.textfield.TextInputEditText
//
//class ProductAdapter(private val context: Context) : RecyclerView.Adapter<ProductAdapter.ViewHolder>() {
////    private var selectedStores: List<StoreSelectionAdapter.Store> = emptyList()
//    private var storeSelectionText: String = "(所有门店)"
//
//    // 模拟门店数据
////    private val mockStores = listOf(
////        StoreSelectionAdapter.Store("1", "北京店"),
////        StoreSelectionAdapter.Store("2", "上海店"),
////        StoreSelectionAdapter.Store("3", "广州店"),
////        StoreSelectionAdapter.Store("4", "深圳店")
////    )
//
//    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
//        val view = LayoutInflater.from(parent.context)
//            .inflate(R.layout.item_product, parent, false)
//        return ViewHolder(view)
//    }
//
//    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
//        holder.bind()
//    }
//
//    override fun getItemCount(): Int = 1 // 暂时只显示一个商品
//
//    inner class ViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
////        private val tvStoreSelection: TextView = itemView.findViewById(R.id.tv_store_selection)
////        private val btnSelectStores: MaterialButton = itemView.findViewById(R.id.btn_select_stores)
////        private val layoutPriceSettings: View = itemView.findViewById(R.id.layout_price_settings)
//        private val etOriginalPrice: TextInputEditText = itemView.findViewById(R.id.et_original_price_allstore)
//        private val etDiscountPrice: TextInputEditText = itemView.findViewById(R.id.et_discount_price_allstore)
//        private val btnSaveOriginalPrice: MaterialButton = itemView.findViewById(R.id.btn_save_original_price_allstore)
//        private val btnSaveDiscountPrice: MaterialButton = itemView.findViewById(R.id.btn_save_discount_price_allstore)
//
////        fun bind() {
//////            tvStoreSelection.text = storeSelectionText
////
//////            btnSelectStores.setOnClickListener {
//////                showStoreSelectionDialog()
//////            }
////
//////            layoutPriceSettings.setOnLongClickListener {
//////                showStoreSelectionDialog()
//////                true
//////            }
////
//////            btnSaveOriginalPrice.setOnClickListener {
//////                val price = etOriginalPrice.text.toString()
//////                if (price.isNotEmpty()) {
//////                    saveOriginalPrice(price)
//////                }
//////            }
//////
//////            btnSaveDiscountPrice.setOnClickListener {
//////                val price = etDiscountPrice.text.toString()
//////                if (price.isNotEmpty()) {
//////                    saveDiscountPrice(price)
//////                }
//////            }
////        }
//
////        private fun showStoreSelectionDialog() {
////            StoreSelectionDialog(context, mockStores) { selected ->
////                selectedStores = selected
////                storeSelectionText = when {
////                    selected.isEmpty() -> "(所有门店)"
////                    selected.size == mockStores.size -> "(所有门店)"
////                    selected.size <= 2 -> "(${selected.joinToString { it.name }})"
////                    else -> "(已选择${selected.size}家门店)"
////                }
////                tvStoreSelection.text = storeSelectionText
////                notifyDataSetChanged()
////            }.show()
////        }
//
////        private fun saveOriginalPrice(price: String) {
////            // TODO: 实现保存原价逻辑，根据selectedStores判断保存范围
////            if (selectedStores.isEmpty() || selectedStores.size == mockStores.size) {
////                // 保存到所有门店
////            } else {
////                // 保存到选中的门店
////                selectedStores.forEach { store ->
////                    // 保存到指定门店
////                }
////            }
////        }
////
////        private fun saveDiscountPrice(price: String) {
////            // TODO: 实现保存优惠价逻辑，根据selectedStores判断保存范围
////            if (selectedStores.isEmpty() || selectedStores.size == mockStores.size) {
////                // 保存到所有门店
////            } else {
////                // 保存到选中的门店
////                selectedStores.forEach { store ->
////                    // 保存到指定门店
////                }
////            }
////        }
//    }
//}