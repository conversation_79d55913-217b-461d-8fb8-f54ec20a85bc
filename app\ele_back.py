import base64
import hashlib
import json
import time
import requests


class Eleme:
    def __init__(self, cookie, shopname):
        print(f"开始初始化{shopname}的Eleme实例")
        self.sellerId = None
        self.storeId = None
        self.cookies = cookie
        self.activityId = "0"  # 默认值

        self.headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/86.0.4240.198 Safari/537.36",
            "Accept": "application/json",
            "Accept-Language": "zh-CN,zh;q=0.8,zh-TW;q=0.7,zh-HK;q=0.5,en-US;q=0.3,en;q=0.2",
            "Accept-Encoding": "gzip, deflate, br",
            "Referer": "https://boreas.ele.me/",
            "Content-type": "application/x-www-form-urlencoded",
            "x-ele-platform": "eb",
            "x-ele-eb-token": "NZZKN2MTAwMDE3MzExMDQ0NTEyT293emhpbjNQ;NZZKN2MTAwMDE3MzExMDQ0NTEyT293emhpbjNQ",
            "Origin": "https://boreas.ele.me",
            "Connection": "keep-alive",
            "Sec-Fetch-Dest": "empty",
            "Sec-Fetch-Mode": "no-cors",
            "Sec-Fetch-Site": "same-site",
            "TE": "trailers",
            "Pragma": "no-cache",
            "Cache-Control": "no-cache"
        }
        
        # 确保x-ele-eb-token存在
        if 'OUTER_AUTH_LOGIN' in self.cookies:
            self.headers['x-ele-eb-token'] = self.cookies['OUTER_AUTH_LOGIN'].replace('%3B', ';')
            print(f"从cookie获取到OUTER_AUTH_LOGIN: {self.cookies['OUTER_AUTH_LOGIN']}")
        else:
            print("OUTER_AUTH_LOGIN不存在于cookie中")

        self.accountinfo = {
            '星辰': {'account': 'eleme_15928716442',
                     'pass': 'Ofz96835'},
            '繁花觅': {'account': 'eleme_15828112127dnl',
                       'pass': 'Yuy01733'},
            '繁花里':{
                        'account': 'eleme_15928716442yQh',
                       'pass': 'Nmk57743'}

        }
        self.shopname = shopname
        self.baseinfo = {
            'item_rank': {
                'url': 'https://nrshop.ele.me/h5/mtop.ele.newretail.item.rank/1.0',
                'params': {
                    "jsv": "2.7.2",
                    "appKey": "********",
                    "api": "mtop.ele.newretail.item.rank",
                    "v": "1.0",
                    "type": "json",
                    "dataType": "json",
                    "valueType": "string"
                },  # 获取商品分类
            },
            'item_edit': {
                'url': 'https://nrshop.ele.me/h5/mtop.ele.newretail.item.edit/1.0',
                'params': {
                    "jsv": "2.7.2",
                    "appKey": "********",
                    "api": "mtop.ele.newretail.item.edit",
                    "v": "1.0",
                    "type": "json",
                    "dataType": "json",
                    "valueType": "string",
                },  # 获取商品分类
            },
            'customCategory_querylist': {
                'url': 'https://nrshop.ele.me/h5/mtop.ele.newretail.customcategory.querylist/1.0',
                'params': {
                    "jsv": "2.7.2",
                    "appKey": "********",
                    "api": "mtop.ele.newretail.itemtags.query",
                    "v": "1.0",
                    "type": "json",
                    "dataType": "json",
                    "valueType": "string"
                },  # 获取商品分类
            },
            'customCategory_create': {
                'url': 'https://nrshop.ele.me/h5/mtop.ele.newretail.customcategory.create/1.0',
                'params': {
                    "jsv": "2.7.2",
                    "appKey": "********",
                    "api": "mtop.ele.newretail.customCategory.create",
                    "v": "1.0",
                    "type": "json",
                    "dataType": "json",
                    "valueType": "string"
                },  # 获取商品分类
            },
            'customCategory_update': {
                'url': 'https://nrshop.ele.me/h5/mtop.ele.newretail.customcategory.update/1.0',
                'params': {
                    "jsv": "2.7.2",
                    "appKey": "********",
                    "api": "mtop.ele.newretail.customCategory.update",
                    "v": "1.0",
                    "type": "json",
                    "dataType": "json",
                    "valueType": "string"
                },  # 获取商品分类
            },
            'pageQuery': {
                "url": "https://nrshop.ele.me/h5/mtop.ele.newretail.item.pagequery/1.0/",
                "params": {
                    "jsv": "2.7.2",
                    "appKey": "********",
                    # "t": timestamp,
                    "api": "mtop.ele.newretail.item.pageQuery",
                    "v": "1.0",
                    "type": "json",
                    "dataType": "json",
                    "valueType": "string",
                    "data": '{"sellerId":"2215722782515","pageNum":888,"pageSize":999,"storeIds":"[\\"1078371359\\"]"}'
                },
                "params2": {
                    "jsv": "2.7.2",
                    "appKey": "********",
                    # "t": timestamp,
                    "api": "mtop.ele.newretail.item.pageQuery",
                    "v": "1.0",
                    "type": "json",
                    "dataType": "json",
                    "valueType": "string",
                    "data": '{"sellerId":"2215722782515","pageNum":888,"pageSize":999,"storeIds":"[\\"1078371359\\"]"}'
                },
            },
            'querycount': {
                "url": "https://nrshop.ele.me/h5/mtop.ele.newretail.item.querycount/1.0/",
                "params": {
                    "jsv": "2.7.2",
                    "appKey": "********",
                    # "t": timestamp,
                    "api": "mtop.ele.newretail.item.queryCount",
                    "v": "1.0",
                    "type": "json",
                    "dataType": "json",
                    "valueType": "string",
                    "data": '{"sellerId":"2215722782515","pageNum":2,"pageSize":20,"storeIds":"[\\"1078371359\\"]"}'

                },
            },
            'querybycustomcatpaging': {
                "url": "https://nrshop.ele.me/h5/mtop.ele.newretail.item.querybycustomcatpaging/1.0/",
                "params": {
                    "jsv": "2.7.0",
                    "appKey": "********",
                    # "t": "1702129065268",
                    # "sign": "d4e13b5608d444485707c393b54b3570",
                    "api": "mtop.ele.newretail.item.queryByCustomCatPaging",
                    "v": "1.0",
                    "type": "json",
                    "dataType": "json",
                    "valueType": "string"}
            },
            'queryPageShopSkuList': {
                "url": "https://nrshop.ele.me/h5/mtop.me.ele.merchant.special.pc.querypageshopskulist/1.0/",
                "params": {
                    "jsv": "2.7.0",
                    "appKey": "********",
                    # "t": "1702129065268",
                    # "sign": "d4e13b5608d444485707c393b54b3570",
                    "api": "mtop.me.ele.merchant.special.pc.queryPageShopSkuList",
                    "v": "1.0",
                    "type": "json",
                    "dataType": "json",
                    "valueType": "string"}

            },
            'editsku': {
                "url": "https://nrshop.ele.me/h5/mtop.me.ele.merchant.special.pc.editsku/1.0/",
                "params": {
                    "jsv": "2.7.0",
                    "appKey": "********",
                    # "t": "*************",
                    # "sign": "159db3fd3c02a1d060f524321e5c6551",
                    "api": "mtop.me.ele.merchant.special.pc.editSku",
                    "v": "1.0",
                    "type": "json",
                    "dataType": "json",
                    "valueType": "string"
                }
            },
            'querypageactivitycreatelist': {
                "url": "https://nrshop.ele.me/h5/mtop.me.ele.merchant.common.querypageactivitycreatelist/1.0",
                "params": {
                    "jsv": "2.7.0",
                    "appKey": "********",
                    # "t": "*************",
                    # "sign": "159db3fd3c02a1d060f524321e5c6551",
                    "api": "mtop.me.ele.merchant.Common.queryPageActivityCreateList",
                    "v": "1.0",
                    "type": "json",
                    "dataType": "json",
                    "valueType": "string"
                }
            },  # 获取折扣商品的activityId
            'querypageshopskulist': {
                "url": "https://nrshop.ele.me/h5/mtop.me.ele.merchant.special.pc.querypageshopskulist/1.0/",
                "params": {
                    "jsv": "2.7.0",
                    "appKey": "********",
                    # "t": "*************",
                    # "sign": "159db3fd3c02a1d060f524321e5c6551",
                    "api": "mtop.me.ele.merchant.special.pc.queryPageShopSkuList",
                    "v": "1.0",
                    "type": "json",
                    "dataType": "json",
                    "valueType": "string"
                }
            },  # 获取折扣商品的skuid
            'getShopUserInfo': {
                "url": "https://nrshop.ele.me/h5/mtop.ele.newretail.ebai.accountreadmtopservice.getshopuserinfo/1.0/",
                "params": {
                    "jsv": "2.7.0",
                    "appKey": "********",
                    "api": "mtop.ele.newretail.ebai.AccountReadMtopService.getShopUserInfo",
                    "v": "1.0",
                    "H5Request": "true",
                    "type": "originaljson",
                    "dataType": "json",
                    "data": "{}"

                }
            },
            'getSkuCommodity': {
                'url': 'https://nrshop.ele.me/h5/mtop.me.ele.merchant.rt.getskucommodity/1.0/',
                "params": {
                    "jsv": "2.7.2",
                    "appKey": "********",
                    "api": "mtop.me.ele.merchant.rt.getSkuCommodity",
                    "v": "1.0",
                    # "H5Request": "true",
                    "type": "originaljson",
                    "dataType": "json",
                    "valueType": "json",

                }
            },
            'addSku': {
                'url': 'https://nrshop.ele.me/h5/mtop.me.ele.merchant.special.pc.addsku/1.0/',
                "params": {
                    "jsv": "2.7.0",
                    "appKey": "********",
                    "api": "mtop.me.ele.merchant.special.pc.addSku",
                    "v": "1.0",
                    "type": "json",
                    "dataType": "json",
                    "valueType": "string",

                }
            },
            'edit': {
                'url': 'https://nrshop.ele.me/h5/mtop.ele.newretail.item.edit/1.0/',
                "params": {
                    "jsv": "2.7.2",
                    "appKey": "********",
                    "api": "mtop.ele.newretail.item.edit",
                    "v": "1.0",
                    "type": "json",
                    "dataType": "json",
                    "valueType": "string",
                    "data_price": '{"sellerId":"2215722782515","itemId":itemId1,"storeId":1078371359,"isWeight":false,"weightType":null,"price":price1}',
                    "data_quantity": '{"sellerId":"2215722782515","itemId":itemId1,"storeId":1078371359,"isWeight":false,"weightType":null,"price":quantity1}'

                }
            },
            'deleteActivitySku': {
                'url': 'https://nrshop.ele.me/h5/mtop.me.ele.merchant.special.pc.deleteactivitysku/1.0/',
                "params": {
                    "jsv": "2.7.0",
                    "appKey": "********",
                    # "t": "1702726572120",
                    # "sign": "eb8274b14ddc3a30ab22c87707ae84bd",
                    "api": "mtop.me.ele.merchant.special.pc.deleteActivitySku",
                    "v": "1.0",
                    "type": "json",
                    "dataType": "json",
                    "valueType": "string"
                }
            },
            'batchedit': {
                'url': 'https://nrshop.ele.me/h5/mtop.ele.newretail.item.batchedit/1.0',
                "params": {
                    "jsv": "2.7.2",
                    "appKey": "********",
                    # "t": "1702726572120",
                    # "sign": "eb8274b14ddc3a30ab22c87707ae84bd",
                    "api": "mtop.ele.newretail.item.batchEdit",
                    "v": "1.0",
                    "type": "json",
                    "dataType": "json",
                    "valueType": "string"
                }
            },
            'batchshelf': {
                'url': 'https://nrshop.ele.me/h5/mtop.ele.newretail.item.batchshelf/1.0',
                'params': {
                    "jsv": "2.7.2",
                    "appKey": "********",
                    # "t": "1715178147396",
                    # "sign": "291c78b3c1439945f94b868fc22dc828",
                    "api": "mtop.ele.newretail.item.batchShelf",
                    "v": "1.0",
                    "type": "json",
                    "dataType": "json",
                    "valueType": "string"
                }
            },
            'searchListPage': {''
                               'url': 'https://nrshop.ele.me/h5/mtop.ele.newretail.item.pagequery/1.0',
                               'params': {
                                   "jsv": "2.7.2",
                                   "appKey": "********",
                                   "t": "1715517136255",
                                   "sign": "a64cd1c044522f5beecfc607e6b5fbd5",
                                   "api": "mtop.ele.newretail.item.pageQuery",
                                   "v": "1.0",
                                   "type": "json",
                                   "dataType": "json",
                                   "valueType": "string",
                                   "data": "{\"sellerId\":\"sellerId2\",\"pageNum\":1,\"pageSize\":20,\"existedQuantity\":false,\"status\":\"[0,1]\",\"storeIds\":\"[\\\"storeIds2\\\"]\"}"
                               }
                               },
            'update':{
                'url':'https://nrshop.ele.me/h5/mtop.ele.newretail.channel.item.update/1.0/',
                'params': {
                    "jsv": "2.7.2",
                    "appKey": "********",
                    "api": "mtop.ele.newretail.channel.item.update",
                    "v": "1.0",
                    "type": "json",
                    "dataType": "json",
                    "valueType": "string"
                }
            }

        }

        # 检查cookie的有效性
        # self.check_cookie()

        # 在尝试获取商户信息之前设置默认值
        if '星辰' in self.shopname:
            self.default_sellerId = "2215722782515"
            self.default_storeId = "1078371359"
        elif '繁花里' in self.shopname:
            self.default_sellerId = "2201038469034"
            self.default_storeId = "1095947714"
        elif '繁花觅' in self.shopname:
            self.default_sellerId = "2201273530391"
            self.default_storeId = "1031582541"
        
        # 尝试获取商户信息
        try:
            self.sellerId, self.storeId = self.getShopUserInfo()
            if not self.sellerId or not self.storeId:
                print(f"无法通过API获取商户信息，使用默认值")
                if hasattr(self, 'default_sellerId') and hasattr(self, 'default_storeId'):
                    self.sellerId = self.default_sellerId
                    self.storeId = self.default_storeId
                    print(f"使用默认值 - sellerId: {self.sellerId}, storeId: {self.storeId}")
        except Exception as e:
            print(f"获取商户信息时出错: {e}")
            # 使用默认值
            if hasattr(self, 'default_sellerId') and hasattr(self, 'default_storeId'):
                self.sellerId = self.default_sellerId
                self.storeId = self.default_storeId
                print(f"使用默认值 - sellerId: {self.sellerId}, storeId: {self.storeId}")
        
        # 初始化activityId，确保即使没有折扣活动也有默认值
        self.activityId = "0"
        
        # 只有在成功获取商户信息的情况下尝试获取折扣活动
        if self.sellerId and self.storeId:
            try:
                data = {
                    "data": '{"query":"{\\"orderType\\":2,\\"pageIndex\\":1,\\"pageSize\\":20,\\"activityType\\":[-1],\\"createRole\\":-1,\\"activityStatus\\":1}"}'
                }
                response = self.get_data_post('querypageactivitycreatelist', data)
                print(f"折扣活动API响应: {json.dumps(response)}")
                
                if ('data' in response and 'data' in response['data'] and 
                    'list' in response['data']['data'] and response['data']['data']['list']):
                    activities = response['data']['data']['list']
                    for activity in activities:
                        if activity.get('activityType') == 101:
                            self.activityId = str(activity.get('activityId', '0'))
                            print(f"获取到折扣活动ID: {self.activityId}")
                            break
                    else:
                        print('未找到activityType=101的折扣活动')
                else:
                    print('获取折扣活动列表失败或列表为空')
            except Exception as e:
                import traceback
                print(f'获取折扣活动失败: {e}')
                print(traceback.format_exc())
                
        print(f"{shopname}的Eleme实例初始化完成 - sellerId: {self.sellerId}, storeId: {self.storeId}, activityId: {self.activityId}")

    def get_sign(self, input_string):
        # 要加密的消息
        message = input_string

        # 创建 MD5 散列对象
        hash_object = hashlib.md5()

        # 准备要加密的数据，这里需要将字符串转换为字节
        message_data = message.encode('utf-8')

        # 更新散列对象
        hash_object.update(message_data)

        # 获取十六进制格式的散列值
        md5_hash = hash_object.hexdigest()

        return md5_hash

    def check_cookie(self):
        # 检查cookie的有效性
        respon = self.get_data('getShopUserInfo')
        if respon['ret'][0] in ['FAIL_SYS_TOKEN_EXOIRED::令牌过期', 'FAIL_SYS_SESSION_EXPIRED::Session过期',
                                'FAIL_SYS_SESSION_SERVICE_FAULT::会话服务故障']:
            print(self.shopname, 'cookie无效,重新获取')
            # 将cookie失效的门店修改为self.shopname + '(cookie失效)'
            for i in range(changediscounwindow.store_list.count()):
                if changediscounwindow.store_list.itemText(i) == self.shopname:
                    changediscounwindow.store_list.setItemText(i, self.shopname + '(cookie失效)')
                    return False

        else:
            print(self.shopname, 'cookie有效')
            return True

    def update_store_cookie(self, file_path, store_name, new_tk, new_tk_enc, WMUSS, WMSTOKEN, OUTER_AUTH_LOGIN):
        with open(file_path, 'r', encoding='utf-8') as file:
            lines = file.readlines()

        updated_lines = []
        for line in lines:
            if store_name in line:
                # 找到对应门店的行并更新
                parts = line.split('】')[1].split(';')
                updated_parts = []
                for part in parts:
                    if '_m_h5_tk=' in part:
                        updated_parts.append(f'_m_h5_tk={new_tk}')
                    elif '_m_h5_tk_enc=' in part:
                        updated_parts.append(f'_m_h5_tk_enc={new_tk_enc}')
                    elif 'WMUSS=' in part:
                        updated_parts.append(f'WMUSS={WMUSS}')
                    elif 'WMSTOKEN=' in part:
                        updated_parts.append(f'WMSTOKEN={WMSTOKEN}')
                    elif 'OUTER_AUTH_LOGIN=' in part:
                        updated_parts.append(f'OUTER_AUTH_LOGIN={OUTER_AUTH_LOGIN}')
                    else:
                        updated_parts.append(part)
                updated_line = store_name + '】' + ';'.join(updated_parts).strip() + '\n'
                updated_lines.append(updated_line)
            else:
                updated_lines.append(line)

        with open(file_path, 'w', encoding='utf-8') as file:
            file.writelines(updated_lines)

    def get_timestamp(self):
        # 获取当前时间戳
        t = int(time.time() * 1000)
        return t

    def get_data_post(self, action, data=None):
        data = data
        max_retries = 2
        retry_count = 0
        
        while retry_count <= max_retries:
            try:
                timestamp = self.get_timestamp()
                try:
                    _m_h5_tk, _m_h5_tk_enc = self.get_m_h5_tk(self.cookies.get('WMUSS', ''))
                    self.cookies['_m_h5_tk'] = _m_h5_tk
                    self.cookies['_m_h5_tk_enc'] = _m_h5_tk_enc
                    
                    # 提取token的第一部分
                    token_part = _m_h5_tk.split("_")[0]
                    e = f'{token_part}&{timestamp}&{self.baseinfo[action]["params"]["appKey"]}&{data["data"]}'
                except Exception as e:
                    print(f"构造参数时出错: {e}")
                    # 使用默认token
                    e = f'default&{timestamp}&{self.baseinfo[action]["params"]["appKey"]}&{data["data"]}'
                
                sign = self.get_sign(e)
                self.baseinfo[action]['params']['sign'] = sign
                self.baseinfo[action]['params']['t'] = timestamp
                
                print(f"POST请求 {action} - URL: {self.baseinfo[action]['url']}")
                print(f"POST请求参数: {self.baseinfo[action]['params']}")
                
                response = requests.post(
                    self.baseinfo[action]['url'], 
                    headers=self.headers, 
                    cookies=self.cookies,
                    params=self.baseinfo[action]['params'], 
                    data=data,
                    timeout=15
                )
                
                # 检查响应
                if response.status_code != 200:
                    print(f"请求失败，状态码: {response.status_code}")
                    if retry_count < max_retries:
                        retry_count += 1
                        print(f"重试 ({retry_count}/{max_retries})...")
                        continue
                    else:
                        return {"ret": ["FAIL::请求失败"], "data": {}}
                
                # 尝试解析JSON响应
                try:
                    json_response = response.json()
                    return json_response
                except ValueError:
                    print(f"无法解析JSON响应: {response.text[:200]}")
                    if retry_count < max_retries:
                        retry_count += 1
                        print(f"重试 ({retry_count}/{max_retries})...")
                        continue
                    else:
                        return {"ret": ["FAIL::无效的响应"], "data": {}}
                
            except Exception as e:
                print(f"请求时出错: {e}")
                if retry_count < max_retries:
                    retry_count += 1
                    print(f"重试 ({retry_count}/{max_retries})...")
                    continue
                else:
                    return {"ret": ["FAIL::请求异常"], "data": {}}
        
        return {"ret": ["FAIL::超过最大重试次数"], "data": {}}

    def get_data(self, action, count=None, pageNum=None, itemID=None, price=None, quantity=None,searchflag=None, productname=None,
                 data=None):
        max_retries = 2
        retry_count = 0
        
        while retry_count <= max_retries:
            try:
                # 准备请求参数
                if action == 'item_edit':
                    self.baseinfo[action]["params"]["data"] = data
                if action == 'edit':
                    if price:
                        print("执行价格")
                        self.baseinfo[action]["params"]["data"] = self.baseinfo[action]["params"]["data_price"].replace('itemId1',str(itemID)).replace('price1', price)
                    elif quantity:
                        print("执行库存")
                        self.baseinfo[action]["params"]["data"] = self.baseinfo[action]["params"]["data_quantity"].replace('itemId1',str(itemID)).replace('quantity1', quantity)
                        print("内容是：",self.baseinfo[action]["params"]["data"])
                if self.sellerId and self.storeId:
                    if '2215722782515' in str(self.baseinfo[action].get("params", {}).get("data", "")):
                        self.baseinfo[action]["params"]["data"] = self.baseinfo[action]["params"]["data"].replace(
                            '2215722782515', self.sellerId).replace('1078371359', self.storeId)
                
                timestamp = self.get_timestamp()
                if action == 'pageQuery':
                    if self.baseinfo[action]["params"].get("data"):
                        self.baseinfo[action]["params"]["data"] = self.baseinfo[action]["params"]["data"].replace('888',
                                                                                                          str(pageNum)).replace(
                        '999', str(count))
                    # 处理搜索标志
                    if searchflag:
                        try:
                            params_str = json.loads(
                                '{"sellerId":"2215722782515","storeIds":"[\\"1078371359\\"]","titleOrBarCode":"【爱丽丝梦境】11朵玫瑰混搭洋桔梗喷泉草圆形鲜花花束 送闺蜜同事老婆礼物 花店送花上门","titleWithoutSplitting":true,"pageNum":1,"pageSize":20}')
                            params_str['pageNum'] = 1
                            params_str['pageSize'] = 20
                            params_str['titleOrBarCode'] = productname
                            params_str['sellerId'] = self.sellerId
                            params_str['storeIds'] = f'["{self.storeId}"]'
                            params_str = json.dumps(params_str)
                            self.baseinfo[action]["params2"]["data"] = params_str
                            
                            try:
                                _m_h5_tk, _m_h5_tk_enc = self.get_m_h5_tk(self.cookies.get('WMUSS', ''))
                                self.cookies['_m_h5_tk'] = _m_h5_tk
                                self.cookies['_m_h5_tk_enc'] = _m_h5_tk_enc
                                token_part = _m_h5_tk.split("_")[0]
                                e = f'{token_part}&{timestamp}&{self.baseinfo[action]["params2"]["appKey"]}&{self.baseinfo[action]["params2"]["data"]}'
                            except Exception as err:
                                print(f"构造参数时出错: {err}")
                                e = f'default&{timestamp}&{self.baseinfo[action]["params2"]["appKey"]}&{self.baseinfo[action]["params2"]["data"]}'
                        except Exception as e:
                            print(f"处理searchflag参数时出错: {e}")
                            return {"ret": ["FAIL::处理搜索参数失败"], "data": {"total": 0, "data": []}}
                    else:
                        try:
                            if self.baseinfo[action]["params"].get("data"):
                                params_str = json.loads(self.baseinfo[action]["params"]["data"])
                                params_str['pageNum'] = pageNum
                                params_str['pageSize'] = count
                                params_str = json.dumps(params_str)
                                self.baseinfo[action]["params"]["data"] = params_str
                        except Exception as e:
                            print(f"处理pageQuery参数时出错: {e}")
                            return {"ret": ["FAIL::处理分页参数失败"], "data": {"total": 0, "data": []}}

                # 如果e已经在searchflag处理中设置，则使用它
                if 'e' not in locals():
                    try:
                        _m_h5_tk, _m_h5_tk_enc = self.get_m_h5_tk(self.cookies.get('WMUSS', ''))
                        self.cookies['_m_h5_tk'] = _m_h5_tk
                        self.cookies['_m_h5_tk_enc'] = _m_h5_tk_enc
                        
                        data_str = self.baseinfo[action]["params"].get("data", "{}")
                        token_part = _m_h5_tk.split("_")[0]
                        e = f'{token_part}&{timestamp}&{self.baseinfo[action]["params"]["appKey"]}&{data_str}'
                    except Exception as err:
                        print(f"构造参数时出错: {err}")
                        data_str = self.baseinfo[action]["params"].get("data", "{}")
                        e = f'default&{timestamp}&{self.baseinfo[action]["params"]["appKey"]}&{data_str}'
                
                sign = self.get_sign(e)
                
                # 设置请求参数
                if searchflag and 'e' in locals():
                    self.baseinfo[action]['params2']['sign'] = sign
                    self.baseinfo[action]['params2']['t'] = timestamp
                    params = self.baseinfo[action]['params2']
                else:
                    self.baseinfo[action]['params']['sign'] = sign
                    self.baseinfo[action]['params']['t'] = timestamp
                    params = self.baseinfo[action]['params']
                
                url = self.baseinfo[action]['url']
                print(f"GET请求 {action} - URL: {url}")
                print(f"GET请求参数: {params}")
                
                # 发送请求
                response = requests.get(url, headers=self.headers, cookies=self.cookies, params=params, timeout=15)
                
                # 检查响应
                if response.status_code != 200:
                    print(f"请求失败，状态码: {response.status_code}")
                    if retry_count < max_retries:
                        retry_count += 1
                        print(f"重试 ({retry_count}/{max_retries})...")
                        continue
                    else:
                        return {"ret": ["FAIL::请求失败"], "data": {"total": 0, "data": []}}
                
                # 尝试解析JSON响应
                try:
                    json_response = response.json()
                    if action == 'pageQuery' and 'data' not in json_response:
                        json_response['data'] = {"total": 0, "data": []}
                    return json_response
                except ValueError:
                    print(f"无法解析JSON响应: {response.text[:200]}")
                    if retry_count < max_retries:
                        retry_count += 1
                        print(f"重试 ({retry_count}/{max_retries})...")
                        continue
                    else:
                        return {"ret": ["FAIL::无效的响应"], "data": {"total": 0, "data": []}}
            
            except Exception as e:
                print(f"请求时出错: {e}")
                if retry_count < max_retries:
                    retry_count += 1
                    print(f"重试 ({retry_count}/{max_retries})...")
                    continue
                else:
                    return {"ret": ["FAIL::请求异常"], "data": {"total": 0, "data": []}}
        
        return {"ret": ["FAIL::超过最大重试次数"], "data": {"total": 0, "data": []}}

    def change_discount_price(self, skuid, newprice, activityDayLimit):
        # 获取折扣商品名称,表格当前行中第一列的内容

        newprice = str(newprice)
        upc = skuid
        # 获取折扣商品的skuid
        data = {
            "data": '{"query":"{\\"activityId\\":\\"activityId2\\",\\"upc\\":\\"wbzsx1459397914387297467\\",\\"verifyStatusList\\":[1,2,3],\\"pageIndex\\":1,\\"pageSize\\":20,\\"upcName\\":\\"\\"}"}'
        }
        data['data'] = data['data'].replace('wbzsx1459397914387297467', upc).replace('activityId2', self.activityId)
        response = self.get_data_post('querypageshopskulist', data)
        if response['ret'][0] == 'SUCCESS::调用成功':
            try:
                newdata = response['data']['data']["list"][0]
            except:

                return '未找到该商品'
            skuid = str(newdata['eleItemId'])
            stock = str(newdata['stock'])
            activityLimit = str(newdata['activityLimit'])
            activityDayLimit = activityDayLimit

            dayStock = str(newdata['dayStock'])

            # 上传新价格
            data = {
                "data": '{"command":"{\\"activityId\\":\\"6000001521332840\\",\\"specialPrice\\":228,\\"stock\\":9999,\\"dayStock\\":999,\\"activityDayLimit\\":888,\\"activityLimit\\":-1,\\"skuList\\":[\\"16818845572236576\\"]}"}'

            }
            data['data'] = data['data'].replace('6000001521332840', self.activityId).replace('228', newprice).replace(
                '9999', stock).replace('999', dayStock).replace('888', activityDayLimit).replace('16818845572236576',skuid).replace('-1', activityLimit)
            a = self.get_data_post('editsku', data)
            # print(a)
            return a['ret'][0]
        else:
            return response['ret'][0]

    def searchListPage(self):
        self.baseinfo['searchListPage']['params']['data'] = self.baseinfo['searchListPage']['params']['data'].replace(
            'sellerId2', self.sellerId).replace('storeIds2', self.storeId)
        re = self.get_data('searchListPage')
        return re

    def addsku(self, productname, discountprice=0):
        # 获取SKUID
        data = {
            "data": '{"query":"{\\"activityId\\":activityId2,\\"pageIndex\\":1,\\"pageSize\\":10,\\"upcName\\":\\"upcName2\\"}"}'
        }
        data['data'] = data['data'].replace('activityId2', self.activityId).replace('upcName2',
                                                                                    productname.replace('／', ' '))
        skuid_salePrice = self.get_data_post('getSkuCommodity', data=data)
        skuid = str(skuid_salePrice['data']['data']['skuList'][0]['skuId'])
        salePrice = str(skuid_salePrice['data']['data']['skuList'][0]['salePrice'])
        upc = str(skuid_salePrice['data']['data']['skuList'][0]['upc'])
        # 通过addsku添加折扣商品
        data = {
            "data": '{"activityId":"activityId2","command":"[{\\"skuId\\":\\"skuId2\\",\\"upc\\":\\"upc2\\",\\"specialPrice\\":salePrice2,\\"stock\\":-1,\\"dayStock\\":1000,\\"activityLimitType\\":2,\\"activityDayLimit\\":1,\\"activityLimit\\":-1}]"}'}
        data['data'] = data['data'].replace('activityId2', self.activityId).replace('skuId2', skuid).replace('upc2',
                                                                                                             upc).replace(
            'salePrice2', str(salePrice))
        self.get_data_post('addSku', data=data)
        # 通过editsku修改折扣商品
        specialPrice = discountprice
        data = {
            'data': '{"command":"{\\"activityId\\":\\"activityId2\\",\\"specialPrice\\":specialPrice2,\\"stock\\":-1,\\"dayStock\\":1000,\\"activityDayLimit\\":1,\\"activityLimit\\":-1,\\"skuList\\":[\\"skuList2\\"]}"}'}
        data['data'] = data['data'].replace('activityId2', self.activityId).replace('skuList2', skuid).replace(
            'specialPrice2', str(specialPrice))
        result = self.get_data_post('editsku', data=data)
        return result['ret'][0]

    def editprice(self, itemID, price):
        # 获取itemID
        # itemID='************'
        price = str(price)
        result = self.get_data('edit', itemID=itemID, price=price)
        return result['ret'][0]

    def editquantity(self, itemID, quantity):
        # 获取itemID
        # itemID='************'
        quantity = str(quantity)
        print("进入editquantity方法")
        result = self.get_data('edit', itemID=itemID, quantity=quantity)
        return result['ret'][0]

    def update(self, itemID, data,cateId):
        # 结构化的数据
        item_edit_data = {
            "itemId": itemID,
            "fromChannel": "ITEM_EDIT",
            "sellerId": self.sellerId,
            "storeId": self.storeId,
            "cateId": cateId,
            "images": []
        }
        cnt = 0
        for i in data:
            if cnt == 0:
                item_edit_data['images'].append({"url": i, "isMaster": True})
            else:
                item_edit_data['images'].append({"url": i, "isMaster": False})
            cnt+=1

        # 将结构化数据转换为请求所需的格式
        data = {
            "data": json.dumps({"itemEditDTO": json.dumps(item_edit_data)})
        }
        response =self.get_data_post('update', data=data)
        return response['ret'][0]

    def deleteActivitySku(self, upc):
        data = {
            "data": '{"query":"{\\"activityId\\":\\"activityId2\\",\\"upc\\":\\"wbzsx1459397914387297467\\",\\"verifyStatusList\\":[1,2,3],\\"pageIndex\\":1,\\"pageSize\\":20,\\"upcName\\":\\"\\"}"}'
        }
        data['data'] = data['data'].replace('wbzsx1459397914387297467', upc).replace('activityId2', self.activityId)
        response = self.get_data_post('querypageshopskulist', data)
        if response['ret'][0] == 'SUCCESS::调用成功':
            try:
                newdata = response['data']['data']["list"][0]
            except:
                # QMessageBox.information(None, '提示', '未找到该商品', QMessageBox.Yes)
                return '未找到该商品'
            skuid = str(newdata['eleItemId'])

            data = {
                'data': '{"command":"{\\"activityId\\":\\"6000001521332840\\",\\"idList\\":[\\"16817874242212649\\"]}"}'}
            data['data'] = data['data'].replace('6000001521332840', self.activityId).replace('16817874242212649', skuid)
            response = self.get_data_post('deleteActivitySku', data)
            # if response['ret'][0]=='SUCCESS::调用成功':
            return response['ret'][0]

    def batchEdit(self, tag_name_map, skuids):  # 修改分类
        customCategoryList = []
        skuids_list = []
        dtoList = []
        for tag_name in tag_name_map:
            customCategoryList.append({"customCategoryId": tag_name_map[tag_name], "customCategoryName": tag_name})
        # 如果skuids是字符串，转换成列表

        if isinstance(skuids, int):
            skuids_list.append(skuids)
        else:
            skuids_list = skuids

        for skuids in skuids_list:
            dtoList.append({
                "sellerId": self.sellerId,
                "customCategoryList": customCategoryList,
                "itemId": skuids,
                "storeId": self.storeId
            })

        data = {"data": json.dumps({"dtoList": json.dumps(dtoList, ensure_ascii=False)}, ensure_ascii=False)}

        response = self.get_data_post('batchedit', data)
        # print(response)
        return response['ret'][0]

    def customCategory_querylist(self):  # 获取商品分类
        info = {"sellerId": self.sellerId, "storeId": self.storeId, "bizType": 2, "returnAllChild": "true"}
        data = {"data": json.dumps({"req": json.dumps(info)})}
        response = self.get_data_post('customCategory_querylist', data)
        # print(response)
        return response

    def customCategory_create(self, name):  # 创建商品分类
        info = {"sellerId": self.sellerId, "storeId": self.storeId, "bizType": 2,
                "customCategory": {"depth": 1, "customCategoryName": name, "image": None}}
        data = {"data": json.dumps({"req": json.dumps(info)})}

        response = self.get_data_post('customCategory_create', data)
        # print(response)
        return response

    def item_rank(self, customCategoryId, bizDtoList):
        info = {"bizType": 2, "sellerId": self.sellerId, "storeId": self.storeId, "customCategoryId": customCategoryId,
                "bizDtoList": bizDtoList}
        data = {"data": json.dumps({"req": json.dumps(info)})}
        response = self.get_data_post('item_rank', data)
        # print(response)
        return response

    def batchshelf(self, itemId, status=None):

        # data = {
        #     "data": json.dumps({
        #         "sellerId": self.sellerId,
        #         "list": '["{\\"itemId\\":756242963504,\\"storeId\\":1078371359,\\"status\\":0"]'
        #     })
        # }
        data = {
            "data": json.dumps({
                "sellerId": self.sellerId,
                "list": '["{\\"itemId\\":793729439502,\\"storeId\\":1078371359,\\"status\\":status2}"]'
            })
        }
        data['data'] = data['data'].replace('793729439502', str(itemId)).replace('1078371359', self.storeId).replace(
            'status2', str(status))

        response = self.get_data_post('batchshelf', data=data)

        return response

    def item_edit(self, itemId, title=None, quantity=None):
        if title:
            info = {"sellerId": self.sellerId, "itemId": itemId, "storeId": self.storeId, "isWeight": False,
                    "weightType": None, "title": title}
        elif quantity:
            info = {"sellerId": self.sellerId, "itemId": itemId, "storeId": self.storeId, "isWeight": False,
                    "weightType": None, "quantity": quantity}
        data = json.dumps(info, ensure_ascii=False, separators=(",", ":"))
        response = self.get_data('item_edit', data=data)
        if response['ret'][0] == 'SUCCESS::调用成功':
            return response
        else:
            return response

    def customCategory_update(self, customCategoryList):  # 修改智能排序
        info = {"sellerId": self.sellerId, "storeId": self.storeId, "bizType": 2,
                "customCategoryList": customCategoryList}
        data = {"data": json.dumps({"req": json.dumps(info)})}

        response = self.get_data_post('customCategory_update', data)
        # print(response)
        return response

    def queryByCustomCatPaging(self, customCategoryId):  # 根据分类查询商品
        info = {"sellerId": self.sellerId, "pageNum": 1, "pageSize": 1000, "customCategoryId": customCategoryId,
                "bizType": 2, "storeId": self.storeId}
        data = {"data": json.dumps({"req": json.dumps(info)})}
        response = self.get_data_post('querybycustomcatpaging', data)
        # print(response)
        return response

    def simplencrypt(self, password):
        # 对密码进行Base64编码
        encoded_password = base64.b64encode(password.encode()).decode()
        # 删除所有的'='字符
        encoded_password = encoded_password.replace('=', '')
        # 将字符串反转
        encoded_password = encoded_password[::-1]
        return encoded_password

    def login(self, account, password):
        account = account
        password = self.simplencrypt(password)

        headers = {
            'authority': 'nracc.ele.me',
            'accept': 'application/json, text/plain, */*',
            'accept-language': 'zh-CN,zh;q=0.9',
            'bx-ua': '227!SSiSphGwZvCT6czzUh9KsDudZ+P4JL9j4lPPOXwFJyaWntR3SCcTpsrUVgiE9O3aPEMwCSPE8mJMtS3Oe8xcJPWY6+o1wHRenHEueEHU8n+zntkk+VnL3SIb++TfrYvCnX77jI5HaBdSn6Q8Bx4ZHm7YmrUQl1Wv/DPEOVvHP7sjCb74SY64peqWqAmnmp8inXPPOVjXEUQWnoRvc1lcYNccKCfXDpvCnXEPOJjXa7KW38BUhPoD3XlzqKCXDvvinh+P4e+Ve73k6dkAmxnL3VszqQisFpW7HXE4OJvHaEQzHtw2mWnI3D0SFKVXlpUinXPPOJjXa7szSdzkDWn9fiUSrZPXDpvCHn0aOJjXa7S11R3T5A9CgJrDfI7sOMef+Abpu398UE3nJnXN5QyGgsCLWnOh5ffiy8jQcA9wgfStJnuiXP8+mmU3+ljH+cNbMhWURIjI2Z3HlllwiZxCat5qyTl+B2swH6nTlxvNJjkGT/pLVMUKiMX54D4Yd0G1prVpz90lun+dy0jHcOKAjtBKGtgxvIvsSq60rZhf4Aed1Ff1sUKRNL79pQZokxOtt8xRNYkOWi9NM30L+YTFPEu/JKW2DCMBLfXpKHq5aiBjNt+5xdymm5PkdBplk/GG+7Hj9QksgR5g+pP6W0rd2hSXpsDSXiUKD2c/8D60gOTSnwxH9A6tgYkPmrQJsRXxnz1q/fRaNvCT86pL/jzwS+XqFj4Y+YsSjuS6HGtcw9a8pfoFheV81nDliBM9wqwqxmaWBz81k5POQ2rk37SB4ATPBEYUEZbUGmV5Lhw16dm8g2r+fCspxS9huAj4Y2uw56qgQ1FLfGT/8b3pitRtg/A8TeN12z0hu//b+ZggvpFbse1W38l61fNJIMMhXavzTv5xmiWIbk1FpQNwwOj0DX/S0l0URuuqYr/0gPrrqwBnBLTuRIYnhcJdQ6FIOetUUOvjgBTl79AK8juREjRjSNPHd8kwNXmj1nGb3DOwsPTv65/ZSzyCoaGYryJ6oAGDf+7SSO/tidkat86h7DumQJKawSP5DVKzbCwP4pUtlZyXWikvo05y7YAWmd36Y2foRBPs/drWatWt86P0MoO+xFmihS+oMGQFQzu9ii6ZHLQVwvXhdaQ4lxwAC07yvu+i8TmPV5LbfpTMLmbo4Xf7AWaoTiErD4k3/ykmPjezkSKym8wAdd75bAOJIPzX3MQjaMefNRFTkESDYWXNpVJvpzTXrTsNtVRaKF1ZSyj0eu2PEgucidBTvTuyJ/WqlvMbwX4/Guf4/ntnsnsP8VYlw8wx6bnlslJJ+6WvxYcHOsLu7DCW72wFOEGsbeY6/3cUkFNZzrodXo3/DYuBj1pJnTLu5oT0ezujU3WID5YN0zAedo7TnVli0jSrzMe21fhON9YeLFRqVfyM9oo0+qgg/raantji3p6lXkWCbBt/mcTao1HJGF0LKLxhEIjlTeuu5yWQiHnUXAP/3x0476r1yGl3/HXukH2VTAIyiY46V1wS305vnRrZfXzo6DDnQQxUjfMxggHoXgDNv59H3HbDhr45BmSOvf8ihpPjjQSsvWPiC7/3OMo4krjh6acUSREQlmeIMIsolINSlM3B4q1=',
            'bx-umidtoken': 'G5EAE524E3DF2F4647A97503889CB6462CB1276361996EF28B7',
            'cache-control': 'no-cache',
            'content-type': 'application/json',
            'origin': 'https://nr.ele.me',
            'pragma': 'no-cache',
            'referer': 'https://nr.ele.me/',
            'sec-ch-ua': '"Not_A Brand";v="8", "Chromium";v="120", "Google Chrome";v="120"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': '"Windows"',
            'sec-fetch-dest': 'empty',
            'sec-fetch-mode': 'cors',
            'sec-fetch-site': 'same-site',
            'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        }

        json_data = {
            'account': account,
            'pass': password,
            'type': 2,
            'domain': 'NR_MERCHANT',
            'redirectUrl': 'https://nr.ele.me/eleme_nr_bfe_retail/eb_common',
            'deviceId': 'e1ee5096ab9720b4ce7e98b579fd8a66',
            'source': 1,
            'channel': 'web',
        }

        response = requests.post('https://nracc.ele.me/api/security/login', headers=headers, json=json_data)

        WMUSS = response.cookies.values()[0]
        print(WMUSS)
        return WMUSS

    def get_m_h5_tk(self, WMUSS):
        WMUSS_str = WMUSS
        WMSTOKEN_str = WMUSS
        cookies = {
            'WMUSS': WMUSS_str,
            'WMSTOKEN': WMSTOKEN_str,
        }

        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/86.0.4240.198 Safari/537.36',
            'Accept': 'application/json',
            'Accept-Language': 'zh-CN,zh;q=0.8,zh-TW;q=0.7,zh-HK;q=0.5,en-US;q=0.3,en;q=0.2',
            # 'Accept-Encoding': 'gzip, deflate, br',
            'Content-type': 'application/x-www-form-urlencoded',
            'Origin': 'https://nr.ele.me',
            'Connection': 'keep-alive',
            'Referer': 'https://nr.ele.me/',
            # 'Cookie': 'isg=BEJCOoiyk2v3LIlpkwQ0bvKakEikE0YtNX48CIxbP7Vg3-BZdaD4PKZei1ujlL7F; WMUSS=NTM4MDMTAwMDE3MzExMDQ0NTEyT3Bvczd3SjJQ; SWITCH_SHOP=; WMSTOKEN=NTM4MDMTAwMDE3MzExMDQ0NTEyT3Bvczd3SjJQ; OUTER_AUTH_LOGIN=NTM4MDMTAwMDE3MzExMDQ0NTEyT3Bvczd3SjJQ^%^3BNTM4MDMTAwMDE3MzExMDQ0NTEyT3Bvczd3SjJQ',
            'Sec-Fetch-Dest': 'empty',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Site': 'same-site',
            # Requests doesn't support trailers
            # 'TE': 'trailers',
        }
        timestamp = self.get_timestamp()
        e = f"undefined&{timestamp}&********&{{}}"
        sign = self.get_sign(e)
        url = f'https://nrshop.ele.me/h5/mtop.ele.newretail.ebai.accountreadmtopservice.getshopuserinfo/1.0/?jsv=2.5.8&appKey=********&t={timestamp}&sign={sign}&api=mtop.ele.newretail.ebai.AccountReadMtopService.getShopUserInfo&v=1.0&H5Request=true&type=originaljson&dataType=json&data=^%^7B^%^7D'
        
        try:
            print(f"发送请求获取token，URL: {url}")
            response = requests.get(
                url=url,
                cookies=cookies,
                headers=headers,
                timeout=10  # 增加超时设置
            )
            
            print(f"获取token请求状态码: {response.status_code}")
            print(f"获取token响应Cookie: {response.cookies}")
            
            # 检查响应中是否有cookie
            if '过期' in response.text:
                print("Token过期，尝试更新token")
                account_dict = {'星辰-饿了么': {'account': 'eleme_15928716442', 'password': 'Ofz96835'},
                                '繁花觅-饿了么': {'account': 'eleme_15828112127dnl', 'password': 'Yuy01733'}}
                                
                # 查找匹配的账户
                account_key = None
                for key in account_dict:
                    if key.split('-')[0] in self.shopname:
                        account_key = key
                        break
                
                if account_key:
                    print(f"找到匹配的账户: {account_key}")
                    WMUSS = self.login(account_dict[account_key]['account'], account_dict[account_key]['password'])
                    
                    if 'security' in WMUSS:
                        print(f"{self.shopname}cookie更新失败")
                        # 如果已经有token，继续使用
                        if '_m_h5_tk' in self.cookies and '_m_h5_tk_enc' in self.cookies:
                            return self.cookies['_m_h5_tk'], self.cookies['_m_h5_tk_enc']
                        # 否则提供默认值
                        return f"defaulttoken_{timestamp}", f"defaultenc_{timestamp}"
                    else:
                        self.cookies['WMUSS'] = WMUSS
                        # 省略部分代码...
                else:
                    print(f"没有找到匹配的账户配置")
                    # 如果已经有token，继续使用
                    if '_m_h5_tk' in self.cookies and '_m_h5_tk_enc' in self.cookies:
                        return self.cookies['_m_h5_tk'], self.cookies['_m_h5_tk_enc']
                    # 否则提供默认值
                    return f"defaulttoken_{timestamp}", f"defaultenc_{timestamp}"

            # 获取响应cookie
            cookie_values = list(response.cookies.values())
            print(f"响应cookie值列表: {cookie_values}")
            
            # 检查cookie列表是否包含所需的值
            if len(cookie_values) >= 2:
                _m_h5_tk = cookie_values[0]
                _m_h5_tk_enc = cookie_values[1]
                print(f"成功获取token: {_m_h5_tk}")
                return _m_h5_tk, _m_h5_tk_enc
            else:
                print("无法从响应中获取token，尝试使用现有token或默认值")
                # 如果已经有token，继续使用
                if '_m_h5_tk' in self.cookies and '_m_h5_tk_enc' in self.cookies:
                    print(f"使用现有token: {self.cookies['_m_h5_tk']}")
                    return self.cookies['_m_h5_tk'], self.cookies['_m_h5_tk_enc']
                
                # 否则提供默认值
                default_tk = f"defaulttoken_{timestamp}"
                default_enc = f"defaultenc_{timestamp}"
                print(f"使用默认token: {default_tk}")
                return default_tk, default_enc
                
        except Exception as e:
            print(f"获取token时出错: {e}")
            # 如果已经有token，继续使用
            if '_m_h5_tk' in self.cookies and '_m_h5_tk_enc' in self.cookies:
                return self.cookies['_m_h5_tk'], self.cookies['_m_h5_tk_enc']
            
            # 否则提供默认值
            default_tk = f"defaulttoken_{timestamp}"
            default_enc = f"defaultenc_{timestamp}"
            return default_tk, default_enc

    def replace_store_attribute(self, cookies_str, store_name, new_attribute):
        # 按行分割字符串
        lines = cookies_str.split('\n')
        # 遍历每一行
        for i, line in enumerate(lines):
            # 检查当前行是否包含指定的门店名称
            if line.startswith(store_name):
                # 替换这一行的属性值
                lines[i] = f'{store_name}】{new_attribute}'
                break  # 找到并替换后就退出循环
        # 将修改后的行合并回一个字符串
        modified_cookies_str = '\n'.join(lines)
        return modified_cookies_str

    def update_account(self, account_dict, new_data):

        # 假设 account_dict 包含从 LeanCloud 查询到的数据，其中包含 objectId
        object_id = account_dict.get("objectId")
        if not object_id:
            print("objectId is missing")
            return

        # 获取 LeanCloud Object 实例
        Account = leancloud.Object.extend('Account')
        account = Account.create_without_data(object_id)

        # 更新数据
        for key, value in new_data.items():
            account.set(key, value)

        # 保存更改
        try:
            account.save()
            print("Account updated successfully.")
            changediscounwindow.showMessageSignal.emit(f"{self.shopname}cookie更新成功")
        except leancloud.LeanCloudError as e:
            print(f"Failed to update account: {e}")
            changediscounwindow.showMessageSignal.emit(f"{self.shopname}cookie更新失败")

    def init_cookie(self, WMUSS=None):
        self.cookies['WMUSS'] = WMUSS
        self.cookies['WMSTOKEN'] = WMUSS
        self.headers['x-ele-eb-token'] = WMUSS + ';' + WMUSS
        self.cookies['OUTER_AUTH_LOGIN'] = WMUSS + '%3B' + WMUSS

        _m_h5_tk, _m_h5_tk_enc = self.get_m_h5_tk(WMUSS)
        return _m_h5_tk, _m_h5_tk_enc

    def getShopUserInfo(self):
        try:
            response = self.get_data('getShopUserInfo')
            
            # 打印完整响应，帮助调试
            print(f"API完整响应内容: {json.dumps(response)}")
            
            # 检查API响应格式
            if response and 'ret' in response and response['ret'][0].startswith('SUCCESS'):
                if 'data' in response and response['data']:
                    data = response['data']
                    # 递归查找shopInfo, sellerId和storeId
                    shopInfo = self.find_value_in_dict('shopInfo', data)
                    if not shopInfo:
                        print("未在API响应中找到shopInfo字段")
                        # 直接尝试查找sellerId和storeId
                        sellerId = self.find_value_in_dict('sellerId', data)
                        storeId = self.find_value_in_dict('storeId', data)
                        
                        if sellerId and storeId:
                            print(f"通过直接查找获得商户信息 - sellerId: {sellerId}, storeId: {storeId}")
                            return sellerId, storeId
                    else:
                        # 从shopInfo中提取sellerId和storeId
                        sellerId = shopInfo.get('sellerId')
                        storeId = shopInfo.get('storeId')
                        if sellerId and storeId:
                            print(f"从shopInfo获得商户信息 - sellerId: {sellerId}, storeId: {storeId}")
                            return sellerId, storeId
            
            # 如果找不到商户信息，尝试使用hardcode的预设值
            print("未找到商户信息，尝试使用预设值...")
            
            # 根据店铺名找到对应的hardcode值
            if '星辰' in self.shopname:
                print("使用星辰店铺的硬编码值")
                return "2215722782515", "1078371359"  # 星辰店铺的硬编码值
            elif '繁花里' in self.shopname:
                print("使用繁花里店铺的硬编码值")
                return "2201038469034", "1095947714"  # 繁花里店铺的硬编码值
            elif '繁花觅' in self.shopname:
                print("使用繁花觅店铺的硬编码值")
                return "2201273530391", "1031582541"  # 繁花觅店铺的硬编码值
                
            print("无法确定商户信息")
            return None, None
            
        except Exception as e:
            import traceback
            print('获取商户信息失败:', e)
            print(traceback.format_exc())
            
            # 失败后尝试使用硬编码值
            if '星辰' in self.shopname:
                return "2215722782515", "1078371359"
            elif '繁花里' in self.shopname:
                return "2201038469034", "1095947714"
            elif '繁花觅' in self.shopname:
                return "2201273530391", "1031582541"
            
            return None, None
            
    def find_value_in_dict(self, key, dictionary):
        """递归查找字典中的键值"""
        if key in dictionary:
            return dictionary[key]
        for k, v in dictionary.items():
            if isinstance(v, dict):
                result = self.find_value_in_dict(key, v)
                if result is not None:
                    return result
            elif isinstance(v, list):
                for item in v:
                    if isinstance(item, dict):
                        result = self.find_value_in_dict(key, item)
                        if result is not None:
                            return result
        return None