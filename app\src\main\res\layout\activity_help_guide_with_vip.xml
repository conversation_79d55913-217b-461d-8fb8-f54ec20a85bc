<?xml version="1.0" encoding="utf-8"?>
<ScrollView
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:padding="16dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp"
                android:background="#FFF3E0">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="重要提示"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:textColor="#FF6F00"
                    android:layout_marginBottom="8dp"/>

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="饿了么平台功能目前处于测试阶段，可能存在不稳定情况。如遇到任何问题，欢迎联系客服获取帮助。\n注：饿了么暂时搜索不支持多规格商品。\n\n客服微信：WMSHXAD"
                    android:textColor="#FF6F00"
                    android:layout_marginBottom="8dp"/>
            </LinearLayout>
        </androidx.cardview.widget.CardView>

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="使用说明"
            android:textSize="24sp"
            android:textStyle="bold"
            android:layout_marginBottom="16dp"/>

        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="基本功能"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:layout_marginBottom="8dp"/>

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="1. 账号注册：每个设备可以注册一个账号\n2. 店铺管理：可添加美团、饿了么店铺\n3.方便单独或一键修改同名商品的原价、折扣价、上下架商品\n4.一键修改所有同名商品名称"
                    android:layout_marginBottom="8dp"/>
            </LinearLayout>
        </androidx.cardview.widget.CardView>

        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="折扣管理说明"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:textColor="#2196F3"
                    android:layout_marginBottom="8dp"/>

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="操作步骤：\n1. 点击商品名称获取当前折扣信息\n2. 获取折扣信息后才能进行修改\n\n重要提示：\n• 目前仅支持修改现有折扣\n• 不支持新增折扣\n• 请确保先获取折扣信息再进行修改\n• 如果商品没有折扣，将无法进行修改"
                    android:textColor="#333333"
                    android:layout_marginBottom="8dp"/>

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="操作示例："
                    android:textStyle="bold"
                    android:layout_marginTop="8dp"
                    android:layout_marginBottom="4dp"/>

                <ImageView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:adjustViewBounds="true"
                    android:scaleType="fitCenter"
                    android:src="@drawable/discount_guide"
                    android:layout_marginBottom="8dp"
                    android:contentDescription="折扣操作示例图"/>

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="图片说明：\n1. 点击商品名称区域\n2. 等待折扣信息加载\n3. 修改折扣数值\n4. 点击保存完成修改"
                    android:textColor="#666666"
                    android:layout_marginBottom="8dp"/>

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="重要提示：\n• 目前仅支持修改现有折扣\n• 不支持新增折扣\n• 请确保先获取折扣信息再进行修改\n• 如果商品没有折扣，将无法进行修改"
                    android:textColor="#333333"
                    android:layout_marginBottom="8dp"/>

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="注意：修改折扣前必须先点击商品名称获取当前折扣信息，否则无法进行修改操作。"
                    android:textStyle="italic"
                    android:textColor="#E91E63"
                    android:layout_marginBottom="8dp"/>
            </LinearLayout>
        </androidx.cardview.widget.CardView>

        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp"
                tools:ignore="ExtraText">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="常见问题"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:layout_marginBottom="8dp"/>

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Q: 为什么无法注册新账号？\nA: 每个设备只能注册一个账号，如果设备已注册过，请使用原账号登录。\n\nQ: 如何添加店铺？\nA: 点击顶部导航栏的【门店】，然后点击右下角的【添加门店】按钮(默认添加美团平台，如需添加饿了么请点击按钮前区域切换)。\n\nQ:添加店铺后还是是无法使用怎么办？\nA:添加门店时确定登录成功后在点击授权\n\nQ: 店铺添加失败怎么办？\nA: 1. 确保账号密码正确\n2. 检查网络连接\n3. 确认是否超出店铺数量限制（普通用户最多绑定2个店铺，VIP用户无限制）\n\nQ: 绑定失效怎么办？\nA: 重新再绑定授权一次"
                    android:layout_marginBottom="8dp"/>
            </LinearLayout>
        </androidx.cardview.widget.CardView>

        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="使用建议"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:layout_marginBottom="8dp"/>

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="1. 定期检查店铺状态\n2. 及时处理异常订单\n3. 保持网络稳定连接\n4. 遇到问题及时联系客服搜索\n•技巧：\n• 输入准确的商品名称可以大大缩短搜索时间\n• 避免使用模糊或简短的关键词\n• 建议使用商品的完整名称进行搜索\n"
                    android:layout_marginBottom="8dp"/>
            </LinearLayout>
        </androidx.cardview.widget.CardView>

        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="联系我们"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:layout_marginBottom="8dp"/>

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="客服微信：WMSHXAD\n工作时间：9:00-18:00\n如果觉得不错欢迎分享给朋友哦~"
                    android:layout_marginBottom="8dp"/>
            </LinearLayout>
        </androidx.cardview.widget.CardView>

    </LinearLayout>
</ScrollView>