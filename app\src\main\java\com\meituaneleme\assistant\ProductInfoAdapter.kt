package com.meituaneleme.assistant

import android.content.Intent
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.google.android.material.button.MaterialButton
import com.meituaneleme.assistant.ProductDetailsActivity
import com.meituaneleme.assistant.R
import com.meituaneleme.assistant.GoodInfo
import com.meituaneleme.assistant.StoreAndCookies
import com.meituaneleme.assistant.StoreCookie
import com.meituaneleme.assistant.ElemeApi
import com.meituaneleme.assistant.MeiTuanApi
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

class ProductInfoAdapter(
    private var productInfoList: List<GoodInfo>,
    private var productName: String,
    private val storeCookies: MutableList<StoreAndCookies>,
) : RecyclerView.Adapter<ProductInfoAdapter.ProductInfoViewHolder>() {
    class ProductInfoViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        val tvShopName: TextView = itemView.findViewById(R.id.tvShopName)
        val tvPrice: TextView = itemView.findViewById(R.id.tvPrice)
        val tvDiscountPrice: TextView = itemView.findViewById(R.id.tvDiscountPrice)
        val tvStock: TextView = itemView.findViewById(R.id.tvStock)
        val btnSoldOut: MaterialButton = itemView.findViewById(R.id.btnSoldOut)
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ProductInfoViewHolder {
        val view = LayoutInflater.from(parent.context)
            .inflate(R.layout.item_product_info, parent, false)
        return ProductInfoViewHolder(view)
    }

    override fun onBindViewHolder(holder: ProductInfoViewHolder, position: Int) {
        try {
            val productInfo = productInfoList[position]
            
            // 设置平台标识
            val tvPlatformTag: TextView = holder.itemView.findViewById(R.id.tvPlatformTag)
            if (productInfo.shop.contains("美团")) {
                tvPlatformTag.text = "美团"
                tvPlatformTag.background = holder.itemView.context.getDrawable(R.drawable.tag_meituan)
            } else {
                tvPlatformTag.text = "饿了么"
                tvPlatformTag.background = holder.itemView.context.getDrawable(R.drawable.tag_eleme)
            }
            
            // 设置商店名称
            holder.tvShopName.text = productInfo.shop
            
            // 设置价格信息
            if (productInfo.price.size == 1) {
                holder.tvPrice.text = "原价: ¥${productInfo.price[0]}"
            } else {
                holder.tvPrice.text = "原价: ¥${productInfo.price.minOrNull()}-${productInfo.price.maxOrNull()}"
            }

            // 显示库存信息
            if (productInfo.stock.size == 1) {
                val stockValue = productInfo.stock[0].toInt()
                
                // 根据库存数量设置不同颜色和背景
                when {
                    stockValue < 10 -> {
                        holder.tvStock.text = "⚠️库存:$stockValue"
                        holder.tvStock.setTextColor(holder.itemView.context.resources.getColor(android.R.color.white))
                        holder.tvStock.background = holder.itemView.context.getDrawable(R.drawable.stock_low_background)
                    }
                    stockValue < 30 -> {
                        holder.tvStock.text = "⚠️库存:$stockValue"
                        holder.tvStock.setTextColor(holder.itemView.context.resources.getColor(android.R.color.black))
                        holder.tvStock.background = holder.itemView.context.getDrawable(R.drawable.stock_medium_background)
                    }
                    else -> {
                        holder.tvStock.text = "✓库存:$stockValue"
                        holder.tvStock.setTextColor(holder.itemView.context.resources.getColor(android.R.color.white))
                        holder.tvStock.background = holder.itemView.context.getDrawable(R.drawable.stock_high_background)
                    }
                }
            } else {
                val totalStock = productInfo.stock.sum().toInt()
                
                // 根据库存数量设置不同颜色和背景
                when {
                    totalStock < 10 -> {
                        holder.tvStock.text = "⚠️库存:$totalStock"
                        holder.tvStock.setTextColor(holder.itemView.context.resources.getColor(android.R.color.white))
                        holder.tvStock.background = holder.itemView.context.getDrawable(R.drawable.stock_low_background)
                    }
                    totalStock < 30 -> {
                        holder.tvStock.text = "⚠️库存:$totalStock"
                        holder.tvStock.setTextColor(holder.itemView.context.resources.getColor(android.R.color.black))
                        holder.tvStock.background = holder.itemView.context.getDrawable(R.drawable.stock_medium_background)
                    }
                    else -> {
                        holder.tvStock.text = "✓库存:$totalStock"
                        holder.tvStock.setTextColor(holder.itemView.context.resources.getColor(android.R.color.white))
                        holder.tvStock.background = holder.itemView.context.getDrawable(R.drawable.stock_high_background)
                    }
                }
            }

            // 不同平台使用不同颜色
            if (productInfo.shop.contains("美团")) {
                holder.tvPrice.setTextColor(holder.itemView.context.resources.getColor(R.color.meituan_brand_dark))
                holder.tvDiscountPrice.setTextColor(holder.itemView.context.resources.getColor(R.color.meituan_brand_dark))
            } else {
                holder.tvPrice.setTextColor(holder.itemView.context.resources.getColor(R.color.eleme_brand_dark))
                holder.tvDiscountPrice.setTextColor(holder.itemView.context.resources.getColor(R.color.eleme_brand_dark))
            }
            
            if (productInfo.actprice_min == 9999.0) {
                holder.tvDiscountPrice.text = "折扣价: ¥***"
            } else if (productInfo.actprice_min == productInfo.actprice_max) {
                holder.tvDiscountPrice.text = "折扣价: ¥${productInfo.actprice_min}"
            } else {
                holder.tvDiscountPrice.text =
                    "折扣价: ¥${productInfo.actprice_min}-${productInfo.actprice_max}"
            }

            //判断productInfo.sellStatus的状态，如果是0或者true则显示下架，否则显示上架
            productInfo.sellStatus?.let {
                Log.d("ProductInfoAdapter", "sellStatus: $it")
                Log.d("ProductInfoAdapter","Shop: $productInfo.shop")
                
                // 检查是否有状态更新标记
                val hasStatusUpdateFlag = productInfo.guige.any { guige -> 
                    guige.activityLimit == ProductAdapter.STATUS_UPDATE_FLAG || 
                    guige.activityLimit == ProductAdapter.STATUS_UPDATE_FLAG_OFF 
                }
                
                if (hasStatusUpdateFlag) {
                    // 根据标记判断当前状态
                    val isOffline = productInfo.guige.any { guige -> 
                        guige.activityLimit == ProductAdapter.STATUS_UPDATE_FLAG_OFF 
                    }
                    holder.btnSoldOut.text = if (isOffline) "上架" else "下架"
                } else {
                    // 使用原来的逻辑
                    if ("美团" in productInfo.shop) {
                        if (it == 0 || it == true) {
                            holder.btnSoldOut.text = "下架"
                        } else {
                            holder.btnSoldOut.text = "上架"
                        }
                    } else {
                        if (it == 1 ) {
                            holder.btnSoldOut.text = "下架"
                        } else {
                            holder.btnSoldOut.text = "上架"
                        }
                    }
                }
            }

            holder.tvShopName.text = productInfo.shop


            //cookie_str是storeCookies的属性，通过storeCookies获取对应的门店cookie
            var cookie_str: String? = null
            storeCookies.forEach() {
                if (it.store == productInfo.shop) {
                    cookie_str = it.cookie_str
                }
            }
            holder.itemView.setOnClickListener {
                var productName = productName.replace("（", "(").replace("）", ")")
                val intent = Intent(holder.itemView.context, ProductDetailsActivity::class.java)
                intent.putExtra("productName", productName)
                val storeName = productInfo.shop
                intent.putExtra("storeName", storeName)
                intent.putExtra("cookies", cookie_str)
                //把productInfo.guige传入给productInfoList
                intent.putParcelableArrayListExtra("productInfoList", ArrayList(productInfo.guige))
                holder.itemView.context.startActivity(intent)
            }

            holder.btnSoldOut.setOnClickListener(View.OnClickListener {
                // 点击上架或下架按钮逻辑
                //调用ElemeApi的batchShelf方法
                // 通过storeCookies获取对应的cookie
                //判断门店名称是否包含"美团"，如果包含则调用MeiTuanApi的changeOrignPrice方法"
                try {
                    if (productInfo.shop.contains("美团")) {
                        val storeCookie = storeCookies.find { it.store == productInfo.shop }
                        val api = storeCookie?.let { MeiTuanApi(it.cookie_str) }
                        //调用MeiTuanApi的changeOrignPrice方法

                        if (holder.btnSoldOut.text == "上架" && api != null) {
                            GlobalScope.launch {
                                val spuIds = productInfo.spuIds ?: 0L
                                if (spuIds != null) {
                                    val skuIdsList = mutableListOf<String>()
                                    if (productInfo.guige.size == 1) {
                                        skuIdsList.add(productInfo.guige[0].id)
                                    } else {
                                        productInfo.guige.forEach { item ->
                                            skuIdsList.add(item.id)
                                        }
                                    }

                                    val skuIds = skuIdsList.joinToString(",")
                                    val res = api.batchSetSellStatus(spuIds, skuIds, 0)

                                    if (res == """{"msg":"success","code":0,"data":""}""") {
                                        withContext(Dispatchers.Main) {
                                            holder.btnSoldOut.text = "下架"
                                        }

                                    } else {
                                        withContext(Dispatchers.Main) {
                                            holder.btnSoldOut.text = "修改失败"
                                        }

                                    }
                                }


                            }
                        }
                        else if (holder.btnSoldOut.text == "下架" && api != null) {
                            GlobalScope.launch {
                                val spuIds = productInfo.spuIds ?: 0L
                                if (spuIds != null) {
                                    val skuIdsList = mutableListOf<String>()
                                    if (productInfo.guige.size == 1) {
                                        skuIdsList.add(productInfo.guige[0].id)
                                    } else {
                                        productInfo.guige.forEach { item ->
                                            skuIdsList.add(item.id)
                                        }
                                    }
                                    val skuIds = skuIdsList.joinToString(",")
                                    val storeCookie =
                                        storeCookies.find { it.store == productInfo.shop }
                                    val api = storeCookie?.let { MeiTuanApi(it.cookie_str) }
                                    if (api != null) {
                                        val res = api.batchSetSellStatus(spuIds, skuIds, 1)
                                        if (res == """{"msg":"success","code":0,"data":""}""") {
                                            withContext(Dispatchers.Main) {
                                                holder.btnSoldOut.text = "上架"
                                            }

                                        } else {
                                            withContext(Dispatchers.Main) {
                                                holder.btnSoldOut.text = "修改失败"
                                            }

                                        }
                                    }
                                }
                            }
                        }
                    }
                    else {
                        val storeCookie = storeCookies.find { it.store == productInfo.shop }
                        val api = storeCookie?.let { ElemeApi(it.cookie_str, productInfo.shop) }
                        if (holder.btnSoldOut.text == "上架" && api != null) {
                            val itemId = productInfo.guige[0].itemId
                            if (itemId != null) {
                                GlobalScope.launch {
                                    val res = api.batchShelf(itemId, 0)
                                    if (res.contains("SUCCESS")) {
                                        withContext(Dispatchers.Main) {
                                            holder.btnSoldOut.text = "下架"
                                        }

                                    } else {
                                        withContext(Dispatchers.Main) {
                                            holder.btnSoldOut.text = "修改失败"
                                        }

                                    }
                                }
                            }
                        } else {
                            val itemId = productInfo.guige[0].itemId
                            if (itemId != null && api != null) {
                                GlobalScope.launch {
                                    val res = api.batchShelf(itemId, -2)
                                    try {
                                        if (res.contains("SUCCESS")) {
                                            withContext(Dispatchers.Main) {
                                                holder.btnSoldOut.text = "上架"
                                            }
                                        } else {
                                            withContext(Dispatchers.Main) {
                                                holder.btnSoldOut.text = "修改失败"
                                            }
                                        }
                                    } catch (e: Exception) {
                                        Log.e(
                                            "ProductInfoAdapter",
                                            "Error binding data at position $position",
                                            e
                                        )
                                    }
                                }
                            }
                        }
                    }
                } catch (e: Exception) {
                    Log.e("ProductInfoAdapter", "Error binding data at position $position", e)
                }
            })

        } catch (e: Exception) {
            Log.e("ProductInfoAdapter", "Error binding data at position $position", e)
        }
    }

    override fun getItemCount(): Int = productInfoList.size

    // 更新数据方法
    fun updateData(newProductInfoList: List<GoodInfo>, productName: String? = null) {
        productName?.let {
            this.productName = it
        }
        productInfoList = newProductInfoList
        notifyDataSetChanged()
    }
}
