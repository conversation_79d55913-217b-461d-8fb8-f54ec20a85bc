package com.meituaneleme.assistant.api

import retrofit2.Retrofit
import retrofit2.converter.gson.GsonConverterFactory
import com.meituaneleme.assistant.config.AppConfig


object ApiClient {
    private val retrofit: Retrofit = Retrofit.Builder()
        .baseUrl(AppConfig.LEANCLOUD_BASE_URL)
        .addConverterFactory(GsonConverterFactory.create())
        .build()

    val api: LeanCloudApi = retrofit.create(LeanCloudApi::class.java)
} 