package com.meituaneleme.assistant

import android.os.Bundle
import android.widget.Button
import android.widget.TextView
import android.widget.Toast
import androidx.appcompat.app.AlertDialog
import androidx.appcompat.app.AppCompatActivity
import com.meituaneleme.assistant.util.DeviceFingerprintManager
import com.meituaneleme.assistant.util.SessionManager

/**
 * 设备指纹管理界面
 * 用于查看和管理设备指纹信息
 */
class DeviceFingerprintActivity : AppCompatActivity() {
    
    private lateinit var deviceFingerprintManager: DeviceFingerprintManager
    private lateinit var sessionManager: SessionManager
    
    private lateinit var deviceIdText: TextView
    private lateinit var userAgentText: TextView
    private lateinit var screenResolutionText: TextView
    private lateinit var timezoneText: TextView
    private lateinit var languageText: TextView
    private lateinit var sessionsText: TextView
    
    private lateinit var refreshButton: Button
    private lateinit var clearFingerprintButton: Button
    private lateinit var clearSessionsButton: Button
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_device_fingerprint)
        
        // 初始化管理器
        deviceFingerprintManager = DeviceFingerprintManager.getInstance(this)
        sessionManager = SessionManager.getInstance(this)
        
        // 初始化视图
        initViews()
        
        // 设置点击事件
        setupClickListeners()
        
        // 加载数据
        loadFingerprintData()
    }
    
    private fun initViews() {
        deviceIdText = findViewById(R.id.tv_device_id)
        userAgentText = findViewById(R.id.tv_user_agent)
        screenResolutionText = findViewById(R.id.tv_screen_resolution)
        timezoneText = findViewById(R.id.tv_timezone)
        languageText = findViewById(R.id.tv_language)
        sessionsText = findViewById(R.id.tv_sessions)
        
        refreshButton = findViewById(R.id.btn_refresh)
        clearFingerprintButton = findViewById(R.id.btn_clear_fingerprint)
        clearSessionsButton = findViewById(R.id.btn_clear_sessions)
    }
    
    private fun setupClickListeners() {
        refreshButton.setOnClickListener {
            loadFingerprintData()
            Toast.makeText(this, "数据已刷新", Toast.LENGTH_SHORT).show()
        }
        
        clearFingerprintButton.setOnClickListener {
            showClearFingerprintDialog()
        }
        
        clearSessionsButton.setOnClickListener {
            showClearSessionsDialog()
        }
    }
    
    private fun loadFingerprintData() {
        // 加载设备指纹信息
        deviceIdText.text = "设备ID: ${deviceFingerprintManager.getDeviceId()}"
        userAgentText.text = "用户代理: ${deviceFingerprintManager.getUserAgent()}"
        screenResolutionText.text = "屏幕分辨率: ${deviceFingerprintManager.getScreenResolution()}"
        timezoneText.text = "时区: ${deviceFingerprintManager.getTimezone()}"
        languageText.text = "语言: ${deviceFingerprintManager.getLanguage()}"
        
        // 加载会话信息
        val sessions = sessionManager.getAllValidSessions()
        val sessionInfo = StringBuilder()
        sessionInfo.append("有效会话数: ${sessions.size}\n\n")
        
        sessions.forEach { session ->
            sessionInfo.append("${session.platform} - ${session.storeName}\n")
            sessionInfo.append("时间: ${java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(java.util.Date(session.timestamp))}\n")
            sessionInfo.append("设备指纹: ${session.deviceFingerprint}\n\n")
        }
        
        sessionsText.text = sessionInfo.toString()
    }
    
    private fun showClearFingerprintDialog() {
        AlertDialog.Builder(this)
            .setTitle("清除设备指纹")
            .setMessage("确定要清除所有设备指纹信息吗？这将导致下次登录时需要重新验证设备。")
            .setPositiveButton("确定") { _, _ ->
                deviceFingerprintManager.clearFingerprint()
                loadFingerprintData()
                Toast.makeText(this, "设备指纹已清除", Toast.LENGTH_SHORT).show()
            }
            .setNegativeButton("取消", null)
            .show()
    }
    
    private fun showClearSessionsDialog() {
        AlertDialog.Builder(this)
            .setTitle("清除会话")
            .setMessage("确定要清除所有登录会话吗？这将导致下次登录时需要重新输入验证码。")
            .setPositiveButton("确定") { _, _ ->
                sessionManager.clearAllSessions()
                loadFingerprintData()
                Toast.makeText(this, "会话已清除", Toast.LENGTH_SHORT).show()
            }
            .setNegativeButton("取消", null)
            .show()
    }
}
