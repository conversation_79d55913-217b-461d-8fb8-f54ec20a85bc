// ProductAdapter.kt
package com.meituaneleme.assistant

import Product
import android.app.Activity
import android.content.Context
import android.content.Intent
import android.graphics.drawable.Drawable
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Button
import android.widget.EditText
import android.widget.ImageView
import android.widget.TextView
import android.widget.Toast
import androidx.appcompat.app.AlertDialog
import androidx.fragment.app.Fragment
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.bumptech.glide.Glide
import com.bumptech.glide.load.DataSource
import com.bumptech.glide.load.engine.GlideException
import com.bumptech.glide.request.RequestListener
import com.bumptech.glide.request.target.Target
import com.meituaneleme.assistant.GoodInfo
import com.meituaneleme.assistant.GoodsInShopsInfo
import com.meituaneleme.assistant.GuiGeInfo
import com.meituaneleme.assistant.QueryPageShopSkuList_Dalouy
import com.meituaneleme.assistant.R
import com.meituaneleme.assistant.SearchFragment
import com.meituaneleme.assistant.StoreAndCookies
import com.meituaneleme.assistant.ImageViewerActivity
import com.meituaneleme.assistant.ElemeApi
import com.meituaneleme.assistant.MeiTuanApi
import com.google.android.material.button.MaterialButton
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.async
import kotlinx.coroutines.launch
import kotlinx.coroutines.runBlocking
import kotlinx.coroutines.withContext
import kotlinx.coroutines.awaitAll
import org.json.JSONArray
import org.json.JSONObject

class ProductAdapter(
    private val context: Context,
    private val productList: MutableList<Product>,
    private val storeCookies: MutableList<StoreAndCookies>,
    private val fragment: Fragment,
    private val onRemoveClick: (Product) -> Unit
) : RecyclerView.Adapter<ProductAdapter.ProductViewHolder>() {

    private val lifecycleScope = fragment.lifecycleScope
    
    // 状态更新标记常量
    companion object {
        const val STATUS_UPDATE_FLAG = "status_updated"
        const val STATUS_UPDATE_FLAG_OFF = "status_updated_off"
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ProductViewHolder {
        val view = LayoutInflater.from(context).inflate(R.layout.item_product, parent, false)
        return ProductViewHolder(view)
    }


    override fun onBindViewHolder(holder: ProductViewHolder, position: Int) {
        try {
            val product = productList[position]
            var image =""
            if (product.image.toString().contains("https")) {
                image = product.image
            }
            else{
                image = product.image.replace("http", "https")
            }

            holder.tvProductTitle.text = product.productName
            Glide.with(context)
                .load(image)
                .placeholder(R.drawable.zhanwei)
                .error(R.drawable.img_erro)
                .listener(object : RequestListener<Drawable> {
                    override fun onLoadFailed(
                        e: GlideException?,
                        model: Any?,
                        target: Target<Drawable>?,
                        isFirstResource: Boolean
                    ): Boolean {
                        Log.e("GlideError", "Error loading image: ${e?.message}")
                        return false
                    }

                    override fun onResourceReady(
                        resource: Drawable?,
                        model: Any?,
                        target: Target<Drawable>?,
                        dataSource: DataSource?,
                        isFirstResource: Boolean
                    ): Boolean {
                        Log.d("GlideSuccess", "Image loaded successfully")
                        return false
                    }
                })
                .into(holder.ivProductImage)

            holder.rvProductInfoList.layoutManager = LinearLayoutManager(context)
            val productInfoAdapter = holder.rvProductInfoList.adapter as? ProductInfoAdapter
                ?: ProductInfoAdapter(product.productinfolist, product.productName, storeCookies).also {
                    holder.rvProductInfoList.adapter = it
                }
            productInfoAdapter.updateData(product.productinfolist, product.productName)
            
            // 恢复编辑按钮原有功能 - 修改商品名称
            holder.ivEditProduct.setOnClickListener {
                showEditProductNameDialog(product)
            }
            
            // 批量操作区域的折叠/展开按钮功能
            val btnToggleBatchOperations = holder.itemView.findViewById<MaterialButton>(R.id.btn_toggle_batch_operations)
            val cardPriceSettings = holder.itemView.findViewById<View>(R.id.card_price_settings)
            
            btnToggleBatchOperations.setOnClickListener {
                // 切换可见性
                if (cardPriceSettings.visibility == View.VISIBLE) {
                    // 如果当前可见，则隐藏
                    cardPriceSettings.visibility = View.GONE
                    // 更新按钮图标为展开图标
                    btnToggleBatchOperations.icon = context.getDrawable(R.drawable.ic_expand)
                } else {
                    // 如果当前隐藏，则显示
                    cardPriceSettings.visibility = View.VISIBLE
                    // 更新按钮图标为折叠图标
                    btnToggleBatchOperations.icon = context.getDrawable(R.drawable.ic_collapse)
                }
            }

            // Set OnClickListener to get product discount
            holder.tvProductTitle.setOnClickListener {
                val overlayView = holder.itemView.findViewById<View>(R.id.overlay_view)

                if (product.hasDiscount) {
                    Toast.makeText(context, "已经有折扣信息了", Toast.LENGTH_SHORT).show()
                    overlayView.visibility = View.GONE
                } else {
                    product.hasDiscount = true
                    overlayView.visibility = View.VISIBLE
                    lifecycleScope.launch(Dispatchers.IO) {
                        getProductDiscount(product)
                        withContext(Dispatchers.Main) {
                            notifyDataSetChanged()
                            overlayView.visibility = View.GONE
                        }
                    }
                }
            }

            holder.btnSaveOriginalPrice.setOnClickListener {
                val originalPrice = holder.etOriginalPrice.text.toString()
                if (originalPrice.isEmpty()) return@setOnClickListener

                lifecycleScope.launch {
                    try {
                        val updateJobs = product.productinfolist.map { info ->
                            async(Dispatchers.IO) {
                                try {
                                    if (info.guige.size > 1) {
                                        withContext(Dispatchers.Main) {
                                Toast.makeText(
                                    context,
                                    "${info.shop}是多规格商品，请在商品规格中修改折扣价",
                                    Toast.LENGTH_SHORT
                                ).show()
                            }
                                        return@async
                                    }

                                    val storeCookie = storeCookies.find { it.store in info.shop }
                                    storeCookie?.let {
                                        if (info.shop.contains("美团")) {
                                            updateMeiTuanOriginalPrice(info, originalPrice, it.cookie_str)
                                            } else {
                                            updateElemeOriginalPrice(info, originalPrice, it.cookie_str)
                                        }
                                    }
                                } catch (e: Exception) {
                                    Log.e("ProductAdapter", "更新原价出错: ${e.message}", e)
                                }
                            }
                        }

                        updateJobs.awaitAll()
                        notifyDataSetChanged()
                    } catch (e: Exception) {
                        Log.e("ProductAdapter", "批量更新原价出错", e)
                    }
                }
            }

            holder.btnSaveDiscountPrice.setOnClickListener {
                val discountPrice = holder.etDiscountPrice.text.toString()
                if (discountPrice.isEmpty()) return@setOnClickListener

                lifecycleScope.launch {
                    try {
                        val updateJobs = product.productinfolist.map { info ->
                            async(Dispatchers.IO) {
                                try {
                                    when {
                                        info.guige.size > 1 -> {
                                            withContext(Dispatchers.Main) {
                                Toast.makeText(
                                    context,
                                                    "${info.shop}是多规格商品，请在商品规格中修改折扣价",
                                    Toast.LENGTH_SHORT
                                ).show()
                            }
                        }
                                        info.actprice_min == 9999.0 -> {
                                            withContext(Dispatchers.Main) {
                                Toast.makeText(
                                    context,
                                    "${info.shop}折扣价未获取，请先获取折扣价",
                                    Toast.LENGTH_SHORT
                                ).show()
                            }
                        }
                                        info.actprice_min != info.actprice_max -> {
                                            withContext(Dispatchers.Main) {
                                Toast.makeText(
                                    context,
                                    "${info.shop}未设置折扣,无法修改折扣价",
                                    Toast.LENGTH_SHORT
                                ).show()
                            }
                        }
                                        else -> {
                                            val storeCookie = storeCookies.find { it.store in info.shop }
                                            storeCookie?.let {
                                                if (info.shop.contains("美团")) {
                                                    updateMeiTuanDiscountPrice(info, discountPrice, it.cookie_str)
                                                } else {
                                                    updateElemeDiscountPrice(info, discountPrice, it.cookie_str)
                                                }
                                            }
                                        }
                                    }
                                } catch (e: Exception) {
                                    Log.e("ProductAdapter", "更新折扣价出错: ${e.message}", e)
                                }
                            }
                        }

                        updateJobs.awaitAll()
                        notifyDataSetChanged()
                    } catch (e: Exception) {
                        Log.e("ProductAdapter", "批量更新折扣价出错", e)
                    }
                }
            }

            holder.ivProductImage.setOnClickListener {
                val intent = Intent(context, ImageViewerActivity::class.java).apply {
                    putExtra("image_url", product.image)
                }
                context.startActivity(intent)
            }

            // 设置长按查看完整商品名称
            holder.tvProductTitle.setOnLongClickListener {
                AlertDialog.Builder(context)
                    .setTitle("商品完整名称")
                    .setMessage(product.productName)
                    .setPositiveButton("关闭", null)
                    .show()
                true
            }

            // 设置库存修改按钮点击事件
            holder.btnSaveStock.setOnClickListener {
                val stock = holder.etStock.text.toString()
                if (stock.isEmpty()) return@setOnClickListener

                lifecycleScope.launch {
                    try {
                        val updateJobs = product.productinfolist.map { info ->
                            async(Dispatchers.IO) {
                                try {
                                    if (info.guige.size > 1) {
                                        withContext(Dispatchers.Main) {
                                            Toast.makeText(
                                                context,
                                                "${info.shop}是多规格商品，请在商品规格中修改库存",
                                                Toast.LENGTH_SHORT
                                            ).show()
                                        }
                                        return@async
                                    }

                                    val storeCookie = storeCookies.find { it.store in info.shop }
                                    storeCookie?.let {
                                        if (info.shop.contains("美团")) {
                                            updateMeiTuanStock(info, stock, it.cookie_str)
                                        } else {
                                            updateElemeStock(info, stock, it.cookie_str)
                                        }
                                    }
                                } catch (e: Exception) {
                                    Log.e("ProductAdapter", "更新库存出错: ${e.message}", e)
                                }
                            }
                        }

                        updateJobs.awaitAll()
                        notifyDataSetChanged()
                    } catch (e: Exception) {
                        Log.e("ProductAdapter", "批量更新库存出错", e)
                    }
                }
            }

            // 设置一键上架按钮点击事件
            holder.btnBatchOnShelf.setOnClickListener {
                lifecycleScope.launch {
                    try {
                        val overlayView = holder.itemView.findViewById<View>(R.id.overlay_view)
                        val tvOverlayText = holder.itemView.findViewById<TextView>(R.id.tv_overlay_text)
                        tvOverlayText.text = "商品上架中..."
                        overlayView.visibility = View.VISIBLE
                        
                        val updateJobs = product.productinfolist.map { info ->
                            async(Dispatchers.IO) {
                                try {
                                    val storeCookie = storeCookies.find { it.store in info.shop }
                                    storeCookie?.let {
                                        if (info.shop.contains("美团")) {
                                            val api = MeiTuanApi(it.cookie_str)
                                            val spuIds = info.spuIds ?: 0L
                                            if (spuIds != 0L) {
                                                val skuIdsList = mutableListOf<String>()
                                                if (info.guige.size == 1) {
                                                    skuIdsList.add(info.guige[0].id)
                                                } else {
                                                    info.guige.forEach { item ->
                                                        skuIdsList.add(item.id)
                                                    }
                                                }
                                                val skuIds = skuIdsList.joinToString(",")
                                                
                                                val res = api.batchSetSellStatus(spuIds, skuIds, 0)
                                                if (res == """{"msg":"success","code":0,"data":""}""") {
                                                    // 不直接修改sellStatus，创建标记
                                                    info.guige.forEach { it.activityLimit = STATUS_UPDATE_FLAG }
                                                    withContext(Dispatchers.Main) {
                                                        Toast.makeText(context, "${info.shop}上架成功", Toast.LENGTH_SHORT).show()
                                                    }
                                                } else {
                                                    withContext(Dispatchers.Main) {
                                                        Toast.makeText(context, "${info.shop}上架失败", Toast.LENGTH_SHORT).show()
                                                    }
                                                }
                                            }
                                        } else {
                                            // 饿了么上架
                                            val api = ElemeApi(it.cookie_str, info.shop)
                                            val itemId = info.guige[0].itemId
                                            if (itemId != null) {
                                                
                                                val res = api.batchShelf(itemId, 0)
                                                if (res.contains("SUCCESS")) {
                                                    // 不直接修改sellStatus，创建标记
                                                    info.guige.forEach { it.activityLimit = STATUS_UPDATE_FLAG }
                                                    withContext(Dispatchers.Main) {
                                                        Toast.makeText(context, "${info.shop}上架成功", Toast.LENGTH_SHORT).show()
                                                    }
                                                } else {
                                                    withContext(Dispatchers.Main) {
                                                        Toast.makeText(context, "${info.shop}上架失败", Toast.LENGTH_SHORT).show()
                                                    }
                                                }
                                            }
                                        }
                                    }
                                } catch (e: Exception) {
                                    Log.e("ProductAdapter", "一键上架出错: ${e.message}", e)
                                    withContext(Dispatchers.Main) {
                                        Toast.makeText(context, "上架过程中出错: ${e.message}", Toast.LENGTH_SHORT).show()
                                    }
                                }
                            }
                        }

                        updateJobs.awaitAll()
                        withContext(Dispatchers.Main) {
                            overlayView.visibility = View.GONE
                            // 刷新商品信息列表适配器
                            val productInfoAdapter = holder.rvProductInfoList.adapter as? ProductInfoAdapter
                            productInfoAdapter?.notifyDataSetChanged()
                            notifyDataSetChanged()
                        }
                    } catch (e: Exception) {
                        Log.e("ProductAdapter", "批量上架出错", e)
                    }
                }
            }
            
            // 设置一键下架按钮点击事件
            holder.btnBatchOffShelf.setOnClickListener {
                lifecycleScope.launch {
                    try {
                        val overlayView = holder.itemView.findViewById<View>(R.id.overlay_view)
                        val tvOverlayText = holder.itemView.findViewById<TextView>(R.id.tv_overlay_text)
                        tvOverlayText.text = "商品下架中..."
                        overlayView.visibility = View.VISIBLE
                        
                        val updateJobs = product.productinfolist.map { info ->
                            async(Dispatchers.IO) {
                                try {
                                    val storeCookie = storeCookies.find { it.store in info.shop }
                                    storeCookie?.let {
                                        if (info.shop.contains("美团")) {
                                            val api = MeiTuanApi(it.cookie_str)
                                            val spuIds = info.spuIds ?: 0L
                                            if (spuIds != 0L) {
                                                val skuIdsList = mutableListOf<String>()
                                                if (info.guige.size == 1) {
                                                    skuIdsList.add(info.guige[0].id)
                                                } else {
                                                    info.guige.forEach { item ->
                                                        skuIdsList.add(item.id)
                                                    }
                                                }
                                                val skuIds = skuIdsList.joinToString(",")
                                                
                                                val res = api.batchSetSellStatus(spuIds, skuIds, 1)
                                                if (res == """{"msg":"success","code":0,"data":""}""") {
                                                    // 不直接修改sellStatus，创建标记
                                                    info.guige.forEach { it.activityLimit = STATUS_UPDATE_FLAG_OFF }
                                                    withContext(Dispatchers.Main) {
                                                        Toast.makeText(context, "${info.shop}下架成功", Toast.LENGTH_SHORT).show()
                                                    }
                                                } else {
                                                    withContext(Dispatchers.Main) {
                                                        Toast.makeText(context, "${info.shop}下架失败", Toast.LENGTH_SHORT).show()
                                                    }
                                                }
                                            }
                                        } else {
                                            // 饿了么下架
                                            val api = ElemeApi(it.cookie_str, info.shop)
                                            val itemId = info.guige[0].itemId
                                            if (itemId != null) {
                                                
                                                val res = api.batchShelf(itemId, -2)
                                                if (res.contains("SUCCESS")) {
                                                    // 不直接修改sellStatus，创建标记
                                                    info.guige.forEach { it.activityLimit = STATUS_UPDATE_FLAG_OFF }
                                                    withContext(Dispatchers.Main) {
                                                        Toast.makeText(context, "${info.shop}下架成功", Toast.LENGTH_SHORT).show()
                                                    }
                                                } else {
                                                    withContext(Dispatchers.Main) {
                                                        Toast.makeText(context, "${info.shop}下架失败", Toast.LENGTH_SHORT).show()
                                                    }
                                                }
                                            }
                                        }
                                    }
                                } catch (e: Exception) {
                                    Log.e("ProductAdapter", "一键下架出错: ${e.message}", e)
                                    withContext(Dispatchers.Main) {
                                        Toast.makeText(context, "下架过程中出错: ${e.message}", Toast.LENGTH_SHORT).show()
                                    }
                                }
                            }
                        }

                        updateJobs.awaitAll()
                        withContext(Dispatchers.Main) {
                            overlayView.visibility = View.GONE
                            // 刷新商品信息列表适配器
                            val productInfoAdapter = holder.rvProductInfoList.adapter as? ProductInfoAdapter
                            productInfoAdapter?.notifyDataSetChanged()
                            notifyDataSetChanged()
                        }
                    } catch (e: Exception) {
                        Log.e("ProductAdapter", "批量下架出错", e)
                    }
                }
            }
        } catch (e: Exception) {
            Log.e("ProductAdapter", "onBindViewHolder error at position $position", e)
        }
    }

    private fun showEditProductNameDialog(product: Product) {
        val dialogView = LayoutInflater.from(context).inflate(R.layout.dialog_edit_product_name, null)
        val etProductName = dialogView.findViewById<EditText>(R.id.et_product_name)
        val tvEditResult = dialogView.findViewById<TextView>(R.id.tv_edit_result)
        val tvCharCount = dialogView.findViewById<TextView>(R.id.tv_char_count)
        val btnSave = dialogView.findViewById<Button>(R.id.btn_save)
        
        etProductName.setText(product.productName)
        
        // 初始化字符计数
        val initialLength = product.productName.length
        tvCharCount.text = "$initialLength/45"
        if (initialLength > 45) {
            tvCharCount.setTextColor(context.resources.getColor(android.R.color.holo_red_dark))
            btnSave.isEnabled = false
        }
        
        // 设置文本变化监听器
        etProductName.addTextChangedListener(object : android.text.TextWatcher {
            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {}
            
            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {}
            
            override fun afterTextChanged(s: android.text.Editable?) {
                val length = s?.length ?: 0
                tvCharCount.text = "$length/45"
                
                if (length > 45) {
                    tvCharCount.setTextColor(context.resources.getColor(android.R.color.holo_red_dark))
                    btnSave.isEnabled = false
                } else {
                    tvCharCount.setTextColor(context.resources.getColor(android.R.color.darker_gray))
                    btnSave.isEnabled = true
                }
            }
        })

        val dialog = AlertDialog.Builder(context)
            .setTitle("编辑商品名称")
            .setView(dialogView)
            .setCancelable(false)
            .setNegativeButton("关闭", null)
            .create()

        dialog.show()

        btnSave.setOnClickListener {
            val newName = etProductName.text.toString()
            if (newName.isNotBlank()) {
                product.productName = newName
                lifecycleScope.launch {
                    try {
                        val updateJobs = product.productinfolist.map { info ->
                            async(Dispatchers.IO) {
                                try {
                                    val storeCookie = storeCookies.find { it.store in info.shop }
                                    storeCookie?.let {
                        if (info.shop.contains("美团")) {
                                            updateMeiTuanProductName(info, newName, it.cookie_str, tvEditResult)
                                        } else {
                                            updateElemeProductName(info, newName, it.cookie_str, tvEditResult)
                                        }
                                    }
                                } catch (e: Exception) {
                                    Log.e("ProductAdapter", "更新商品名称出错: ${e.message}", e)
                                }
                            }
                        }
                        updateJobs.awaitAll()
                        notifyDataSetChanged()
                    } catch (e: Exception) {
                        Log.e("ProductAdapter", "批量更新商品名称出错", e)
                    }
                }
            } else {
                tvEditResult.append("商品名称不能为空\n")
                tvEditResult.setTextColor(context.resources.getColor(android.R.color.holo_red_dark))
            }
        }
    }

    private suspend fun updateMeiTuanProductName(
        info: GoodInfo,
        newName: String,
        cookieStr: String,
        tvEditResult: TextView
    ) {
        val api = MeiTuanApi(cookieStr)
        val postResult = info.spuIds?.let { api.updateSpuName(it, newName) }
        withContext(Dispatchers.Main) {
            if (postResult == "{\"msg\":\"\",\"code\":0,\"data\":\"\"}") {
                tvEditResult.append("${info.shop}修改成功\n")
            } else {
                tvEditResult.append("${info.shop}修改失败，原因：${postResult}\n")
            }
        }
    }

    private suspend fun updateElemeProductName(
        info: GoodInfo,
        newName: String,
        cookieStr: String,
        tvEditResult: TextView
    ) {
        val api = ElemeApi(cookieStr, info.shop)
        val postResult = info.guige[0].itemId?.let { api.item_edit(it, newName) }
        withContext(Dispatchers.Main) {
            if (postResult?.contains("SUCCESS") == true) {
                tvEditResult.append("${info.shop}修改成功\n")
            } else {
                tvEditResult.append("${info.shop}修改失败，原因：${postResult}\n")
            }
        }
    }

    override fun getItemCount(): Int = productList.size

    fun updateData(finalResM: MutableList<GoodsInShopsInfo>) {
        try {
            productList.clear()
            finalResM.forEach { shopInfo ->
                productList.add(
                    Product(
                        productName = shopInfo.title,
                        image = shopInfo.info.firstOrNull()?.image ?: "",
                        productinfolist = shopInfo.info
                    )
                )
            }
            notifyDataSetChanged()
        } catch (e: Exception) {
            Log.e("ProductAdapter", "Error updating data", e)
        }
    }

    private fun getProductDiscount(product: Product) {
        lifecycleScope.launch(Dispatchers.IO) {
            try {
                val discountJobs = product.productinfolist.map { info ->
                    async(Dispatchers.IO) {
                        try {
                            val storeCookie = storeCookies.find { it.store in info.shop }
                            storeCookie?.let {
                                if (info.shop.contains("美团")) {
                                    getMeiTuanDiscount(info, it.cookie_str)
                                } else {
                                    getElemeDiscount(info, it.cookie_str)
                                }
                            }
                        } catch (e: Exception) {
                            Log.e("ProductAdapter", "获取折扣信息出错: ${e.message}", e)
                        }
                    }
                }

                discountJobs.awaitAll()
                withContext(Dispatchers.Main) {
                    notifyDataSetChanged()
                }
            } catch (e: Exception) {
                Log.e("ProductAdapter", "批量获取折扣信息出错", e)
            }
        }
    }

    private suspend fun getMeiTuanDiscount(info: GoodInfo, cookieStr: String) {
        val api = MeiTuanApi(cookieStr)
                            for (guige in info.guige) {
            val response = api.getActPriceBySkuID(guige.id)
            val obj = JSONObject(response)
                                val respPage = obj.getJSONObject("data").getJSONObject("respPage")
                                    .get("pageContent") as JSONArray
                                if (respPage.length() > 0) {
                                    val pageContent = respPage.getJSONObject(0)
                updateMeiTuanDiscountInfo(info, guige, pageContent)
            }
        }
    }

    private suspend fun getElemeDiscount(info: GoodInfo, cookieStr: String) {
        val api = ElemeApi(cookieStr, info.shop)
        val data = QueryPageShopSkuList_Dalouy(
            data = """{"query":"{\"activityId\":\"activityId2\",\"upc\":\"wbzsx1459397914387297467\",\"verifyStatusList\":[1,2,3],\"pageIndex\":1,\"pageSize\":20,\"upcName\":\"\"}"}"""
        ).apply {
            data = data.replace(
                "wbzsx1459397914387297467",
                info.guige[0].id.toString()
            ).replace("activityId2", info.guige[0].itemActId.toString())
        }

        val result = api.postDataEle(
            "querypageshopskulist",
            JSONObject().apply { put("data", data.data) }.toString()
        )

        val obj = JSONObject(result)
        val list = obj.getJSONObject("data").getJSONObject("data").getJSONArray("list")
        if (list.length() > 0) {
            updateElemeDiscountInfo(info, list.getJSONObject(0))
        } else {
            resetDiscountInfo(info)
        }
    }

    // 美团更新原价
    private suspend fun updateMeiTuanOriginalPrice(info: GoodInfo, price: String, cookieStr: String) {
        val api = MeiTuanApi(cookieStr)
        val response = api.changeOrignPrice(info.guige[0].id, price.toDouble())
        if (response == "{\"msg\":\"success\",\"code\":0,\"data\":\"\"}") {
            updatePriceSuccess(info, price.toDouble())
        }
    }

    // 饿了么更新原价
    private suspend fun updateElemeOriginalPrice(info: GoodInfo, price: String, cookieStr: String) {
        val api = ElemeApi(cookieStr, info.shop)
        val response = api.changeOrignPrice(price, info.guige[0].itemId?.toString() ?: "")
        if (response.contains("调用成功")) {
            updatePriceSuccess(info, price.toDouble())
        }
    }

    // 美团更新折扣价
    private suspend fun updateMeiTuanDiscountPrice(info: GoodInfo, price: String, cookieStr: String) {
        val api = MeiTuanApi(cookieStr)
        val response = api.upActPrice(price.toDouble(), info.guige[0].itemActId?.toString() ?: "")
        val responseJson = JSONObject(response)
        if (responseJson.getJSONObject("data").getInt("failCount") == 0) {
            updateDiscountSuccess(info, price)
        }
    }

    // 饿了么更新折扣价
    private suspend fun updateElemeDiscountPrice(info: GoodInfo, price: String, cookieStr: String) {
        val api = ElemeApi(cookieStr, info.shop)
        val data = createElemeDiscountData(info, price)
        val response = api.postDataEle("editsku", data)
        if (response.contains("SUCCESS")) {
            updateDiscountSuccess(info, price)
        }
    }

    // 更新成功后的UI更新
    private suspend fun updatePriceSuccess(info: GoodInfo, price: Double) {
        withContext(Dispatchers.Main) {
            Toast.makeText(context, "${info.shop}原价修改为：$price 成功", Toast.LENGTH_SHORT).show()
            info.guige[0].orignprice = price
            info.price = listOf(price)
        }
    }

    private suspend fun updateDiscountSuccess(info: GoodInfo, price: String) {
        withContext(Dispatchers.Main) {
            Toast.makeText(context, "${info.shop}折扣价修改为：$price 成功", Toast.LENGTH_SHORT).show()
            info.guige[0].actprice = price
            info.actprice_min = price.toDouble()
            info.actprice_max = price.toDouble()
        }
    }

    // 库存更新成功后的UI更新
    private suspend fun updateStockSuccess(info: GoodInfo, stock: Int) {
        withContext(Dispatchers.Main) {
            Toast.makeText(context, "${info.shop}库存修改为：$stock 成功", Toast.LENGTH_SHORT).show()
            info.guige[0].stock = stock.toDouble()
            info.stock = mutableListOf(stock.toDouble())
        }
    }

    // 美团更新库存
    private suspend fun updateMeiTuanStock(info: GoodInfo, stock: String, cookieStr: String) {
        val api = MeiTuanApi(cookieStr)
        val response = api.updateStock(info.guige[0].id, stock.toInt())
        try {
            val jsonResponse = JSONObject(response)
            if (jsonResponse.optInt("code") == 0 && jsonResponse.optString("msg") == "修改成功") {
                updateStockSuccess(info, stock.toInt())
            }
        } catch (e: Exception) {
            Log.e("ProductAdapter", "美团更新库存失败: ${e.message}", e)
        }
    }

    // 饿了么更新库存
    private suspend fun updateElemeStock(info: GoodInfo, stock: String, cookieStr: String) {
        val api = ElemeApi(cookieStr, info.shop)
        val response = api.changeStock(stock, info.guige[0].itemId?.toString() ?: "")
        if (response.contains("调用成功")) {
            updateStockSuccess(info, stock.toInt())
        }
    }

    private fun createElemeDiscountData(info: GoodInfo, price: String): String {
        val data = QueryPageShopSkuList_Dalouy(
            data = """{"command":"{\"activityId\":\"activityId_num\",\"specialPrice\":specialPrice_num,\"stock\":9999,\"dayStock\":999,\"activityDayLimit\":888,\"activityLimit\":-1,\"skuShopInfos\":[{\"skuId\":\"17026997612217535\",\"itemSkuId\":null}]}"}"""
        )

        data.data = data.data
            .replace("activityId_num", info.guige[0].id)
            .replace("specialPrice_num", price)
            .replace("9999", info.guige[0].stock.toString())
            .replace("999", info.guige[0].dayStock.toString())
            .replace("888", info.guige[0].activityDayLimit.toString())
            .replace("17026997612217535", info.guige[0].itemActId)
            .replace("-1", info.guige[0].activityLimit.toString())

        return JSONObject().apply {
            put("data", data.data)
        }.toString()
    }

    private fun updateMeiTuanDiscountInfo(info: GoodInfo, guige: GuiGeInfo, pageContent: JSONObject) {
        if (guige.guige == pageContent.getString("spec")) {
            guige.actprice = pageContent.getString("actPrice")
                                            val actPrice = pageContent.getDouble("actPrice")
                                            if (actPrice >= info.actprice_max) {
                                                info.actprice_max = actPrice
                                            }
                                            if (actPrice <= info.actprice_min) {
                                                info.actprice_min = actPrice
                                            }
            guige.itemActId = pageContent.getString("itemActId")
        }
    }

    private fun updateElemeDiscountInfo(info: GoodInfo, listItem: JSONObject) {
        info.guige.forEach { moreInfo ->
                                    moreInfo.actprice = listItem.getString("specialPrice")
                                    moreInfo.activityLimit = listItem.getString("activityLimit")
            moreInfo.activityDayLimit = listItem.getString("activityDayLimit")
                                    moreInfo.dayStock = listItem.getString("dayStock")
                                    moreInfo.id = listItem.getString("activityId")

            val specialPrice = listItem.getDouble("specialPrice")
            if (specialPrice >= info.actprice_max) {
                info.actprice_max = specialPrice
                                    }
            if (specialPrice <= info.actprice_min) {
                info.actprice_min = specialPrice
                                    }
                                    moreInfo.itemActId = listItem.getString("eleItemId")
                                }
    }

    private fun resetDiscountInfo(info: GoodInfo) {
                                info.guige.forEach { moreInfo ->
                                    moreInfo.actprice = "0"
                                    info.actprice_max = 9999.0
                                    info.actprice_min = 0.0
                                }
    }

    class ProductViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        val tvProductTitle: TextView = itemView.findViewById(R.id.tv_product_title)
        val ivProductImage: ImageView = itemView.findViewById(R.id.iv_product_image)
        val rvProductInfoList: RecyclerView = itemView.findViewById(R.id.rv_product_info_list)
        val etDiscountPrice: TextView = itemView.findViewById(R.id.et_discount_price_allstore)
        val btnSaveDiscountPrice: MaterialButton = itemView.findViewById(R.id.btn_save_discount_price_allstore)
        //原价
        val etOriginalPrice: TextView = itemView.findViewById(R.id.et_original_price_allstore)
        //按钮
        val btnSaveOriginalPrice: MaterialButton = itemView.findViewById(R.id.btn_save_original_price_allstore)
        //库存
        val etStock: TextView = itemView.findViewById(R.id.et_stock_allstore)
        //库存按钮
        val btnSaveStock: MaterialButton = itemView.findViewById(R.id.btn_save_stock_allstore)
        val ivEditProduct: MaterialButton = itemView.findViewById(R.id.iv_edit_product)
        // 一键上下架按钮
        val btnBatchOnShelf: MaterialButton = itemView.findViewById(R.id.btn_batch_on_shelf)
        val btnBatchOffShelf: MaterialButton = itemView.findViewById(R.id.btn_batch_off_shelf)
    }
}


