# 京东抢资格助手 v2.0

现代化图形用户界面版本的京东政府补贴资格抢购工具。

## 功能特点

### 🎨 现代化界面
- 基于 CustomTkinter 的现代化 GUI
- 支持深色/浅色主题切换
- 响应式布局设计
- 直观的操作界面

### 🚀 智能抢购
- 保留原脚本的所有核心功能
- 智能时间调度策略
- 整点时刻密集尝试
- 自动重试机制

### 📊 实时监控
- 实时状态显示
- 进度条和倒计时
- 尝试次数统计
- 成功率监控

### 📝 日志管理
- 实时日志显示
- 日志过滤功能
- 日志导出功能
- 多级别日志记录

### ⚙️ 配置管理
- 图形化配置界面
- 配置文件保存/加载
- 参数验证
- 默认配置恢复

### ⌨️ 快捷键支持
- 常用操作快捷键
- 标签页切换
- 快速启动/停止

## 安装要求

### Python 版本
- Python 3.7 或更高版本

### 依赖包
```bash
pip install customtkinter requests
```

或者使用 requirements.txt：
```bash
pip install -r requirements.txt
```

## 使用方法

### 1. 快速启动
双击运行 `start_gui.py` 或在命令行中执行：
```bash
python start_gui.py
```

### 2. 直接启动GUI
```bash
python gui_app.py
```

### 3. 配置设置
1. 打开"配置"标签页
2. 填写有效的 Cookie 和 User-Agent 信息
3. 根据需要调整延迟时间和重试次数
4. 点击"保存配置"

### 4. 开始抢购
1. 在"监控"标签页点击"开始抢购"
2. 程序会自动运行，在整点时刻进行密集尝试
3. 成功后程序会自动停止并显示通知

## 界面说明

### 监控页面
- **运行状态**: 显示当前程序状态
- **统计信息**: 显示尝试次数、成功次数等
- **进度条**: 显示距离下个整点的进度
- **控制按钮**: 开始/停止抢购、测试连接
- **实时信息**: 显示最新的日志信息

### 配置页面
- **基本配置**: Cookie、User-Agent 设置
- **时间配置**: 延迟时间、重试次数设置
- **配置管理**: 保存、加载、重置配置

### 日志页面
- **日志控制**: 清空、导出、过滤日志
- **日志显示**: 详细的操作日志
- **过滤功能**: 按关键词或级别过滤日志

### 关于页面
- **软件信息**: 版本号、功能介绍
- **快捷键**: 所有可用快捷键列表
- **主题切换**: 深色/浅色模式切换

## 快捷键

| 快捷键 | 功能 |
|--------|------|
| Ctrl+S | 保存配置 |
| Ctrl+O | 加载配置 |
| Ctrl+L | 清空日志 |
| Ctrl+E | 导出日志 |
| Ctrl+T | 切换主题 |
| Ctrl+Q | 退出程序 |
| F1 | 显示帮助 |
| F5 | 开始抢购 |
| F6 | 停止抢购 |
| F9 | 测试连接 |
| Ctrl+1-4 | 切换标签页 |

## 时间策略

程序采用智能时间调度策略：

1. **普通时段**: 使用较长的随机延迟（默认5-10秒）
2. **接近整点**: 使用较短的延迟（默认0.5-2秒）
3. **整点时刻**: 进行密集尝试（默认5次重试）

## 注意事项

1. **Cookie 有效性**: 请确保填写的 Cookie 信息有效且未过期
2. **网络连接**: 保持稳定的网络连接
3. **启动时机**: 建议在整点前几分钟启动程序
4. **配置备份**: 重要配置建议手动备份
5. **合理使用**: 请遵守相关网站的使用条款

## 故障排除

### 常见问题

1. **导入错误**: 确保已安装所有依赖包
2. **配置丢失**: 检查 config.json 文件是否存在
3. **连接失败**: 检查网络连接和 Cookie 有效性
4. **界面异常**: 尝试重启程序或切换主题

### 日志文件

程序会自动生成日志文件保存在 `logs` 目录下，可用于问题诊断。

## 更新日志

### v2.0.0
- 全新的图形用户界面
- 现代化设计风格
- 实时状态监控
- 日志管理功能
- 配置管理功能
- 快捷键支持
- 主题切换功能

## 技术支持

如遇到问题，请检查：
1. Python 版本是否符合要求
2. 依赖包是否正确安装
3. 原脚本文件是否存在
4. 配置信息是否正确

## 免责声明

本工具仅供学习和研究使用，请遵守相关网站的使用条款和法律法规。使用本工具产生的任何后果由用户自行承担。
