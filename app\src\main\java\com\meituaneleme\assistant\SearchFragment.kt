package com.meituaneleme.assistant

import com.meituaneleme.assistant.ElemeApi
import com.meituaneleme.assistant.MeiTuanApi
import Product

import SearchHistoryManager
import android.content.Context
import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Button
import android.widget.EditText
import android.widget.TextView
import android.widget.Toast
import androidx.fragment.app.Fragment
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.google.android.material.button.MaterialButton
import com.google.android.material.chip.Chip
import com.google.android.material.chip.ChipGroup
import com.google.android.material.textfield.TextInputEditText
import kotlinx.coroutines.launch
import kotlinx.coroutines.async
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.awaitAll
import kotlinx.coroutines.withContext
import kotlinx.serialization.json.Json
import kotlinx.serialization.decodeFromString
import com.meituaneleme.assistant.model.ElePageQueryGetData
import org.json.JSONArray
import com.google.android.material.appbar.AppBarLayout

class SearchFragment : Fragment() {
    private lateinit var recyclerView: RecyclerView
    private lateinit var searchButton: MaterialButton
    private lateinit var searchInput: TextInputEditText
    private lateinit var storeCookies: MutableList<StoreAndCookies>
    private lateinit var adapter: ProductAdapter
    private lateinit var loadingOverlay: View
    private lateinit var meiTuanApi: MeiTuanApi
    private lateinit var elemeApi: ElemeApi
    lateinit var finalResM_E: MutableList<GoodsInShopsInfo>
    private val apiInstances = mutableMapOf<String, Any>()  // 存储API实例
    
    // 搜索历史相关
    private lateinit var searchHistoryManager: SearchHistoryManager
    private lateinit var chipGroupSearchHistory: ChipGroup
    private lateinit var tvClearHistory: TextView
    private lateinit var searchHistoryContainer: View
    private lateinit var appBarLayout: AppBarLayout

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        return inflater.inflate(R.layout.activity_main, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        finalResM_E = mutableListOf()
        loadingOverlay = view.findViewById(R.id.loading_overlay)
        storeCookies = mutableListOf()

        recyclerView = view.findViewById(R.id.recycler_view)
        searchButton = view.findViewById(R.id.btn_search)
        searchInput = view.findViewById(R.id.et_search)
        
        // 初始化搜索历史相关组件
        searchHistoryManager = SearchHistoryManager(requireContext())
        chipGroupSearchHistory = view.findViewById(R.id.chip_group_search_history)
        tvClearHistory = view.findViewById(R.id.tv_clear_history)
        searchHistoryContainer = view.findViewById(R.id.search_history_container)
        appBarLayout = view.findViewById(R.id.appbar_layout)

        adapter = ProductAdapter(requireContext(), mutableListOf(), storeCookies, this) { product ->
            Toast.makeText(requireContext(), "下架: ${product.productName}", Toast.LENGTH_SHORT).show()
        }
        recyclerView.adapter = adapter
        recyclerView.layoutManager = LinearLayoutManager(requireContext())

        setupSearchHistory()
        
        // 初始化历史记录显示状态
        updateSearchHistoryVisibility()

        searchButton.setOnClickListener {
            val searchWord = searchInput.text.toString()
            if (searchWord.isNotEmpty()) {
                // 添加到搜索历史
                searchHistoryManager.addSearchHistory(searchWord)
                // 更新搜索历史UI
                updateSearchHistoryUI()
                // 执行搜索
                performSearch(searchWord)
            } else {
                Toast.makeText(requireContext(), "请输入搜索内容", Toast.LENGTH_SHORT).show()
            }
        }

        // 设置搜索输入框的动作按钮
        searchInput.setOnEditorActionListener { _, actionId, _ ->
            if (actionId == android.view.inputmethod.EditorInfo.IME_ACTION_SEARCH) {
                val searchWord = searchInput.text.toString()
                if (searchWord.isNotEmpty()) {
                    // 添加到搜索历史
                    searchHistoryManager.addSearchHistory(searchWord)
                    // 更新搜索历史UI
                    updateSearchHistoryUI()
                    // 执行搜索
                    performSearch(searchWord)
                    return@setOnEditorActionListener true
                } else {
                    Toast.makeText(requireContext(), "请输入搜索内容", Toast.LENGTH_SHORT).show()
                }
            }
            false
        }

        // 点击搜索框时展开搜索历史
        searchInput.setOnFocusChangeListener { _, hasFocus ->
            if (hasFocus) {
                expandSearchHistory()
            }
        }

        getStoreCookiesFromLocal()
    }
    
    /**
     * 设置搜索历史相关功能
     */
    private fun setupSearchHistory() {
        // 初始加载搜索历史
        updateSearchHistoryUI()
        
        // 设置清空历史按钮点击事件
        tvClearHistory.setOnClickListener {
            searchHistoryManager.clearSearchHistory()
            updateSearchHistoryUI()
        }
    }
    
    /**
     * 更新搜索历史UI
     */
    private fun updateSearchHistoryUI() {
        val historyList = searchHistoryManager.getSearchHistory()
        
        // 更新搜索历史容器的可见性
        searchHistoryContainer.visibility = if (historyList.isEmpty()) View.GONE else View.VISIBLE
        
        // 清除现有的搜索历史项
        chipGroupSearchHistory.removeAllViews()
        
        // 添加搜索历史项
        for (keyword in historyList) {
            val chip = LayoutInflater.from(requireContext())
                .inflate(R.layout.item_search_history, chipGroupSearchHistory, false) as Chip
            
            chip.text = keyword
            
            // 设置点击事件 - 点击执行搜索
            chip.setOnClickListener {
                searchInput.setText(keyword)
                performSearch(keyword)
            }
            
            // 设置关闭图标点击事件 - 删除该搜索历史
            chip.setOnCloseIconClickListener {
                searchHistoryManager.removeSearchHistory(keyword)
                updateSearchHistoryUI()
            }
            
            chipGroupSearchHistory.addView(chip)
        }
    }
    
    override fun onResume() {
        super.onResume()
        // 每次恢复时更新搜索历史
        updateSearchHistoryUI()
    }

    private fun getStoreCookiesFromLocal() {
        val sharedPreferences = requireContext().getSharedPreferences("StoreCookies", Context.MODE_PRIVATE)
        val existingData = sharedPreferences.getString("storeCookiesList", "[]")
        val jsonArray = JSONArray(existingData)
        storeCookies.clear()
        for (i in 0 until jsonArray.length()) {
            val jsonObject = jsonArray.getJSONObject(i)
            val platform = jsonObject.getString("platform")
            val storeName = jsonObject.getString("storeName") + platform
            val cookies = jsonObject.getString("cookies")
            storeCookies.add(StoreAndCookies(storeName, cookies))
        }
    }

    private fun getOrCreateMeiTuanApi(cookieStr: String): MeiTuanApi {
        return apiInstances.getOrPut(cookieStr) { 
            MeiTuanApi(cookieStr) 
        } as MeiTuanApi
    }

    private fun getOrCreateElemeApi(cookieStr: String, store: String): ElemeApi {
        val key = "$cookieStr:$store"
        return apiInstances.getOrPut(key) { 
            ElemeApi(cookieStr, store) 
        } as ElemeApi
    }

    private fun performSearch(searchWord: String) {
        loadingOverlay.visibility = View.VISIBLE
        searchButton.isEnabled = false
        finalResM_E.clear()
        adapter.updateData(finalResM_E)

        lifecycleScope.launch {
            try {
                val finalResM = mutableListOf<GoodsInShopsInfo>()
                val finalResE = mutableListOf<GoodsInShopsInfo>()
                val totalStores = storeCookies.size
                var completedStores = 0

                val searchJobs = storeCookies.map { storeCookie ->
                    async(Dispatchers.IO) {
                        try {
                            val store = storeCookie.store
                            val cookieStr = storeCookie.cookie_str
                            if (store.contains("美团")) {
                                val api = getOrCreateMeiTuanApi(cookieStr)
                                val response = api.postData(searchWord)
                                val goodsInShopsInfo = dealData(response, store)
                                synchronized(finalResM) {
                                    mergeResults(goodsInShopsInfo, finalResM)
                                }
                            } else {
                                val api = getOrCreateElemeApi(cookieStr, store)
                                try {
                                    val result = api.pageQuery(searchWord)
                                    val data = try {
                                        Log.d("SearchFragment", "开始解析JSON数据")
                                        Json { 
                                            ignoreUnknownKeys = true
                                            isLenient = true
                                            prettyPrint = true
                                        }.decodeFromString<ElePageQueryGetData>(result)
                                    } catch (e: Exception) {
                                        Log.e("SearchFragment", "JSON解析错误: ${e.message}")
                                        Log.d("SearchFragment", "原始数据: $result")
                                        Log.e("SearchFragment", "详细错误: ", e)
                                        
                                        withContext(Dispatchers.Main) {
                                            Toast.makeText(
                                                context,
                                                "数据解析失败: ${e.message}\n请检查数据格式或联系客服",
                                                Toast.LENGTH_LONG
                                            ).show()
                                        }
                                        return@async
                                    }
                                    
                                    val goodsInShopsInfo = dealDataEleme(data, store)
                                    synchronized(finalResE) {
                                        mergeResults(goodsInShopsInfo, finalResE)
                                    }
                                } catch (e: Exception) {
                                    Log.e("SearchFragment", "网络请求错误: ${e.message}")
                                    withContext(Dispatchers.Main) {
                                        Toast.makeText(
                                            context,
                                            "获取数据失败: ${e.message}",
                                            Toast.LENGTH_LONG
                                        ).show()
                                    }
                                }
                            }
                        } catch (e: Exception) {
                            Log.e("SearchFragment", "搜索错误: ${e.message}", e)
                        } finally {
                            withContext(Dispatchers.Main) {
                                completedStores++
                                updateSearchProgress(completedStores, totalStores)
                            }
                        }
                    }
                }

                searchJobs.awaitAll()
                mergeFinalResults(finalResM, finalResE)
                adapter.updateData(finalResM_E)

            } catch (e: Exception) {
                Log.e("SearchFragment", "搜索过程错误: ", e)
                withContext(Dispatchers.Main) {
                    Toast.makeText(context, "搜索出错: ${e.message}", Toast.LENGTH_SHORT).show()
                }
            } finally {
                loadingOverlay.visibility = View.GONE
                searchButton.isEnabled = true
            }
        }
    }

    private fun updateSearchProgress(completed: Int, total: Int) {
        val progress = (completed.toFloat() / total * 100).toInt()
        // 更新进度显示，你可以添加一个进度条或文本显示
        activity?.title = "搜索中... $progress%"
    }

    private fun mergeResults(goodsInShopsInfo: List<GoodsInShopsInfo>, finalRes: MutableList<GoodsInShopsInfo>) {
        goodsInShopsInfo.forEach { itemB ->
            val existingItem = finalRes.find { itemA -> itemA.title == itemB.title }
            if (existingItem != null) {
                existingItem.info = existingItem.info + itemB.info
            } else {
                finalRes.add(itemB)
            }
        }
    }

    private fun mergeFinalResults(finalResM: List<GoodsInShopsInfo>, finalResE: List<GoodsInShopsInfo>) {
        finalResM.forEach { itemM ->
            val existingItem = finalResM_E.find { itemE -> itemE.title == itemM.title }
            if (existingItem != null) {
                existingItem.info = existingItem.info + itemM.info
            } else {
                finalResM_E.add(itemM)
            }
        }

        finalResE.forEach { itemE ->
            val existingItem = finalResM_E.find { itemM -> itemM.title == itemE.title }
            if (existingItem != null) {
                existingItem.info = existingItem.info + itemE.info
            } else {
                finalResM_E.add(itemE)
            }
        }
    }

    fun dealDataEleme(data: ElePageQueryGetData, store: String): List<GoodsInShopsInfo> {
        val res: MutableList<GoodsInShopsInfo> = mutableListOf()

        // 遍历数据列表
        data.data.forEach { item ->
            val stork_t: MutableList<Double> = mutableListOf(item.quantity)
            val price_t: MutableList<Double> = mutableListOf(item.price)
            val info_t: MutableList<GoodInfo> = mutableListOf()
            val moreinfo_t: MutableList<GuiGeInfo> = mutableListOf(
                GuiGeInfo(
                    guige = "默认规格",
                    orignprice = item.price,
                    actprice = "***",
                    stock = item.quantity,
                    id = item.barCode,
                    itemActId = data.activityId,
                    itemId = item.itemId.toString()
                )
            )

            info_t.add(
                GoodInfo(
                    image = item.picUrl,
                    shop = store,
                    monthsale = item.monthlySaledQuantity,
                    stock = stork_t,
                    sellStatus = if (item.itemCanSell) 1 else 0,
                    price = price_t,
                    actprice_max = 0.0,
                    actprice_min = 9999.0,
                    spuIds = item.spuId?.takeIf { it.isNotEmpty() }?.let {
                        try {
                            it.toLong()
                        } catch (e: NumberFormatException) {
                            0L
                        }
                    } ?: 0L,
                    guige = moreinfo_t
                )
            )

            val at = GoodsInShopsInfo(
                title = item.title,
                info = info_t
            )

            res.add(at)
        }

        return res
    }

    //解析 response成现需要的数据
    fun dealData(stringData: String, store: String): List<GoodsInShopsInfo> {
        val json = Json { 
            ignoreUnknownKeys = true 
            isLenient = true
        } // 忽略未知键，宽松模式
        
        try {
            val obj = json.decodeFromString<MeiTuanInterface>(stringData)
            val res = mutableListOf<GoodsInShopsInfo>()
            
            // 处理不同数据结构的情况
            val products = when {
                // 情况1: 标准结构，直接从productList获取数据
                obj.data.productList.isNotEmpty() -> obj.data.productList
                
                // 情况2: 数据在respPage.pageContent中
                obj.data.respPage?.pageContent?.isNotEmpty() == true -> obj.data.respPage.pageContent
                
                // 无数据
                else -> emptyList()
            }
            
            // 处理产品数据
            products.forEach { product ->
                val stockList = mutableListOf<Double>()
                val priceList = mutableListOf<Double>()
                val infoList = mutableListOf<GoodInfo>()
                val moreInfoList = mutableListOf<GuiGeInfo>()
    
                product.wmProductSkus.forEach { sku ->
                    stockList.add(sku.stock)
                    priceList.add(sku.price)
    
                    moreInfoList.add(
                        GuiGeInfo(
                            guige = sku.spec,
                            orignprice = sku.price,
                            actprice = "***",
                            stock = sku.stock,
                            id = sku.id.toString(), 
                            itemActId = "1"
                        )
                    )
                }
    
                infoList.add(
                    GoodInfo(
                        image = product.pictures.firstOrNull() ?: "", 
                        shop = store,
                        monthsale = product.sellCount,
                        stock = stockList,
                        spuIds = product.id,
                        sellStatus = product.sellStatus,
                        price = priceList,
                        actprice_max = 0.0,
                        actprice_min = 9999.0,
                        guige = moreInfoList
                    )
                )
    
                val goodsInShopInfo = GoodsInShopsInfo(
                    title = product.name,
                    info = infoList
                )
    
                res.add(goodsInShopInfo)
            }
            
            return res
            
        } catch (e: Exception) {
            Log.e("SearchFragment", "处理美团数据出错: ${e.message}", e)
            // 出错时返回空列表
            return emptyList()
        }
    }

    /**
     * 展开搜索历史
     */
    private fun expandSearchHistory() {
        appBarLayout.setExpanded(true, true)
    }
    
    /**
     * 收起搜索历史
     */
    private fun collapseSearchHistory() {
        appBarLayout.setExpanded(false, true)
    }
    
    /**
     * 更新搜索历史的可见性
     */
    private fun updateSearchHistoryVisibility() {
        val historyList = searchHistoryManager.getSearchHistory()
        
        // 没有搜索历史时隐藏整个容器
        if (historyList.isEmpty()) {
            searchHistoryContainer.visibility = View.GONE
        } else {
            searchHistoryContainer.visibility = View.VISIBLE
        }
    }
} 