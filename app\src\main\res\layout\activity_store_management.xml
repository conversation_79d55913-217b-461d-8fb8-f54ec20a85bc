<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical"
        android:background="@color/white">

        <!-- 标题区域 -->
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:layout_marginBottom="8dp"
            android:gravity="center"
            android:text="已绑定的门店列表↓"
            android:textColor="@color/colorPrimaryDark"
            android:textSize="16sp" />

        <!-- 门店列表区域 - 使用权重让它填充剩余空间 -->
        <androidx.cardview.widget.CardView
            android:id="@+id/card_view"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1"
            android:layout_marginHorizontal="10dp"
            android:layout_marginVertical="5dp"
            app:cardCornerRadius="10dp"
            app:cardElevation="2dp">

            <FrameLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent">

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/recycler_view"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:clipToPadding="false"
                    android:scrollbars="vertical"
                    android:paddingTop="4dp"
                    android:paddingBottom="4dp"/>

                <com.google.android.material.floatingactionbutton.FloatingActionButton
                    android:id="@+id/refresh_fab"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="top|end"
                    android:layout_margin="16dp"
                    android:src="@drawable/ic_refresh"
                    app:backgroundTint="@color/colorPrimary"
                    app:fabSize="mini" />
            </FrameLayout>
        </androidx.cardview.widget.CardView>

        <!-- 底部添加门店区域 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center"
            android:padding="12dp"
            android:background="@color/white">

            <Spinner
                android:id="@+id/platform_spinner2"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:padding="12dp"
                android:background="@drawable/spinner_background"/>

            <Button
                android:id="@+id/add_store_button"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="添加门店"
                android:background="@drawable/rounded_button"
                android:textColor="@color/white"
                android:layout_marginLeft="10dp"/>
        </LinearLayout>
    </LinearLayout>
</androidx.coordinatorlayout.widget.CoordinatorLayout>