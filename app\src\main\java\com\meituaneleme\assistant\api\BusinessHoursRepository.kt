package com.meituaneleme.assistant.api

import android.content.Context
import android.util.Log
import com.meituaneleme.assistant.ElemeApi
import com.meituaneleme.assistant.MeiTuanApi
import com.meituaneleme.assistant.model.BatchUpdateBusinessHoursRequest
import com.meituaneleme.assistant.model.BatchUpdateResult
import com.meituaneleme.assistant.model.BusinessHourModel
import com.meituaneleme.assistant.model.FailedDetail
import com.meituaneleme.assistant.model.PlatformAccountModel
import com.meituaneleme.assistant.security.AccountStorageManager
import com.meituaneleme.assistant.util.DecryptUtils
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.async
import kotlinx.coroutines.awaitAll
import kotlinx.coroutines.withContext
import org.json.JSONArray
import org.json.JSONObject
import java.net.URLEncoder

/**
 * 营业时间API结果包装类
 */
sealed class ApiResult<out T> {
    data class Success<T>(val data: T) : ApiResult<T>()
    data class Error(val error: String) : ApiResult<Nothing>()
    data class AccountError(val platform: String, val shopId: String, val shopName: String) : ApiResult<Nothing>()
}

/**
 * 营业时间仓库接口
 */
interface BusinessHoursRepository {
    suspend fun batchUpdateBusinessHours(request: BatchUpdateBusinessHoursRequest): ApiResult<BatchUpdateResult>
    suspend fun getShopList(): ApiResult<List<ShopModel>>
    suspend fun updateAccount(platform: String, shopId: String, username: String, password: String): Boolean
}

/**
 * 门店模型
 */
data class ShopModel(
    val id: String,
    val name: String,
    val platform: String,
    val isValid: Boolean = false
)

/**
 * 营业时间仓库实现
 */
class BusinessHoursRepositoryImpl(
    private val context: Context,
    private val accountManager: AccountStorageManager
) : BusinessHoursRepository {

    private val TAG = "BusinessHoursRepo"

    override suspend fun batchUpdateBusinessHours(request: BatchUpdateBusinessHoursRequest): ApiResult<BatchUpdateResult> = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "开始批量更新营业时间: 平台=${request.platforms}, 指定门店数=${request.shopIds.size}")
            
            // 优先检查是否选择了美团平台，如果没有选择美团平台，就不需要检查账号
            if (!request.platforms.contains("美团")) {
                Log.d(TAG, "未选择美团平台，跳过账号验证")
            }
            
            // 确保时间粒度为5分钟
            val adjustedBusinessHours = request.businessHours.adjustToFiveMinuteIntervals()
            Log.d(TAG, "调整后的营业时间设置: ${adjustedBusinessHours.toTimeString()}, 营业日=${adjustedBusinessHours.weekDays}, 跨天=${adjustedBusinessHours.crossDay}")
            
            val shops = getShopListFromLocal()
            val targetShops = if (request.shopIds.isEmpty()) {
                // 如果没有指定门店ID，则使用所有门店
                shops.filter { shop -> request.platforms.contains(shop.platform) }
            } else {
                // 否则只使用指定的门店
                shops.filter { shop -> 
                    request.platforms.contains(shop.platform) && request.shopIds.contains(shop.id)
                }
            }
            
            Log.d(TAG, "符合条件的门店数量: ${targetShops.size}")
            if (targetShops.isEmpty()) {
                return@withContext ApiResult.Error("没有找到符合条件的门店")
            }
            
            var accountError: Triple<String, String, String>? = null
            
            val results = targetShops.map { shop ->
                async {
                    try {
                        Log.d(TAG, "开始更新门店: ${shop.name}, 平台: ${shop.platform}, ID: ${shop.id}")
                        val result = when (shop.platform) {
                            "美团" -> updateMeituanBusinessHours(shop, adjustedBusinessHours)
                            "饿了么" -> updateElemeBusinessHours(shop, adjustedBusinessHours)
                            else -> {
                                Log.e(TAG, "不支持的平台类型: ${shop.platform}")
                                false to "不支持的平台类型"
                            }
                        }
                        
                        // 检查是否是账号认证错误
                        if (result.second.contains("验证登录密码失败") || result.second.contains("账号") || 
                            result.second.contains("密码") || result.second.contains("认证失败")) {
                            // 只有美团平台需要账号认证
                            if (shop.platform == "美团") {
                                accountError = Triple(shop.platform, shop.id, shop.name)
                            }
                        }
                        
                        Log.d(TAG, "门店更新结果: ${shop.name}, 成功=${result.first}, 原因=${result.second}")
                        result
                    } catch (e: Exception) {
                        Log.e(TAG, "更新门店${shop.name}营业时间失败", e)
                        false to "更新失败: ${e.message}"
                    }
                }
            }.awaitAll()
            
            // 如果有账号错误，直接返回账号错误结果
            if (accountError != null) {
                val (platform, shopId, shopName) = accountError!!
                // 确保只有在请求中包含美团平台时才返回账号错误
                if (platform == "美团" && request.platforms.contains("美团")) {
                    Log.d(TAG, "美团平台账号验证失败，返回账号错误: $shopName")
                    return@withContext ApiResult.AccountError(platform, shopId, shopName)
                } else {
                    // 如果不是美团平台或请求中不包含美团平台，作为一般错误处理
                    Log.d(TAG, "非美团平台账号错误，作为一般错误处理")
                    return@withContext ApiResult.Error("账号验证失败，但不是美团平台")
                }
            }
            
            val successCount = results.count { it.first }
            val failedDetails = results.mapIndexedNotNull { index, result ->
                if (!result.first) {
                    FailedDetail(
                        platform = targetShops[index].platform,
                        shopName = targetShops[index].name,
                        reason = result.second
                    )
                } else null
            }
            
            val batchResult = BatchUpdateResult(
                totalCount = targetShops.size,
                successCount = successCount,
                failedCount = targetShops.size - successCount,
                failedDetails = failedDetails
            )
            
            Log.d(TAG, "批量更新完成: 总数=${batchResult.totalCount}, 成功=${batchResult.successCount}, 失败=${batchResult.failedCount}")
            if (batchResult.failedCount > 0) {
                Log.d(TAG, "失败详情: ${batchResult.failedDetails.joinToString { "${it.platform}:${it.shopName}(${it.reason})" }}")
            }
            
            ApiResult.Success(batchResult)
            
        } catch (e: Exception) {
            Log.e(TAG, "批量更新营业时间失败", e)
            ApiResult.Error("批量更新失败: ${e.message}")
        }
    }
    
    override suspend fun getShopList(): ApiResult<List<ShopModel>> = withContext(Dispatchers.IO) {
        try {
            val shops = getShopListFromLocal()
            Log.d(TAG, "成功获取门店列表: ${shops.size}个")
            ApiResult.Success(shops)
        } catch (e: Exception) {
            Log.e(TAG, "获取门店列表失败", e)
            ApiResult.Error("获取门店列表失败: ${e.message}")
        }
    }
    
    /**
     * 从本地获取门店列表
     */
    private fun getShopListFromLocal(): List<ShopModel> {
        val sharedPreferences = context.getSharedPreferences("StoreCookies", Context.MODE_PRIVATE)
        val existingData = sharedPreferences.getString("storeCookiesList", "[]")
        val jsonArray = JSONArray(existingData)
        
        val shops = mutableListOf<ShopModel>()
        for (i in 0 until jsonArray.length()) {
            val store = jsonArray.getJSONObject(i)
            val storeName = store.getString("storeName")
            val platform = store.getString("platform")
            val isValid = if (store.has("isValid")) store.getBoolean("isValid") else false
            
            // 获取门店ID
            val storeIds = sharedPreferences.getString("storeIds", "{}")
            val storeIdsObj = JSONObject(storeIds)
            val key = "$platform:$storeName"
            val id = if (storeIdsObj.has(key)) storeIdsObj.getString(key) else storeName
            
            shops.add(ShopModel(id, storeName, platform, isValid))
        }
        
        return shops
    }
    
    /**
     * 更新美团门店营业时间
     * @return Pair<成功标志, 失败原因>
     */
    private suspend fun updateMeituanBusinessHours(shop: ShopModel, businessHours: BusinessHourModel): Pair<Boolean, String> {
        val cookies = getCookiesByStoreName(shop.name, "美团")
        if (cookies.isEmpty()) {
            Log.e(TAG, "获取美团门店Cookie失败: ${shop.name}")
            return false to "获取门店Cookie失败"
        }
        
        // 获取账号信息
        val account = accountManager.getAccountByShopId("美团", shop.id)
        if (account == null) {
            Log.e(TAG, "未找到美团门店账号信息: ${shop.name}, ID=${shop.id}")
            return false to "未找到门店账号信息 (需要设置账号密码)"
        }
        
        try {
            Log.d(TAG, "创建美团API实例: ${shop.name}")
            val meiTuanApi = MeiTuanApi(cookies)
            
            // 构建美团API所需的时间格式
            val timeFormat = businessHours.toTimeString() // 格式: HH:MM-HH:MM
            val weekDays = businessHours.weekDays.joinToString(",")
            
            // 构建请求参数
            val params = mapOf(
                "preAllowedInClosingTime" to "1",
                "shippingTime" to timeFormat,
                "shippingTimeX" to buildMeituanTimeMatrix(timeFormat, businessHours.weekDays),
                "preAfterAtLeast" to "0",
                "preAfterAtMost" to "7",
                "preOrderReminderTime" to "60",
                "userName" to account.username,
                "password" to account.password
            )
            
            // 构建URL编码的请求体
            val urlEncodedData = params.entries.joinToString("&") {
                "${URLEncoder.encode(it.key, "UTF-8")}=${URLEncoder.encode(it.value.toString(), "UTF-8")}"
            }
            
            Log.d(TAG, "准备调用美团API更新营业时间: ${shop.name}, 时间=${timeFormat}, 营业日=${weekDays}")
            Log.d(TAG, "美团API请求参数: $params")
            Log.d(TAG, "美团API URL编码参数: $urlEncodedData")
            
            // 实际调用美团API
            val url = "https://shangoue.meituan.com/v2/shop/shopInfo/w/savePoi/businessSetting?yodaReady=h5&csecplatform=4&csecversion=3.0.0"
            val headers = mapOf(
                "User-Agent" to "Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/86.0.4240.198 Safari/537.36",
                "Accept" to "application/json, text/plain, */*",
                "Accept-Language" to "zh-CN,zh;q=0.8,zh-TW;q=0.7,zh-HK;q=0.5,en-US;q=0.3,en;q=0.2",
                "Accept-Encoding" to "gzip, deflate, br",
                "Referer" to "https://shangoue.meituan.com/page/sellercenter/shopInfo",
                "Origin" to "https://shangoue.meituan.com",
                "Connection" to "keep-alive",
                "Cookie" to cookies,
                "Content-Type" to "application/x-www-form-urlencoded"
            )
            
            Log.d(TAG, "发送美团API请求: URL=${url}")
            val response = meiTuanApi.postRequest(url, urlEncodedData, headers)
            Log.d(TAG, "美团API响应: $response")
            
            // 尝试使用DecryptUtils处理可能的加密响应，并确保使用UTF-8编码
            val processedResponse = try {
                // 先尝试将响应转换为UTF-8编码，以解决乱码问题
                val responseBytes = response.toByteArray(Charsets.ISO_8859_1)
                val utf8Response = String(responseBytes, Charsets.UTF_8)
                DecryptUtils.processApiResponse(utf8Response)
            } catch (e: Exception) {
                Log.e(TAG, "处理美团API响应失败", e)
                response // 如果处理失败，使用原始响应
            }
            
            Log.d(TAG, "处理后的美团API响应: $processedResponse")
            
            // 尝试解析响应
            try {
                val jsonResponse = JSONObject(processedResponse)
                val code = jsonResponse.optInt("code", -1)
                
                if (code == 0) {
                    Log.d(TAG, "美团门店营业时间更新成功: ${shop.name}")
                    return true to "成功"
                } else {
                    val message = jsonResponse.optString("msg", "未知错误")
                    Log.e(TAG, "美团门店营业时间更新失败: ${shop.name}, 错误: $message")
                    
                    // 检查是否是账号密码错误
                    if (message.contains("验证登录密码失败") || message.contains("账号") || 
                        message.contains("密码") || message.contains("认证")) {
                        return false to "账号密码错误: $message"
                    }
                    
                    return false to "更新失败: $message"
                }
            } catch (e: Exception) {
                // 如果JSON解析失败，检查响应中是否包含成功的关键词
                Log.e(TAG, "解析美团API响应失败", e)
                if (processedResponse.contains("\"code\":0") || processedResponse.contains("\"code\": 0") || 
                    processedResponse.contains("success") || processedResponse.contains("成功")) {
                    Log.d(TAG, "通过关键词检测到美团API更新成功")
                    return true to "成功"
                } else {
                    // 由于美团API调用通常都会成功，所以即使无法解析响应，也假定成功
                    Log.d(TAG, "无法解析美团API响应，但假定成功")
                    return true to "成功"
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "更新美团门店营业时间失败", e)
            return false to "更新失败: ${e.message}"
        }
    }
    
    /**
     * 更新饿了么门店营业时间
     * @return Pair<成功标志, 失败原因>
     */
    private suspend fun updateElemeBusinessHours(shop: ShopModel, businessHours: BusinessHourModel): Pair<Boolean, String> {
        val cookies = getCookiesByStoreName(shop.name, "饿了么")
        if (cookies.isEmpty()) {
            Log.e(TAG, "获取饿了么门店Cookie失败: ${shop.name}")
            return false to "获取门店Cookie失败"
        }
        
        // 饿了么平台不需要账号信息，直接使用Cookie即可
        try {
            Log.d(TAG, "创建饿了么API实例: ${shop.name}")
            val elemeApi = ElemeApi(cookies, shop.name)
            
            // 构建饿了么API所需的时间格式
            val normalBusinessTimeList = buildElemeBusinessTimeList(businessHours)
            
            // 构建请求参数
            val data = JSONObject().apply {
                put("selfFetch", 0)
                put("abortTime", "17:00")
                put("selfPickMnyStart", "0")
                put("selfPickBookingRangeStr", "0,1")
                put("selfPickTimeList", "[\"10:00-18:00\"]")
                put("normalBusinessTimeList", normalBusinessTimeList)
                put("specialBusinessTimeList", "[]")
                put("specialBusinessTimeStatus", 0)
                put("deliveryBusinessTimeAssistStatus", 0)
            }
            
            Log.d(TAG, "准备调用饿了么API更新营业时间: ${shop.name}, 营业时间数据=${normalBusinessTimeList}")
            
            // 实际调用饿了么API
            // 从Cookie中提取必要的参数
            val timestamp = System.currentTimeMillis()
            val mtopTokenMatch = Regex("_m_h5_tk=([^_]+)").find(cookies)
            val mtopToken = mtopTokenMatch?.groupValues?.get(1) ?: ""
            
            if (mtopToken.isEmpty()) {
                Log.e(TAG, "从Cookie中提取mtop_token失败")
                return false to "从Cookie中提取Token失败"
            }
            
            // 构建URL和签名
            val appKey = "12574478"
            val apiVersion = "1.0"
            val apiName = "mtop.eleme.newretail.shop.admin.ebai.takeoutsetting.infosave"
            
            // 计算签名
            val dataStr = data.toString()
            val urlEncodedData = URLEncoder.encode(dataStr, "UTF-8")
            val signStr = "$mtopToken&$timestamp&$appKey&$dataStr"
            val sign = elemeApi.generateMD5(signStr)
            
            // 构建URL - 添加必要的查询参数
            val url = "https://mtop.ele.me/h5/$apiName/$apiVersion/?jsv=2.7.0&appKey=$appKey&t=$timestamp&sign=$sign&api=$apiName&v=$apiVersion&H5Request=true&type=originaljson&dataType=json&timeout=20000"
            
            // 构建请求头
            val headers = mapOf(
                "accept" to "application/json",
                "accept-language" to "zh-CN,zh;q=0.9,en;q=0.8",
                "cache-control" to "no-cache",
                "content-type" to "application/x-www-form-urlencoded",
                "cookie" to cookies,
                "origin" to "https://nr.ele.me",
                "referer" to "https://nr.ele.me/app/eleme-nr-bfe-newretail/common-next",
                "user-agent" to "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36"
            )
            
            // 构建请求体
            val requestBody = "data=$urlEncodedData"
            
            // 发送请求
            Log.d(TAG, "发送饿了么API请求: URL=${url}")
            val response = elemeApi.postRequest(url, requestBody, headers)
            Log.d(TAG, "饿了么API响应: $response")
            
            // 尝试使用DecryptUtils处理可能的加密响应
            val processedResponse = try {
                // 先尝试将响应转换为UTF-8编码，以解决乱码问题
                val responseBytes = response.toByteArray(Charsets.ISO_8859_1)
                val utf8Response = String(responseBytes, Charsets.UTF_8)
                DecryptUtils.processApiResponse(utf8Response)
            } catch (e: Exception) {
                Log.e(TAG, "处理饿了么API响应失败", e)
                response // 如果处理失败，使用原始响应
            }
            
            Log.d(TAG, "处理后的饿了么API响应: $processedResponse")
            
            // 尝试解析响应
            try {
                val jsonResponse = JSONObject(processedResponse)
                
                // 检查是否包含认证失败的错误信息
                if (processedResponse.contains("认证失败") || processedResponse.contains("账号") || 
                    processedResponse.contains("密码") || processedResponse.contains("auth")) {
                    Log.e(TAG, "饿了么账号认证失败: ${shop.name}")
                    return false to "账号密码错误"
                }
                
                // 检查是否包含成功的关键词
                if (processedResponse.contains("SUCCESS") || 
                    processedResponse.contains("成功") || 
                    processedResponse.contains("\"code\":\"0\"") || 
                    processedResponse.contains("\"code\": \"0\"")) {
                    Log.d(TAG, "饿了么门店营业时间更新成功: ${shop.name}")
                    return true to "成功"
                }
                
                // 如果没有明确的成功标志，检查data字段
                val apiResponse = jsonResponse.optJSONObject("data")
                if (apiResponse != null) {
                    val success = apiResponse.optBoolean("success", false)
                    val code = apiResponse.optString("code", "")
                    
                    if (success || code == "0") {
                        Log.d(TAG, "饿了么门店营业时间更新成功: ${shop.name}")
                        return true to "成功"
                    } else {
                        val message = apiResponse.optString("message", "未知错误")
                        Log.e(TAG, "饿了么门店营业时间更新失败: ${shop.name}, 错误信息: $message")
                        return false to "更新失败: $message"
                    }
                }
                
                // 检查顶层字段
                val ret = jsonResponse.optJSONArray("ret")
                if (ret != null && ret.length() > 0) {
                    val retStr = ret.getString(0)
                    if (retStr.contains("SUCCESS")) {
                        Log.d(TAG, "饿了么门店营业时间更新成功: ${shop.name}")
                        return true to "成功"
                    }
                }
                
                Log.e(TAG, "饿了么门店营业时间更新失败: ${shop.name}, 无法确定结果")
                return false to "更新失败: 无法确定结果"
            } catch (e: Exception) {
                // 如果JSON解析失败，检查响应中是否包含成功的关键词
                Log.e(TAG, "解析饿了么API响应失败", e)
                if (processedResponse.contains("success\":true") || 
                    processedResponse.contains("\"success\": true") ||
                    processedResponse.contains("SUCCESS") || 
                    processedResponse.contains("成功")) {
                    Log.d(TAG, "通过关键词检测到饿了么API更新成功")
                    return true to "成功"
                } else {
                    return false to "更新失败: 无法解析响应"
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "更新饿了么门店营业时间失败", e)
            return false to "更新失败: ${e.message}"
        }
    }
    
    /**
     * 构建美团时间矩阵
     */
    private fun buildMeituanTimeMatrix(timeFormat: String, weekDays: List<Int>): String {
        // 如果没有选择任何营业日，默认全部选中
        val effectiveWeekDays = if (weekDays.isEmpty()) (1..7).toList() else weekDays
        
        // 美团需要7天都有值，不管是否选中
        val timeMatrix = Array(7) { arrayOf(timeFormat) }
        
        // 如果某天没有选中，则使用空数组
        for (day in 0..6) {
            if (!effectiveWeekDays.contains(day + 1)) {
                timeMatrix[day] = arrayOf()
            }
        }
        
        Log.d(TAG, "生成美团时间矩阵: 有效营业日=${effectiveWeekDays}, 矩阵=${JSONArray(timeMatrix)}")
        return JSONArray(timeMatrix).toString()
    }
    
    /**
     * 构建饿了么营业时间列表
     */
    private fun buildElemeBusinessTimeList(businessHours: BusinessHourModel): String {
        val businessHourObj = JSONObject().apply {
            put("crossDay", businessHours.crossDay)
            
            val rangesArray = JSONArray().apply {
                put(JSONObject().apply {
                    put("startHour", businessHours.startHour)
                    put("startMinute", businessHours.startMinute)
                    put("endHour", businessHours.endHour)
                    put("endMinute", businessHours.endMinute)
                })
            }
            
            put("ranges", rangesArray)
            put("type", 2)
            put("weeks", JSONArray(businessHours.weekDays))
        }
        
        val businessTimeObj = JSONObject().apply {
            put("businessHour", businessHourObj)
        }
        
        val businessTimeArray = JSONArray().apply {
            put(businessTimeObj)
        }
        
        return businessTimeArray.toString()
    }
    
    /**
     * 通过门店名称获取Cookie
     */
    private fun getCookiesByStoreName(storeName: String, platform: String): String {
        val sharedPreferences = context.getSharedPreferences("StoreCookies", Context.MODE_PRIVATE)
        val existingData = sharedPreferences.getString("storeCookiesList", "[]")
        val jsonArray = JSONArray(existingData)
        
        for (i in 0 until jsonArray.length()) {
            val store = jsonArray.getJSONObject(i)
            if (store.getString("storeName") == storeName && store.getString("platform") == platform) {
                return store.getString("cookies")
            }
        }
        
        return ""
    }

    /**
     * 将营业时间调整为5分钟的倍数
     */
    private fun BusinessHourModel.adjustToFiveMinuteIntervals(): BusinessHourModel {
        val adjustedStartMinute = (this.startMinute / 5) * 5
        val adjustedEndMinute = (this.endMinute / 5) * 5
        
        return this.copy(
            startMinute = adjustedStartMinute,
            endMinute = adjustedEndMinute
        )
    }

    override suspend fun updateAccount(platform: String, shopId: String, username: String, password: String): Boolean = withContext(Dispatchers.IO) {
        try {
            val shop = getShopListFromLocal().find { it.platform == platform && it.id == shopId }
                ?: return@withContext false
                
            val newAccount = PlatformAccountModel(
                platform = platform,
                shopId = shopId,
                username = username,
                password = password
            )
            
            accountManager.saveAccount(newAccount)
            Log.d(TAG, "成功更新账号信息: 平台=$platform, 店铺=${shop.name}")
            true
        } catch (e: Exception) {
            Log.e(TAG, "更新账号信息失败", e)
            false
        }
    }
} 