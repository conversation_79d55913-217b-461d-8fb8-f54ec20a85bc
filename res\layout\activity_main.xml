<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>商品界面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background-color: #f5f5f5;
            margin: 0;
            padding: 20px;
        }
        .product-card {
            display: flex;
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
            padding: 10px;
        }
        .product-image {
            width: 100px;
            height: 100px;
            border-radius: 10px;
            margin-right: 10px;
        }
        .product-info {
            flex: 1;
        }
        .product-name {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 5px;
        }
        .store-info {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .store-name {
            font-size: 14px;
            color: #666;
        }
        .price {
            color: red;
            font-size: 16px;
            font-weight: bold;
        }
        .discount-price {
            color: #888;
            text-decoration: line-through;
            margin-left: 5px;
        }
        .action-button {
            background-color: #f0ad4e;
            color: white;
            border: none;
            border-radius: 5px;
            padding: 5px 10px;
            cursor: pointer;
        }
    </style>
</head>
<body>

<div class="product-card">
    <img src="product.jpg" alt="商品图片" class="product-image">
    <div class="product-info">
        <div class="product-name">商品名称</div>
        <div class="store-info">
            <div class="store-name">门店名称</div>
            <div>
                <span class="price">¥498</span>
                <span class="discount-price">¥188.0</span>
            </div>
            <button class="action-button">下架</button>
        </div>
    </div>
</div>

</body>
</html>