// ProductSpecAdapter.kt
package com.meituaneleme.assistant

import com.meituaneleme.assistant.ElemeApi
import com.meituaneleme.assistant.MeiTuanApi
import android.app.Activity
import android.content.Context
import android.content.Intent
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Button
import android.widget.EditText
import android.widget.TextView
import android.widget.Toast
import androidx.recyclerview.widget.RecyclerView
import com.google.android.material.textfield.TextInputEditText
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import org.json.JSONObject

data class ProductSpec(
    val spec: String,
    val originalPrice: Double,
    val discountPrice: Double,
    val stock: Double
)

class ProductSpecAdapter(
    private val context: Context,
    private val productInfos: List<GuiGeInfo>,
    private val cookie_str: String,
    private val storeName: String,

) :
    RecyclerView.Adapter<ProductSpecAdapter.ProductSpecViewHolder>() {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ProductSpecViewHolder {
        val view = LayoutInflater.from(parent.context).inflate(R.layout.item_product_spec, parent, false)
        return ProductSpecViewHolder(view)
    }

    override fun onBindViewHolder(holder: ProductSpecViewHolder, position: Int) {
        val productSpec = productInfos[position]
        holder.tvSpec.text = productSpec.guige
        holder.etOriginalPrice.setText(productSpec.orignprice.toString())
        holder.etDiscountPrice.setText(productSpec.actprice.toString())
        holder.etStock.setText(productSpec.stock.toInt().toString())

        holder.btnSaveOriginalPrice.setOnClickListener {
            // 保存原价逻辑
            //调用MeiTuanApi的changeOrignPrice方法
            GlobalScope.launch {
                if (holder.etOriginalPrice.text.toString().isEmpty()){
                    return@launch
                }
                else if(storeName.contains("美团")){
                    val api = MeiTuanApi(cookie_str)
                    val postResult = api.changeOrignPrice(
                        productSpec.id,
                        holder.etOriginalPrice.text.toString().toDouble()
                    )
                    if (postResult == "{\"msg\":\"success\",\"code\":0,\"data\":\"\"}") {
                        //按钮变绿
                        (context as Activity).runOnUiThread {
                            Toast.makeText(context, "修改成功", Toast.LENGTH_SHORT).show()
                        }
                    } else {
                        //按钮变红
                        holder.btnSaveOriginalPrice.setBackgroundColor(context.resources.getColor(R.color.red))
                    }
                }
                else{
                    //饿了么
                    val api = ElemeApi(cookie_str,storeName)
                    val postResult = api.changeOrignPrice(
                        holder.etOriginalPrice.text.toString(),
                        productSpec.itemId?.toString() ?: ""
                    )
                    if (postResult.contains("调用成功")) {
                        //按钮变绿
                        (context as Activity).runOnUiThread {
                            Toast.makeText(context, "修改成功", Toast.LENGTH_SHORT).show()
                        }
                    } else {
                        //按钮变红
                        holder.btnSaveOriginalPrice.setBackgroundColor(context.resources.getColor(R.color.red))
                    }

                }
            }
        }

        holder.btnSaveDiscountPrice.setOnClickListener {
            GlobalScope.launch {
                if (holder.etDiscountPrice.text.toString().isEmpty()){
                    return@launch
                }
                else if(storeName.contains("美团")){
                    val api = MeiTuanApi(cookie_str)
                    val postResult = api.upActPrice(
                        holder.etDiscountPrice.text.toString().toDouble(),
                        productSpec.itemActId?.toString() ?: "",

                    )
                    //postResult转成json
                    val postResult_json = JSONObject(postResult)
                    
                    // 检查响应状态码和数据
                    if (postResult_json.optInt("code", 0) == 400 || postResult_json.isNull("data")) {
                        // 处理错误情况
                        val errorMessage = postResult_json.optString("message", "修改失败")
                        holder.btnSaveDiscountPrice.setBackgroundColor(context.resources.getColor(R.color.red))
                        (context as Activity).runOnUiThread {
                            Toast.makeText(context, errorMessage, Toast.LENGTH_SHORT).show()
                        }
                    } else {
                        // 正常处理数据
                        val dataObj = postResult_json.getJSONObject("data")
                        val failCount = dataObj.optInt("failCount", 0)
                        if (failCount == 0) {
                            //按钮变绿
    //                        holder.btnSaveDiscountPrice.setBackgroundColor(context.resources.getColor(R.color.green))
                            (context as Activity).runOnUiThread {
                                Toast.makeText(context, "修改成功", Toast.LENGTH_SHORT).show()
                            }
                        } else {
                            //按钮变红
                            holder.btnSaveDiscountPrice.setBackgroundColor(context.resources.getColor(R.color.red))
                            //弹toast
                            val failMsg = dataObj.optString("failMsg", "修改失败")
                            (context as Activity).runOnUiThread {
                                Toast.makeText(context, failMsg, Toast.LENGTH_SHORT).show()
                            }
                        }
                    }
                }
                else{
                    //饿了么
                    val api = ElemeApi(cookie_str,storeName)
                    val data = QueryPageShopSkuList_Dalouy(
                        data = """{"command":"{\"activityId\":\"activityId_num\",\"specialPrice\":specialPrice_num,\"stock\":9999,\"dayStock\":999,\"activityDayLimit\":888,\"activityLimit\":-1,\"skuShopInfos\":[{\"skuId\":\"17026997612217535\",\"itemSkuId\":null}]}"}"""
                    )

                    data.data = data.data.replace("activityId_num", productSpec.id.toString())
                        .replace("specialPrice_num",  holder.etDiscountPrice.text.toString())
                        .replace("9999", productSpec.stock.toString())
                        .replace("999", productSpec.dayStock.toString())
                        .replace("888", productSpec.activityDayLimit.toString())
                        .replace("17026997612217535", productSpec.itemActId.toString())
                        .replace("-1", productSpec.activityLimit.toString())

                    val dataLoay_str=JSONObject().apply {
                        put("data", data.data) }.toString()

                    val result = api.postDataEle("editsku", dataLoay_str)
                    if (result.contains("SUCCESS")) {
                        //按钮变绿
//                        holder.btnSaveDiscountPrice.setBackgroundColor(context.resources.getColor(R.color.green))
                        (context as Activity).runOnUiThread {
                            Toast.makeText(context, "修改成功", Toast.LENGTH_SHORT).show()
                        }
                    } else {
                        //按钮变红
                        holder.btnSaveDiscountPrice.setBackgroundColor(context.resources.getColor(R.color.red))
                    }

                }
            }
        }

        // 添加库存修改功能
        holder.btnSaveStock.setOnClickListener {
            val newStock = holder.etStock.text.toString()
            if (newStock.isEmpty()) {
                Toast.makeText(context, "请输入库存数量", Toast.LENGTH_SHORT).show()
                return@setOnClickListener
            }

            GlobalScope.launch(Dispatchers.IO) {
                try {
                    val result = if (storeName.contains("美团")) {
                        updateMeiTuanStock(productSpec.id, newStock.toInt(), cookie_str)
                    } else {
                        updateElemeStock(productSpec, newStock.toInt(), cookie_str, storeName)
                    }

                    withContext(Dispatchers.Main) {
                        if (result) {
                            Toast.makeText(context, "库存修改成功", Toast.LENGTH_SHORT).show()
                            // 更新本地数据
                            productSpec.stock = newStock.toDouble()
                        } else {
                            Toast.makeText(context, "库存修改失败", Toast.LENGTH_SHORT).show()
                            holder.btnSaveStock.setBackgroundColor(context.resources.getColor(R.color.red))
                        }
                    }
                } catch (e: Exception) {
                    withContext(Dispatchers.Main) {
                        Toast.makeText(context, "修改出错: ${e.message}", Toast.LENGTH_SHORT).show()
                        holder.btnSaveStock.setBackgroundColor(context.resources.getColor(R.color.red))
                    }
                }
            }
        }
    }

    /**
     * 更新美团平台商品库存
     */
    private suspend fun updateMeiTuanStock(skuId: String, stock: Int, cookieStr: String): Boolean {
        val api = MeiTuanApi(cookieStr)
        val response = api.updateStock(skuId, stock)
        
        return try {
            val jsonResponse = JSONObject(response)
            jsonResponse.optInt("code") == 0 && jsonResponse.optString("msg") == "修改成功"
        } catch (e: Exception) {
            false
        }
    }

    /**
     * 更新饿了么平台商品库存
     */
    private suspend fun updateElemeStock(productSpec: GuiGeInfo, stock: Int, cookieStr: String, storeName: String): Boolean {
        val api = ElemeApi(cookieStr, storeName)
        val response = api.changeStock(stock.toString(), productSpec.itemId?.toString() ?: "")
        
        // 检查返回结果是否包含调用成功字符串
        return response.contains("调用成功")
    }

    override fun getItemCount(): Int = productInfos.size

    class ProductSpecViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        val tvSpec: TextView = itemView.findViewById(R.id.tv_spec)
        val etOriginalPrice: TextInputEditText = itemView.findViewById(R.id.et_original_price)
        val etDiscountPrice: TextInputEditText = itemView.findViewById(R.id.et_discount_price)
        val etStock: TextInputEditText = itemView.findViewById(R.id.et_stock)
        val btnSaveOriginalPrice: Button = itemView.findViewById(R.id.btn_save_original_price)
        val btnSaveDiscountPrice: Button = itemView.findViewById(R.id.btn_save_discount_price)
        val btnSaveStock: Button = itemView.findViewById(R.id.btn_save_stock)
    }
}