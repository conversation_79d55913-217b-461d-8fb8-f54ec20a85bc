package com.meituaneleme.assistant

import com.meituaneleme.assistant.ElemeApi
import LeanCloudRequest
import com.meituaneleme.assistant.MeiTuanApi
import Product
import android.content.Intent
import android.os.Bundle
import android.os.Parcel
import android.os.Parcelable
import android.util.Log
import android.view.View
import android.widget.Button
import android.widget.EditText
import android.widget.Toast
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.meituaneleme.assistant.api.StoreCookies
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import kotlinx.serialization.json.Json
import org.json.JSONArray

@kotlinx.serialization.Serializable
data class ElePageQueryGetData(
    val data: List<ElePageQueryData>,
    val activityId: String
)

@kotlinx.serialization.Serializable
data class GeneratedTypeLiteralInterface1(
    val catePath: String,
    val channels: String? = null,
    val count: Int,
    val customCategoryId: Int,
    val customCategoryName: String,
    val customCategoryParentId: String? = null,
    val customCategoryParentName: String? = null,
    val depth: Int,
    val eleCustomCategoryId: Long,
    val eleCustomCategoryParentId: String? = null,
    val hasChildren: String? = null,
    val image: String? = null,
    val itemRank: String? = null,
    val itemTop: String? = null,
    val rank: Int,
    val shopCustomId: String? = null,
    val smartSort: Boolean? = null,
    val top: String? = null
)

@kotlinx.serialization.Serializable
data class GeneratedTypeLiteralInterface2(
    val isMaster: Boolean,
    val levelSource: String? = null,
    val url: String
)

@kotlinx.serialization.Serializable
data class GeneratedTypeLiteralInterface3(
    val endDate: String? = null,
    val endDeliveryDate: String? = null,
    val latestDeliveryDate: String? = null,
    val preSaleType: String? = null,
    val startDate: String? = null,
    val startDeliveryDate: String? = null
)

@kotlinx.serialization.Serializable
data class ElePageQueryData(
    val agentProduct: String? = null,
    val associatedItemSum: String? = null,
    val auctionStatus: String? = null,
    val autoSetQuantity: String? = null,
    val autoSetQuantityNum: String? = null,
    val autoShelf: Boolean,
    val barCode: String,
    val bdOp: Boolean? = null,
    val brandId: String? = null,
    val brandName: String? = null,
    val businessFormId: String? = null,
    val buyLimitEndDate: String? = null,
    val buyLimitNum: String? = null,
    val buyLimitRuleDto: String? = null,
    val buyLimitRuleFlag: Boolean,
    val buyLimitStartDate: String? = null,
    val buyLimitType: String? = null,
    val cateId: Int,
    val cateId1: Int,
    val cateId2: Int,
    val cateName: String,
    val cateName1: String,
    val cateName2: String,
    val chargeUnit: String? = null,
    val chatBuyLightItem: Boolean? = null,
    val code: Int,
    val combineItemIdList: List<String> = emptyList(),
    val combineItemSize: Int,
    val combineMainItemIdList: List<String> = emptyList(),
    val combineMainItemSize: Int,
    val combineMainProductSize: String? = null,
    val combineProductSize: String? = null,
    val combineSubItemMarketingDTO: String? = null,
    val combineSubItemVOList: String? = null,
    val combineSubProductList: String? = null,
    val combineSubType: String? = null,
    val consignmentSale: String? = null,
    val control: Boolean? = null,
    val createAt: String,
    val customCategoryId: Int,
    val customCategoryList: List<GeneratedTypeLiteralInterface1>,
    val customCategoryName: String? = null,
    val customCategoryParentId: String? = null,
    val customCategoryParentName: String? = null,
    val customProperties: List<String> = emptyList(),
    val cycleBuyFlag: String? = null,
    val descImageList: List<String>,
    val description: String,
    val draftId: String? = null,
    val durationSaleDto: String? = null,
    val durationSaleFlag: Boolean,
    val editSource: String? = null,
    val educatePreferential: String? = null,
    val effectTimeMap: String? = null,
    val eleId: String,
    val eleProductId: String? = null,
    val eleSkuId: String? = null,
    val endDeliveryDate: String? = null,
    val existedQuantity: String? = null,
    val fee: String? = null,
    val forbidEndTime: String? = null,
    val forbidSale: Boolean,
    val forbidSaleReason: String? = null,
    val forbidSaleReasonDesc: String? = null,
    val forbidSaleReasonSummary: String? = null,
    val goodsDeliveryType: String? = null,
    val hasCombineSubItem: Boolean,
    val hasMedicareCateLogId: Boolean,
    val hasSku: Boolean,
    val headChainId: String? = null,
    val headImageList: String? = null,
    val images: List<GeneratedTypeLiteralInterface2>,
    val includeStoreCateInfo: Boolean? = null,
    val industryCustomStruct: String? = null,
    val invokeId: String? = null,
    val isJoinActivity: Boolean,
    val isProcessing: Boolean? = null,
    val isWeight: Boolean,
    val itemCanSell: Boolean,
    val itemCateRelationship: Boolean? = null,
    val itemId: Long,
    val itemPropValues: List<itemPropValuesData> = emptyList(),
    val itemSkuList: List<String>? = null,
    val itemStatus: String? = null,
    val itemWeight: Int,
    val lackRequiredFields: String? = null,
    val lackRequiredPropValues: String? = null,
    val latestDeliveryDate: String? = null,
    val merchantableItemSum: String? = null,
    val minActivePrice: Double? = null,
    val monthlySaledQuantity: Int,
    val number: String? = null,
    val originalSaleUnit: String? = null,
    val outId: Long,
    val packageFeeDto: String? = null,
    val packageFlag: Boolean? = null,
    val picUrl: String? = null,
    val preHeatEndDate: String? = null,
    val preHeatFlag: String? = null,
    val preHeatStartDate: String? = null,
    val preMinusWeight: String? = null,
    val preSaleDto: GeneratedTypeLiteralInterface3? = null,
    val preSaleEndDate: String? = null,
    val preSaleFlag: Boolean,
    val preSaleStartDate: String? = null,
    val preSaleType: String? = null,
    val preparationTime: Int? = null,
    val price: Double,
    val processing: List<String> = emptyList(),
    val productCreateSource: String? = null,
    val productId: Long,
    val productPartUpdateDTO: String? = null,
    val productSkuList: String? = null,
    val purchaseQuantity: Double,
    val quantity: Double,
    val quantityPartZero: String? = null,
    val rank: String? = null,
    val repeatDate: String? = null,
    val saleInfoSyncType: String? = null,
    val saleUnit: String? = null,
    val scItemId: String? = null,
    val sceneType: String? = null,
    val secondControlBarcode: Boolean,
    val sellCount: String? = null,
    val sellerId: Long,
    val sellerIdList: String? = null,
    val sevenDaysWithOutReason: Boolean? = null,
    val sevenDaysWithOutReasonType: Int? = null,
    val shelvesNum: String? = null,
    val shopName: String,
    val singleFlag: String? = null,
    val singlePackageFee: String? = null,
    val skuMaxPrice: String? = null,
    val skuMaxQuantity: String? = null,
    val skuMinPrice: String? = null,
    val skuMinQuantity: String? = null,
    val soldOutSoon: String? = null,
    val spuBarCode: String? = null,
    val spuId: Int? = null,
    val spuPrice: String? = null,
    val spuStatus: String? = null,
    val spuSubTitle: String? = null,
    val spuTitle: String? = null,
    val startDeliveryDate: String? = null,
    val status: Int,
    val stepQuantity: String? = null,
    val stockUnit: String? = null,
    val storeCateId: Int,
    val storeCreateSync: String? = null,
    val storeId: Long,
    val storeIdList: String? = null,
    val storeRule: String? = null,
    val subTitle: String? = null,
    val supplyRelationshipDTOList: String? = null,
    val syncAll: String? = null,
    val syncCustomCateToItem: String? = null,
    val syncItemOption: String? = null,
    val title: String,
    val top: String? = null,
    val totalSoldQuantity: Int,
    val videoDTOList: String? = null,
    val weightType: String? = null,
    val wid: String
)

@kotlinx.serialization.Serializable
data class itemPropValuesData(
    val elePropText: String? = null,
    val eleValueText: String? = null,
    val images: List<GeneratedTypeLiteralInterface2>? = emptyList(),
    val inputValue: Boolean? = null,
    val levelSource: String? = null,
    val propId: Long,
    val propText: String? = null,
    val showImage: String? = null,
    val valueId: Long,
    val valueText: String? = null,

)

@kotlinx.serialization.Serializable
data class MeiTuanInterface(
    val data: Data
)

@kotlinx.serialization.Serializable
data class Product3(
    val name: String,
    val id: Long,
    val sellCount: Int,
    val sellStatus: Int,
    val pictures: List<String>,
    val wmProductSkus: List<WmProductSku>
)

@kotlinx.serialization.Serializable
data class Data(
    val productList: List<Product3> = emptyList(),
    val respPage: RespPage? = null,
    val code: Int? = null,
    val message: String? = null
)

@kotlinx.serialization.Serializable
data class RespPage(
    val pageContent: List<Product3> = emptyList()
)

// GoodsInShopsInfo 数据类
data class GoodsInShopsInfo(
    val title: String,
    var info: List<GoodInfo>
)

// GoodInfo 数据类
data class GoodInfo(
    val image: String?=null,
    val shop: String,
    val monthsale: Int,
    val sellStatus: Any, // Kotlin 不支持联合类型，可以用 Any 类型代替，需要进一步处理
    val spuIds: Long?,
    var stock: MutableList<Double>,
    var price: List<Double>,
    var actprice_max: Double,
    var actprice_min: Double,
    val guige: List<GuiGeInfo>
)


// GuiGeInfo 数据类
data class GuiGeInfo(
    val guige: String,
    var orignprice: Double,
    var actprice: String,
    var stock: Double,
    var id: String,
    var itemActId: String,
    var activityLimit: String? = null,
    var activityDayLimit: String? = null,
    var dayStock: String? = null,
    val itemId: String? = null
) : Parcelable {
    constructor(parcel: Parcel) : this(
        parcel.readString() ?: "",
        parcel.readDouble(),
        parcel.readString() ?: "",
        parcel.readDouble(),
        parcel.readString() ?: "",
        parcel.readString() ?: "",
        parcel.readString(),
        parcel.readString(),
        parcel.readString(),
        parcel.readString()
    )

    override fun writeToParcel(parcel: Parcel, flags: Int) {
        parcel.writeString(guige)
        parcel.writeDouble(orignprice)
        parcel.writeString(actprice)
        parcel.writeDouble(stock)
        parcel.writeString(id)
        parcel.writeString(itemActId)
        parcel.writeString(activityLimit)
        parcel.writeString(activityDayLimit)
        parcel.writeString(dayStock)
        parcel.writeString(itemId)
    }

    override fun describeContents(): Int {
        return 0
    }

    companion object CREATOR : Parcelable.Creator<GuiGeInfo> {
        override fun createFromParcel(parcel: Parcel): GuiGeInfo {
            return GuiGeInfo(parcel)
        }

        override fun newArray(size: Int): Array<GuiGeInfo?> {
            return arrayOfNulls(size)
        }
    }
}



@kotlinx.serialization.Serializable
data class WmProductSku(
    val weight4g: Double,
    val upcCode: String,
    val price: Double,
    val stock: Double,
    val weight_unit: String,
    val sellStatus: Int,
    val sourceFoodCode: String,
    val spec: String,
    val weight: Double,
    val minOrderCount: Int,
    val boxPrice: Double,
    val boxNum: Double,
    val id: Long // 修改为 Long 类型，避免大数解析问题
)


@kotlinx.serialization.Serializable
data class StoreCookie (
    val store: String,
    val cookie_str: String,

)
@kotlinx.serialization.Serializable
data class  QueryPageShopSkuList_Dalouy(
    var data:String
    )

@kotlinx.serialization.Serializable
data class StoreAndCookies(
    val store: String,
    val cookie_str: String
)

suspend fun getLeanCloud(): List<StoreCookie> {
    val api = LeanCloudRequest()

    return try {
        val cookies = withContext(Dispatchers.IO) {
            api.getCookieByName("Account", "hjf")
        }
        if (cookies != null) {
            val storeCookies = Index.parseCookies(cookies)
            Log.d("getLeanCloud", "store_cookies: ${storeCookies}")
            storeCookies
        } else {
            Log.d("getLeanCloud", "Cookies: $cookies")
            emptyList()
        }
    } catch (error: Exception) {
        Log.e("getLeanCloud", "查询失败: ${error.message}")
        throw Exception("查询失败: ${error.message}")
    }
}


class MainActivity : AppCompatActivity() {
    private lateinit var meiTuanApi: MeiTuanApi
    private lateinit var elemeApi: ElemeApi
    lateinit var finalResM_E: MutableList<GoodsInShopsInfo> // 声明全局变量
    private lateinit var loadingOverlay: View
    private lateinit var storeCookies: MutableList<StoreAndCookies> // Declare storeCookies here

    public override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_main)
//        finalResM_E = mutableListOf() // 初始化全局变量
//        loadingOverlay = findViewById(R.id.loading_overlay)
//        // 初始化 storeCookies
//        storeCookies = mutableListOf<StoreAndCookies>() // 确保 storeCookies 被初始化
//
//        GlobalScope.launch {
//
//            runOnUiThread {
//                if (storeCookies.isEmpty()) {
//                    Toast.makeText(this@MainActivity, "请先添加店铺", Toast.LENGTH_SHORT).show()
//                }
//                else {
//                    Toast.makeText(this@MainActivity, "店铺信息已获取", Toast.LENGTH_SHORT).show()
//                    // 在这里执行需要在获取到 storeCookies 之后的代码
//                    // 初始化 RecyclerView
//                    val recyclerView: RecyclerView = findViewById(R.id.recycler_view)
//
//                    // 示例数据
//                    val productList = mutableListOf<Product>()
//
//                    // 初始化适配器
//                    val adapter = ProductAdapter(this@MainActivity, productList, storeCookies, this@MainActivity) { product ->
//                        // 下架按钮点击事件
//                        Toast.makeText(this@MainActivity, "下架: ${product.productName}", Toast.LENGTH_SHORT).show()
//                    }
//
//                    recyclerView.adapter = adapter
//                    recyclerView.layoutManager = LinearLayoutManager(this@MainActivity)
//
//                    // 设置搜索按钮点击事件
//                    val searchButton: Button = findViewById(R.id.btn_search)
//                    val searchInput: EditText = findViewById(R.id.et_search)
//                    searchButton.setOnClickListener {
//                        val searchWord = searchInput.text.toString()
//                        if (searchWord.isNotEmpty()) {
//                            loadingOverlay.visibility = View.VISIBLE
//                            //按钮变为按下状态，不可再次点击
//                            searchButton.isEnabled = false
//
//                            //清空finalResM_E
//                            finalResM_E.clear()
//                            // 清空 RecyclerView
//                            adapter.updateData(finalResM_E)
//                            //循环遍历店铺cookie,发起请求
//                            lifecycleScope.launch {
//                                try {
//                                    // 循环遍历店铺cookie,发起请求
//                                    val finalResM = mutableListOf<GoodsInShopsInfo>() // 定义全局列表
//                                    val finalResE = mutableListOf<GoodsInShopsInfo>() // 定义全局列表
//
//                                    try {
//                                        for (storeCookie in storeCookies) {
//                                            val store = storeCookie.store ?: ""
//                                            val cookieStr = storeCookie.cookie_str ?: ""
//                                            //如果店铺名称有'美团'，就执行下面的
//                                            if (store.contains("美团")) {
//                                                meiTuanApi = MeiTuanApi(cookieStr)
//                                                val response = meiTuanApi.postData(searchWord)
//                                                // 解析 response成现需要的数据
//                                                val goodsInShopsInfo = dealData(response, store)
//                                                val resM =
//                                                    mutableListOf<GoodsInShopsInfo>() // 假设 GoodsInfo 是数据模型的类名
//
//                                                goodsInShopsInfo.forEach { itemB ->
//                                                    // 查找已有的 itemA（resM 中的项）是否存在与 itemB 的 title 相同的项
//                                                    val existingItem =
//                                                        resM.find { itemA -> itemA.title == itemB.title }
//
//                                                    if (existingItem != null) {
//                                                        // 如果找到匹配项，将 itemB 的 info 合并到 existingItem 的 info 中
//                                                        existingItem.info =
//                                                            existingItem.info + itemB.info
//                                                    } else {
//                                                        // 如果不存在匹配项，直接将 itemB 添加到 resM 中
//                                                        resM.add(itemB)
//                                                    }
//                                                }
//                                                //将循环得到的resM合并成一个列表
//                                                // 合并 resM 到 finalResM
//                                                resM.forEach { itemB ->
//                                                    val existingItem =
//                                                        finalResM.find { itemA -> itemA.title == itemB.title }
//
//                                                    if (existingItem != null) {
//                                                        existingItem.info =
//                                                            existingItem.info + itemB.info
//                                                    } else {
//                                                        finalResM.add(itemB)
//                                                    }
//                                                }
//                                                Log.d("MainActivity222", "Current resM: $resM")
//
//                                            } else {
//                                                //饿了么
//                                                elemeApi = ElemeApi(cookieStr, store)
//                                                val result = elemeApi.pageQuery(searchWord)
//                                                //将查询结果result转换为ElePageQueryGetData对象
//                                                val data =
//                                                    Json.decodeFromString<ElePageQueryGetData>(
//                                                        result
//                                                    )
//
//
//                                                var goodsInShopsInfo = dealDataEleme(data, store)
//                                                val resE =
//                                                    mutableListOf<GoodsInShopsInfo>() // 假设 GoodsInfo 是数据模型的类名
//
//                                                goodsInShopsInfo.forEach { itemB ->
//                                                    // 查找已有的 itemA（resE 中的项）是否存在与 itemB 的 title 相同的项
//                                                    val existingItem =
//                                                        resE.find { itemA -> itemA.title == itemB.title }
//
//                                                    if (existingItem != null) {
//                                                        // 如果找到匹配项，将 itemB 的 info 合并到 existingItem 的 info 中
//                                                        existingItem.info =
//                                                            existingItem.info + itemB.info
//                                                    } else {
//                                                        // 如果不存在匹配项，直接将 itemB 添加到 resE 中
//                                                        resE.add(itemB)
//                                                    }
//                                                }
//
//                                                // 合并 resE 到 finalResE
//                                                resE.forEach { itemB ->
//                                                    val existingItem =
//                                                        finalResE.find { itemA -> itemA.title == itemB.title }
//
//                                                    if (existingItem != null) {
//                                                        existingItem.info =
//                                                            existingItem.info + itemB.info
//                                                    } else {
//                                                        finalResE.add(itemB)
//                                                    }
//                                                }
//                                                Log.d("MainActivity222", "Current resE: $resE")
//
//                                            }
//
//
//                                        }
//                                        //将美团和饿了么的结果合并finalResM和finalResE合并成finalResM_E
//                                        finalResM.forEach { itemM ->
//                                            val existingItem =
//                                                finalResM_E.find { itemE -> itemE.title == itemM.title }
//                                            if (existingItem != null) {
//                                                existingItem.info = existingItem.info + itemM.info
//                                            } else {
//                                                finalResM_E.add(itemM)
//                                            }
//                                        }
//
//                                        finalResE.forEach { itemE ->
//                                            val existingItem =
//                                                finalResM_E.find { itemM -> itemM.title == itemE.title }
//                                            if (existingItem != null) {
//                                                existingItem.info = existingItem.info + itemE.info
//                                            } else {
//                                                finalResM_E.add(itemE)
//                                            }
//                                        }
//
//
//
//
//                                        Log.d("MainActivity222", "Final finalResM_E: $finalResM_E")
//                                        // 更新 RecyclerView
//                                        runOnUiThread {
//                                            adapter.updateData(finalResM_E)
//                                        }
//                                    } catch (e: Exception) {
//                                        Log.d("MainActivity222", "error: $e")
//                                    }
//                                } catch (e: Exception) {
//                                    Log.d("MainActivity", "error: $e")
//                                } finally {
//                                    // 隐藏遮罩层
//                                    runOnUiThread {
//                                        loadingOverlay.visibility = View.GONE
//                                        searchButton.isEnabled = true
//                                    }
//                                }
//                            }
//
//
//                        } else {
//                            Toast.makeText(this@MainActivity, "请输入搜索内容", Toast.LENGTH_SHORT)
//                                .show()
//                        }
//                    }
//                }
//            }
        }
    }

//    override fun onResume() {
//        super.onResume()
//        storeCookies = mutableListOf<StoreAndCookies>() // 确保 storeCookies 被初始化
//        // 每次返回到 MainActivity 时重新获取 Cookies
//        getStoreCookiesFromLocal()
//    }
//
//    public fun getStoreCookiesFromLocal() {
//        val sharedPreferences = getSharedPreferences("StoreCookies", MODE_PRIVATE)
//        val existingData = sharedPreferences.getString("storeCookiesList", "[]")
//        val jsonArray = JSONArray(existingData)
//        storeCookies.clear() // 清空之前的数据
//        for (i in 0 until jsonArray.length()) {
//            val jsonObject = jsonArray.getJSONObject(i)
//            val storeName = jsonObject.getString("storeName")
//            val cookies = jsonObject.getString("cookies")
//            storeCookies.add(StoreAndCookies(storeName, cookies))
//        }
//
//        // 更新适配器的数据
//        // 这里需要确保适配器的引用是可用的
//        // 例如，您可以在适配器中添加一个方法来更新数据
//        // adapter.updateStoreCookies(storeCookies)
//    }
//
//    fun dealDataEleme(data: ElePageQueryGetData, store: String): List<GoodsInShopsInfo> {
//        val st: ElePageQueryGetData = data
//
//        val res: MutableList<GoodsInShopsInfo> = mutableListOf()
//
//        // 遍历 Ele_PageQuery_Data 数组
//        st.data.forEach { item ->
//            val stork_t: MutableList<Double> = mutableListOf(item.quantity)
//            val price_t: MutableList<Double> = mutableListOf(item.price)
//            val info_t: MutableList<GoodInfo> = mutableListOf()
//            val moreinfo_t: MutableList<GuiGeInfo> = mutableListOf(
//                GuiGeInfo(
//                    guige = "默认规格",
//                    orignprice = item.price,
//                    actprice = "***",
//                    stock = item.quantity,
//                    id = item.barCode,
//                    itemActId = st.activityId,
//                    itemId = item.itemId.toString()
//                )
//            )
//
//            info_t.add(
//                GoodInfo(
//                    image = item.picUrl,
//                    shop = store,
//                    monthsale = item.monthlySaledQuantity,
//                    stock = stork_t,
//                    sellStatus = item.itemCanSell,
//                    price = price_t,
//                    actprice_max = 0.0,
//                    actprice_min = 9999.0,
//                    spuIds = item.spuId?.toLong() ?: 0,
//                    guige = moreinfo_t
//                )
//            )
//
//            val at = GoodsInShopsInfo(
//                title = item.title,
//                info = info_t
//            )
//
//            res.add(at)
//        }
//
//        return res
//    }
//
//    //解析 response成现需要的数据
//    fun dealData(stringData: String, store: String): List<GoodsInShopsInfo> {
//        val json = Json { ignoreUnknownKeys = true } // 忽略未知键
//        val obj =
//            json.decodeFromString<MeiTuanInterface>(stringData) // 使用 kotlinx.serialization 解析 JSON
//        val res = mutableListOf<GoodsInShopsInfo>()
//
//        obj.data.productList.forEach { product ->
//            val stockList = mutableListOf<Double>()
//            val priceList = mutableListOf<Double>()
//            val infoList = mutableListOf<GoodInfo>()
//            val moreInfoList = mutableListOf<GuiGeInfo>()
//
//            product.wmProductSkus.forEach { sku ->
//                stockList.add(sku.stock)
//                priceList.add(sku.price)
//
//                moreInfoList.add(
//                    GuiGeInfo(
//                        guige = sku.spec,
//                        orignprice = sku.price,
//                        actprice = "***",
//                        stock = sku.stock,
//                        id = sku.id.toString(), // 转换为字符串
//                        itemActId = "1"
//                    )
//                )
//            }
//
//            infoList.add(
//                GoodInfo(
//                    image = product.pictures.firstOrNull() ?: "", // 防止空指针异常
//                    shop = store,
//                    monthsale = product.sellCount,
//                    stock = stockList,
//                    spuIds = product.id,
//                    sellStatus = product.sellStatus,
//                    price = priceList,
//                    actprice_max = 0.0,
//                    actprice_min = 9999.0,
//                    guige = moreInfoList
//                )
//            )
//
//            val goodsInShopInfo = GoodsInShopsInfo(
//                title = product.name,
//                info = infoList
//            )
//
//            res.add(goodsInShopInfo)
//        }
//
//        return res
//    }
//}
