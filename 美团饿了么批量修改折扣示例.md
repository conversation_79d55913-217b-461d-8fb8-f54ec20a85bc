# 美团饿了么平台折扣修改示例

本文档提供了在美团和饿了么平台如何修改商品折扣价格和排序的简洁完整示例。

## 目录

- [单个商品修改](#单个商品修改)
  - [修改单个商品折扣价](#修改单个商品折扣价)
  - [修改单个商品折扣排序](#修改单个商品折扣排序)
- [批量修改商品](#批量修改商品)
  - [批量修改折扣价](#批量修改折扣价)
  - [批量修改折扣排序](#批量修改折扣排序)
- [API调用示例](#api调用示例)
  - [美团平台示例](#美团平台示例)
  - [饿了么平台示例](#饿了么平台示例)

## 单个商品修改

### 修改单个商品折扣价

#### 美团平台

```python
# 导入必要的模块
from src.api.meituan_api import MeituanAPI
from src.db.models import Discount
from src.db.session import get_session_context

# 实例化美团API客户端
api = MeituanAPI(cookies=store_cookies)

# 修改折扣价格
async def update_meituan_discount_price(item_act_id, new_price, store_id):
    # 调用API修改折扣价格
    result = await api.update_discount(
        item_act_id=item_act_id,  # 商品活动ID
        act_price=new_price,      # 新的折扣价格
        store_id=store_id,        # 店铺ID
        order_limit_count=-1      # 不限购
    )
    
    # 更新本地数据库
    if result and result.get('data', {}).get('code') == 0:
        with get_session_context() as session:
            discount = session.query(Discount).filter_by(
                store_id=store_id,
                itemact_id=item_act_id
            ).first()
            
            if discount:
                discount.discount_price = new_price
                session.commit()
                return True
    
    return False
```

#### 饿了么平台

```python
# 导入必要的模块
from src.api.eleme.discount import ElemeDiscount
from src.db.models import Discount
from src.db.session import get_session_context

# 实例化饿了么折扣API客户端
discount_api = ElemeDiscount(client=eleme_client)

# 修改折扣价格
async def update_eleme_discount_price(sku_id, new_price, store_id):
    # 调用API修改折扣价格
    result = await discount_api.update_discount(
        sku_id=sku_id,           # 商品SKU ID
        special_price=new_price,  # 新的折扣价格
        activity_limit=-1         # 不限购
    )
    
    # 更新本地数据库
    if result:
        with get_session_context() as session:
            discount = session.query(Discount).filter_by(
                store_id=store_id,
                sku_id=sku_id
            ).first()
            
            if discount:
                discount.discount_price = new_price
                session.commit()
                return True
    
    return False
```

### 修改单个商品折扣排序

#### 美团平台

```python
# 导入必要的模块
from src.api.meituan_api import MeituanAPI
from src.db.models import Discount
from src.db.session import get_session_context

# 实例化美团API客户端
api = MeituanAPI(cookies=store_cookies)

# 修改折扣排序
async def update_meituan_discount_sort(discount_id, product_id, sku_id, new_sort, store_id):
    # 调用API修改折扣排序
    result = await api.update_discount_sort(
        id=discount_id,        # 折扣ID
        spu_id=product_id,     # 商品SPU ID
        wm_sku_id=sku_id,      # 商品SKU ID
        sort_number=new_sort   # 新的排序值
    )
    
    # 更新本地数据库
    if result and result.get('code') == 0:
        with get_session_context() as session:
            discount = session.query(Discount).filter_by(
                store_id=store_id,
                itemact_id=discount_id
            ).first()
            
            if discount:
                discount.sort_index = new_sort
                session.commit()
                return True
    
    return False
```

#### 饿了么平台

> 注意：饿了么平台不支持折扣排序功能。

## 批量修改商品

### 批量修改折扣价

#### 界面调用方式

在应用界面中，可以通过批量操作功能修改多个商品的折扣价：

1. 选择要修改的商品
2. 点击"批量操作"按钮
3. 选择"修改折扣价"选项
4. 输入新的折扣价格
5. 点击"确定"按钮

#### 实现代码

```python
# 批量修改折扣价
async def batch_update_discount_price(products, new_price):
    success_count = 0
    error_count = 0
    error_details = []
    
    for product in products:
        store_id = product['store_id']
        platform = product['platform']
        sku_id = product['sku_id']
        product_name = product['name']
        
        try:
            # 获取API实例
            api = get_store_api(store_id, platform)
            
            if not api:
                error_msg = f"未找到门店API实例: {store_id}"
                error_count += 1
                error_details.append(f"{product_name}: {error_msg}")
                continue
            
            # 根据平台调用不同的API
            if platform == '美团':
                # 获取商品折扣ID
                with get_session_context() as session:
                    discount = session.query(Discount).filter_by(
                        store_id=store_id,
                        sku_id=sku_id
                    ).first()
                    
                    if not discount:
                        # 创建新折扣
                        result = await api.create_discount(
                            discount_price=new_price,
                            origin_price=product['price'],
                            itemName=product_name,
                            spuId=product['product_id'],
                            wmSkuId=sku_id
                        )
                    else:
                        # 更新已有折扣
                        result = await api.update_discount(
                            item_act_id=discount.itemact_id,
                            act_price=new_price,
                            store_id=store_id
                        )
                
                if not result or (isinstance(result, dict) and result.get('code') != 0):
                    error_msg = result.get('msg', '未知错误') if isinstance(result, dict) else '接口调用失败'
                    error_details.append(f"{product_name}: {error_msg}")
                    error_count += 1
                    continue
                
                success_count += 1
                
            elif platform == '饿了么':
                # 获取饿了么折扣API
                discount_api = api.discount
                
                # 更新折扣价格
                result = await discount_api.update_discount(
                    sku_id=sku_id,
                    special_price=new_price
                )
                
                if not result:
                    error_details.append(f"{product_name}: 接口调用失败")
                    error_count += 1
                    continue
                
                success_count += 1
        
        except Exception as e:
            error_details.append(f"{product_name}: {str(e)}")
            error_count += 1
    
    return {
        'success_count': success_count,
        'error_count': error_count,
        'error_details': error_details
    }
```

### 批量修改折扣排序

#### 界面调用方式

在应用界面中，可以通过批量操作功能修改多个商品的折扣排序：

1. 选择要修改的商品
2. 点击"批量操作"按钮
3. 选择"修改折扣排序"选项
4. 输入新的排序值
5. 点击"确定"按钮

#### 实现代码

```python
# 批量修改折扣排序
async def update_all_stores_sort_index(new_index, products):
    success_count = 0
    error_count = 0
    error_details = []
    
    for product in products:
        store_id = product['store_id']
        platform = product['platform']
        sku_id = product['sku_id']
        product_id = product['product_id']
        product_name = product['name']
        
        try:
            # 获取API实例
            api = get_store_api(store_id, platform)
            
            if not api:
                error_msg = f"未找到门店API实例: {store_id}"
                error_count += 1
                error_details.append(f"{product_name}: {error_msg}")
                continue
            
            # 根据平台调用不同的API
            if platform == '美团':
                # 获取商品折扣ID
                with get_session_context() as session:
                    discount = session.query(Discount).filter_by(
                        store_id=store_id,
                        product_id=product_id
                    ).first()
                    
                    if not discount:
                        error_details.append(f"{product_name}: 未找到折扣记录")
                        error_count += 1
                        continue
                    
                    itemact_id = discount.itemact_id
                
                # 更新排序
                result = await api.update_discount_sort(
                    id=itemact_id,
                    spu_id=product_id,
                    wm_sku_id=sku_id,
                    sort_number=new_index
                )
                
                if not result or result.get('code') != 0:
                    error_msg = result.get('msg', '未知错误') if result else '接口调用失败'
                    error_details.append(f"{product_name}: {error_msg}")
                    error_count += 1
                    continue
                
                success_count += 1
                
            else:  # 饿了么
                # 饿了么不支持排序，直接计入成功
                success_count += 1
        
        except Exception as e:
            error_details.append(f"{product_name}: {str(e)}")
            error_count += 1
    
    return {
        'success_count': success_count,
        'error_count': error_count,
        'error_details': error_details
    }
```

## API调用示例

### 美团平台示例

#### 获取折扣商品列表

```python
# 获取折扣商品列表
async def get_meituan_discount_list():
    api = MeituanAPI(cookies=store_cookies)
    
    # 获取第一页折扣商品
    result = await api.get_discount_list(page_no=1, page_size=20)
    
    if result and result.get('code') == 0:
        discount_list = result.get('data', {}).get('respPage', {}).get('pageContent', [])
        for item in discount_list:
            print(f"商品: {item['itemName']}, 原价: {item['originalPrice']}, 折扣价: {item['actPrice']}, 排序: {item['sortIndex']}")
    
    # 搜索折扣商品
    search_result = await api.get_discount_list(
        page_no=1, 
        page_size=10,
        search_params={"fuzzySkuName": "牛肉面"}
    )
    
    # 处理搜索结果
    if search_result and search_result.get('code') == 0:
        search_items = search_result.get('data', {}).get('respPage', {}).get('pageContent', [])
        for item in search_items:
            print(f"搜索结果: {item['itemName']}, 折扣价: {item['actPrice']}")
```

#### 创建商品折扣

```python
# 创建商品折扣
async def create_meituan_discount():
    api = MeituanAPI(cookies=store_cookies)
    
    # 商品信息
    product_info = {
        'name': '香辣牛肉面',
        'sku_id': '18755075576',
        'spu_id': '12674209382',
        'price': 32.0,
        'discount_price': 28.8
    }
    
    # 创建折扣
    result = await api.create_discount(
        discount_price=product_info['discount_price'],
        origin_price=product_info['price'],
        itemName=product_info['name'],
        spuId=product_info['spu_id'],
        wmSkuId=product_info['sku_id']
    )
    
    if isinstance(result, dict) and result.get('code') == 0:
        print(f"创建折扣成功: {product_info['name']}, 折扣价: {product_info['discount_price']}")
        # 获取折扣ID
        discount_id = result.get('data', {}).get('id')
        return discount_id
    else:
        print(f"创建折扣失败: {result}")
        return None
```

#### 修改折扣排序

```python
# 修改折扣排序
async def update_meituan_discount_sort():
    api = MeituanAPI(cookies=store_cookies)
    
    # 折扣信息
    discount_info = {
        'id': '44117752095',
        'product_id': '12674209382',
        'sku_id': '18755075576',
        'new_sort': 10
    }
    
    # 修改排序
    result = await api.update_discount_sort(
        id=discount_info['id'],
        spu_id=discount_info['product_id'],
        wm_sku_id=discount_info['sku_id'],
        sort_number=discount_info['new_sort']
    )
    
    if result and result.get('code') == 0:
        print(f"修改排序成功: ID={discount_info['id']}, 新排序={discount_info['new_sort']}")
    else:
        print(f"修改排序失败: {result}")
```

### 饿了么平台示例

#### 修改折扣价格

```python
# 修改折扣价格
async def update_eleme_discount():
    # 获取饿了么客户端
    eleme_client = get_eleme_client(store_cookies)
    
    # 实例化折扣API
    discount_api = ElemeDiscount(client=eleme_client)
    
    # 商品信息
    product_info = {
        'sku_id': '15273639201',
        'new_price': 25.8,
        'limit': 2  # 每单限购2份
    }
    
    # 修改折扣
    result = await discount_api.update_discount(
        sku_id=product_info['sku_id'],
        special_price=product_info['new_price'],
        activity_limit=product_info['limit']
    )
    
    if result:
        print(f"修改折扣成功: ID={product_info['sku_id']}, 新价格={product_info['new_price']}")
    else:
        print(f"修改折扣失败")
```

#### 批量修改折扣

```python
# 批量修改折扣
async def batch_update_eleme_discounts():
    # 获取饿了么客户端
    eleme_client = get_eleme_client(store_cookies)
    
    # 实例化折扣API
    discount_api = ElemeDiscount(client=eleme_client)
    
    # 获取活动ID
    await discount_api.ensure_activity_id()
    activity_id = discount_api.activity_id
    
    # 商品列表
    products = [
        {'sku_id': '15273639201', 'special_price': 25.8},
        {'sku_id': '15273639202', 'special_price': 18.5},
        {'sku_id': '15273639203', 'special_price': 32.0}
    ]
    
    # 批量更新
    results = await discount_api.batch_update_discounts(
        activity_id=activity_id,
        updates=products
    )
    
    # 处理结果
    success_count = sum(1 for result in results if result['success'])
    print(f"批量修改结果: 成功={success_count}, 总数={len(products)}")
    
    for result in results:
        if not result['success']:
            print(f"修改失败: ID={result['sku_id']}, 原因={result['message']}")
``` 