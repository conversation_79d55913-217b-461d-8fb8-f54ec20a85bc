import android.content.Context
import android.content.SharedPreferences
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken

/**
 * 搜索历史管理类
 */
class SearchHistoryManager(context: Context) {
    companion object {
        private const val PREF_NAME = "search_history_pref"
        private const val KEY_SEARCH_HISTORY = "search_history"
        private const val MAX_HISTORY_SIZE = 20 // 最大历史记录数
    }

    private val sharedPreferences: SharedPreferences = context.getSharedPreferences(PREF_NAME, Context.MODE_PRIVATE)
    private val gson = Gson()

    /**
     * 添加搜索记录
     */
    fun addSearchHistory(keyword: String) {
        if (keyword.isBlank()) return
        
        val historyList = getSearchHistory().toMutableList()
        
        // 如果已存在相同关键词，先删除旧记录
        historyList.removeAll { it == keyword }
        
        // 将新关键词添加到列表开头
        historyList.add(0, keyword)
        
        // 如果超过最大数量，删除最旧的记录
        if (historyList.size > MAX_HISTORY_SIZE) {
            historyList.removeAt(historyList.size - 1)
        }
        
        // 保存更新后的列表
        saveSearchHistory(historyList)
    }

    /**
     * 获取搜索历史列表
     */
    fun getSearchHistory(): List<String> {
        val json = sharedPreferences.getString(KEY_SEARCH_HISTORY, null) ?: return emptyList()
        val type = object : TypeToken<List<String>>() {}.type
        return try {
            gson.fromJson(json, type)
        } catch (e: Exception) {
            emptyList()
        }
    }

    /**
     * 删除指定的搜索记录
     */
    fun removeSearchHistory(keyword: String) {
        val historyList = getSearchHistory().toMutableList()
        historyList.remove(keyword)
        saveSearchHistory(historyList)
    }

    /**
     * 清空所有搜索历史
     */
    fun clearSearchHistory() {
        saveSearchHistory(emptyList())
    }

    /**
     * 保存搜索历史列表
     */
    private fun saveSearchHistory(historyList: List<String>) {
        val json = gson.toJson(historyList)
        sharedPreferences.edit().putString(KEY_SEARCH_HISTORY, json).apply()
    }
} 