// ProductDetailsActivity.kt
package com.meituaneleme.assistant

import android.os.Bundle
import android.widget.TextView
import androidx.appcompat.app.AppCompatActivity
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView

class ProductDetailsActivity : AppCompatActivity() {

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_product_details)

        val productName = intent.getStringExtra("productName") ?: "商品名称"
        val storeName = intent.getStringExtra("storeName") ?: "店铺名称"
        //收取传递过来的商品规格信息
        val cookis_str = intent.getStringExtra("cookies") ?: ""

        val productInfos = intent.getParcelableArrayListExtra<GuiGeInfo>("productInfoList") ?: arrayListOf()

        val tvProductName: TextView = findViewById(R.id.tv_product_name)
        tvProductName.text = productName

        val tvStoreName: TextView = findViewById(R.id.tv_store_name)
        tvStoreName.text = storeName

        val rvProductSpecs: RecyclerView = findViewById(R.id.rv_product_specs)
        rvProductSpecs.layoutManager = LinearLayoutManager(this)
        rvProductSpecs.adapter = ProductSpecAdapter(this, productInfos,cookis_str,storeName)
    }
}