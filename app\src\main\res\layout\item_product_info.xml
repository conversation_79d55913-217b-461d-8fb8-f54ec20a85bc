<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginVertical="3dp"
    app:cardCornerRadius="6dp"
    app:cardElevation="0dp"
    app:strokeColor="@color/divider"
    app:strokeWidth="1dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="8dp"
        android:gravity="center_vertical">

        <!-- 店铺名称和平台标识 -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1.2"
            android:orientation="vertical">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical">

                <!-- 平台标识 -->
                <TextView
                    android:id="@+id/tvPlatformTag"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="平台"
                    android:textSize="10sp"
                    android:textColor="@android:color/white"
                    android:background="@drawable/tag_meituan"
                    android:paddingHorizontal="4dp"
                    android:paddingVertical="1dp"
                    android:layout_marginEnd="4dp"/>

                <!-- 店铺名称 -->
                <TextView
                    android:id="@+id/tvShopName"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="店铺名称"
                    android:textSize="13sp"
                    android:textStyle="bold"
                    android:textColor="@color/text_primary"
                    android:maxLines="1"
                    android:ellipsize="end" />
            </LinearLayout>
                
<!--            <TextView-->
<!--                android:layout_width="wrap_content"-->
<!--                android:layout_height="wrap_content"-->
<!--                android:text="点击查看详情"-->
<!--                android:textSize="12sp"-->
<!--                android:textColor="@color/text_hint"-->
<!--                android:layout_marginTop="2dp"/>-->
        </LinearLayout>

        <!-- 价格信息和库存容器 -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1.8"
            android:orientation="horizontal">

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical">
                
                <!-- 原价 -->
                <TextView
                    android:id="@+id/tvPrice"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="原价"
                    android:textSize="12sp"
                    android:textColor="@color/colorPrimary"
                    android:textStyle="bold" />

                <!-- 折扣价 -->
                <TextView
                    android:id="@+id/tvDiscountPrice"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="1dp"
                    android:text="卖价"
                    android:textColor="@color/colorSecondary"
                    android:textSize="12sp" />
                
                <!-- 库存信息 -->
                <TextView
                    android:id="@+id/tvStock"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="1dp"
                    android:text="库存: 0"
                    android:textColor="@color/text_primary"
                    android:textSize="12sp"
                    android:paddingHorizontal="6dp"
                    android:paddingVertical="1dp"
                    android:minWidth="80dp"
                    android:background="@drawable/stock_indicator_background" />
            </LinearLayout>

            <!-- 下架按钮 -->
            <com.google.android.material.button.MaterialButton
                android:id="@+id/btnSoldOut"
                android:layout_width="60dp"
                android:layout_height="32dp"
                android:text="下架"
                android:textSize="11sp"
                app:cornerRadius="6dp"
                android:insetTop="0dp"
                android:insetBottom="0dp"
                style="@style/Widget.App.Button.Secondary" />
        </LinearLayout>

    </LinearLayout>
</com.google.android.material.card.MaterialCardView>