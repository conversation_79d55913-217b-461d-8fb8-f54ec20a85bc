# 美团饿了么折扣接口开发文档

本文档详细介绍了美团外卖和饿了么平台的折扣价和折扣排序相关接口实现，以及在项目中的批量修改界面调用方式。

## 目录

- [接口概述](#接口概述)
- [美团平台接口](#美团平台接口)
  - [获取折扣商品列表](#获取折扣商品列表-美团)
  - [创建折扣商品](#创建折扣商品-美团)
  - [修改折扣商品信息](#修改折扣商品信息-美团)
  - [修改折扣商品排序](#修改折扣商品排序-美团)
  - [下线折扣商品](#下线折扣商品-美团)
- [饿了么平台接口](#饿了么平台接口)
  - [修改折扣商品信息](#修改折扣商品信息-饿了么)
  - [批量修改折扣商品](#批量修改折扣商品-饿了么)
  - [管理折扣商品](#管理折扣商品-饿了么)
  - [删除折扣商品](#删除折扣商品-饿了么)
- [数据结构](#数据结构)
  - [数据库结构](#数据库结构)
- [界面实现](#界面实现)
  - [单个修改实现](#单个修改实现)
  - [批量修改实现](#批量修改实现)
- [使用示例](#使用示例)
  - [修改单个商品折扣价](#修改单个商品折扣价)
  - [修改单个商品折扣排序](#修改单个商品折扣排序)
  - [批量修改折扣价](#批量修改折扣价)
  - [批量修改折扣排序](#批量修改折扣排序)

## 接口概述

本项目中实现了两个外卖平台（美团和饿了么）的折扣商品管理功能，主要包括：
- 获取折扣商品列表
- 创建折扣商品
- 修改折扣商品信息（折扣价格、限购数量等）
- 修改折扣商品排序（仅美团支持）
- 删除折扣商品

## 美团平台接口

### 获取折扣商品列表-美团

获取店铺中的折扣商品列表，支持分页、搜索等功能。

#### 接口信息
- **接口地址**: `https://waimaieapp.meituan.com/sg/business/item/activity/query/pageList`
- **请求方式**: POST
- **Content-Type**: application/json

#### 请求参数

```json
{
    "actType": 17,
    "page": {
        "pageNo": 1,
        "pageSize": 10
    },
    "queryReq": {
        "source": [-1],
        "actStatus": 101,
        "fuzzySkuName": "商品名称(可选)",
        "skuIdList": ["商品ID(可选)"]
    },
    "alonePoi": true,
    "loginPoiId": "店铺ID"
}
```

#### 响应参数

```json
{
    "code": 0,
    "message": "成功",
    "data": {
        "queryMaxCount": 100,
        "limitPageNo": 10,
        "sortSuccess": true,
        "doModifyActivityCount": 0,
        "respPage": {
            "pageContent": [
                {
                    "itemActId": ***********,
                    "poiId": 17234837,
                    "poiName": "店铺名称",
                    "upc": "商品条码",
                    "skuCode": "SKU编码",
                    "skuId": ***********,
                    "spuId": ***********,
                    "itemName": "商品名称",
                    "actId": "活动ID",
                    "startTime": 1600000000000,
                    "endTime": 1700000000000,
                    "actStatus": 1,
                    "source": 1,
                    "creator": "创建者",
                    "sortIndex": 100,
                    "originalPrice": "100.00",
                    "actPrice": "80.00",
                    "discount": "8.0",
                    "settingType": 1,
                    "mtCharge": "0.00",
                    "poiCharge": "80.00",
                    "agentCharge": "0.00",
                    "dayLimit": 0,
                    "orderLimit": 0,
                    "weeks": "1,2,3,4,5,6,7"
                }
            ]
        }
    }
}
```

#### 代码实现

```python
async def get_discount_list(self, page_no: int = 1, page_size: int = 10, search_params: Optional[Dict] = None) -> Dict[str, Any]:
    """
    获取折扣商品列表

    Args:
        page_no: int, 页码，从1开始
        page_size: int, 每页数量，默认10
        search_params: Optional[Dict], 搜索参数，可包含:
            - fuzzySkuName: str, 商品名称搜索关键词
            - skuIdList: List[str], SKU ID列表，用于精确搜索

    Returns:
        Dict[str, Any]: API响应数据，包含折扣商品列表
    """
    url = "https://waimaieapp.meituan.com/sg/business/item/activity/query/pageList"

    # 构建请求数据
    data = {
        "actType": 17,  # 固定值
        "page": {
            "pageNo": page_no,
            "pageSize": page_size
        },
        "queryReq": {
            "source": [-1],  # 全部来源
            "actStatus": 2  # 全部状态
        },
        "alonePoi": True,  # 固定值
        "loginPoiId": self.cookies.get('wmPoiId')
    }

    # 添加搜索参数
    if search_params:
        if "fuzzySkuName" in search_params:
            data["queryReq"]["fuzzySkuName"] = search_params["fuzzySkuName"]
        if "skuIdList" in search_params:
            data["queryReq"]["skuIdList"] = search_params["skuIdList"]

    self.logger.info(f"获取折扣商品列表: page={page_no}, size={page_size}")
    self.logger.debug(f"请求数据: {data}")

    # 发送POST请求，使用JSON格式
    return await self.post(url, data=data, contenttype='json')
```

### 创建折扣商品-美团

创建一个新的折扣商品。

#### 接口信息
- **接口地址**: 美团API
- **请求方式**: POST

#### 代码实现

```python
async def create_discount(self, discount_price, origin_price, itemName, spuId, wmSkuId):
    try:
        # 构建请求数据和发送请求的具体实现
        # ...
        
        response = requests.post(url, headers=headers, cookies=self.cookies, data=encoded_data).json()
        return response
    except Exception as e:
        return f'更改折扣失败,原因：{e}'
```

### 修改折扣商品信息-美团

修改折扣商品的价格、限购等信息。

#### 接口信息
- **接口地址**: `https://waimaieapp.meituan.com/sg/business/item/activity/batch/modify/saleFood`
- **请求方式**: POST
- **Content-Type**: application/json

#### 请求参数

```json
{
    "actType": 17,
    "modifyItemList": [{
        "itemActId": "12345678",
        "poiId": "87654321"
    }],
    "updateActInfo": false,
    "actPrice": 88.8,
    "orderLimit": true,
    "orderLimitCount": "5",
    "dayInventory": false,
    "dayInventoryCount": "-1",
    "discountType": 0,
    "updateActInfo": false
}
```

#### 代码实现

```python
async def update_discount(self, 
                        item_act_id: int,
                        act_price: float,
                        store_id: str,
                        order_limit_count: int = -1) -> Dict[str, Any]:
    """修改折扣商品信息"""
    url = "https://waimaieapp.meituan.com/sg/business/item/activity/batch/modify/saleFood"
    
    # 从cookies获取wmPoiId
    poi_id = int(self.cookies.get('wmPoiId'))
    
    # 在数据库中的discounts表获取actId
    actId = '0'
    try:
        with get_session_context() as session:
            discount = session.query(Discount).filter_by(
                store_id=store_id,
                itemact_id=item_act_id
            ).first()
            
            if discount:
                actId = discount.actId
                session.refresh(discount)
            
            session.commit()
    except Exception as e:
        self.logger.error(f"获取折扣信息失败: {str(e)}")
        actId = '0'

    # 构建请求数据
    data = {
        "actType": 17,
        "modifyItemList": [{
            "itemActId": str(item_act_id),
            "poiId": str(poi_id)
        }],
        "updateActInfo": False,
        "actPrice": float(act_price),
        "orderLimit": order_limit_count != -1,
        "orderLimitCount": int(order_limit_count) if order_limit_count != -1 else "-1",
        "dayInventory": False,
        "dayInventoryCount": "-1",
        "discountType": 0,
        "updateActInfo": False
    }

    self.logger.info(f"修改折扣商品信息: itemActId={item_act_id}, actPrice={act_price}")
    self.logger.debug(f"请求数据: {data}")

    return await self.post(url, data=data, params=self.params, contenttype='json')
```

### 修改折扣商品排序-美团

修改店铺中折扣商品的显示排序。

#### 接口信息
- **接口地址**: `https://waimaieapp.meituan.com/sg/business/item/activity/updateSortIndex`
- **请求方式**: POST
- **Content-Type**: application/x-www-form-urlencoded

#### 请求参数

```
wmActType=17&skuSet=[{"id":***********,"spuId":***********,"wmSkuId":***********,"wmPoiId":"17234837","sortNumber":25}]&opType=7
```

#### Query Parameters
- yodaReady: "h5"
- csecplatform: 4
- csecversion: "3.0.0"

#### 响应参数

```json
{
    "code": 0,
    "message": "成功",
    "data": {
        "code": 0,
        "msg": null,
        "data": 132502
    }
}
```

#### 代码实现

```python
async def update_discount_sort(self, id: str, spu_id: str, wm_sku_id: str, sort_number: int) -> Dict:
    """
    修改商品折扣排序
    
    Args:
        id: 商品ID
        spu_id: 商品SPU ID
        wm_sku_id: 商品SKU ID
        sort_number: 新的排序号
        
    Returns:
        Dict: 包含响应数据的字典
        示例: {"code":0, "message":"成功", "data":{"code":0, "msg":null, "data":134102}}
    """
    url = "https://waimaieapp.meituan.com/sg/business/item/activity/updateSortIndex"
    
    params = {
        "yodaReady": "h5",
        "csecplatform": "4",
        "csecversion": "3.0.0"
    }
    
    # 构造form-urlencoded格式的数据
    sku_data = {
        "id": id,
        "spuId": spu_id,
        "wmSkuId": wm_sku_id,
        "wmPoiId": str(self.cookies.get('wmPoiId')),
        "sortNumber": sort_number
    }
    
    data = {
        "wmActType": "17",
        "skuSet": json.dumps([sku_data]),
        "opType": "7"
    }

    try:
        response = await self.post(url, data=data, params=params)
        
        if response.get('code') != 0:
            error_msg = response.get('message', '未知错误')
            raise Exception(f"API返回错误: {error_msg}")
        
        return response
        
    except Exception as e:
        raise Exception(f"修改失败: {str(e)}")
```

### 下线折扣商品-美团

下线（删除）折扣商品活动。

#### 接口信息
- **接口地址**: `/sg/business/item/activity/batch/offline`
- **请求方式**: POST

#### 代码实现

```python
async def delete_discount(self, item_act_list: List[Dict[str, Any]]) -> Dict[str, Any]:
    """
    下线折扣商品
    
    Args:
        item_act_list: 折扣商品列表
    """
    url = f"{self.base_url}/sg/business/item/activity/batch/offline"
    return await self.post(url, {'actType': 17, 'itemActList': item_act_list})
```

## 饿了么平台接口

### 修改折扣商品信息-饿了么

修改折扣商品的价格、限购等信息。

#### 代码实现

```python
async def update_discount(self,
                        sku_id: str,
                        special_price: float,
                        activity_limit: int = -1) -> bool:
    """
    修改折扣商品信息

    Args:
        sku_id: str, 商品SKU ID
        special_price: float, 特价价格
        activity_limit: int, 每单限购数量，默认9999（不限购）

    Returns:
        bool: 更新是否成功
    """
    try:
        if not self.activity_id:
            await self.ensure_activity_id()
            activity_id = self.activity_id
        else:
            activity_id = self.activity_id
        # 构建请求命令
        command = {
            "activityId": str(activity_id),
            "specialPrice": str(special_price),
            "stock": 9999,
            "dayStock": 9999,
            "activityLimit": 9999,
            "activityDayLimit": activity_limit,
            "skuShopInfos": [
                {
                    "skuId": str(sku_id),
                    "itemSkuId": None
                }
            ]
        }

        # 构建请求数据
        data = {
            "command": json.dumps(command)  # 只对command进行一次序列化
        }

        # 发送请求
        result = await self.client.make_request(
            'editSku',
            method='POST',
            data=data
        )

        self.logger.info(f"修改折扣商品信息: activityId={activity_id}, specialPrice={special_price}")
        self.logger.debug(f"请求数据: {data}")

        # 检查响应
        return result and result.get('ret', [])[0].startswith('SUCCESS')

    except Exception as e:
        self.logger.error(f"修改折扣商品信息失败: {str(e)}")
        return False
```

### 批量修改折扣商品-饿了么

批量修改多个折扣商品的信息。

#### 代码实现

```python
async def batch_update_discounts(self, 
                               activity_id: str,
                               updates: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
    """批量修改商品折扣信息
    
    Args:
        activity_id: 活动ID
        updates: 更新列表，每项包含：
            {
                'sku_id': 商品SKU ID,
                'special_price': 特价,
                'stock': 库存(可选),
                'day_stock': 每日库存(可选),
                'activity_limit': 活动限购(可选),
                'activity_day_limit': 每日限购(可选)
            }
            
    Returns:
        List[Dict]: 每个商品的更新结果
    """
    results = []
    
    for update in updates:
        try:
            result = await self.update_discount(
                activity_id=activity_id,
                sku_id=update['sku_id'],
                special_price=update['special_price'],
                stock=update.get('stock', 9999),
                day_stock=update.get('day_stock', 9999),
                activity_limit=update.get('activity_limit', 9999),
                activity_day_limit=update.get('activity_day_limit', -1)
            )
            results.append({
                'sku_id': update['sku_id'],
                'success': True,
                'message': '修改成功'
            })
        except Exception as e:
            results.append({
                'sku_id': update['sku_id'],
                'success': False,
                'message': str(e)
            })
    
    return results
```

### 管理折扣商品-饿了么

管理（添加/编辑/删除）折扣商品。

#### 代码实现

```python
async def manage_discount(self,
                      mode: str,  # 'add', 'edit', 'delete'
                      upc: str = None,
                      special_price: Union[float, str] = None,
                      activity_id: str = None,
                      activity_limit_type: int = 2,
                      activity_day_limit: int = 999) -> Dict[str, Any]:
    """管理商品折扣（添加/编辑/删除）"""
    
    # 根据模式设置相应字段
    if mode == 'add':
        command["appendUpcProductList"] = [{
            "upcCode": upc,
            "participateType": 2,
            "specialPrice": float(special_price)
        }]
        operation_desc = f"添加折扣，活动ID: {activity_id}, 商品UPC: {upc}, 特价: {special_price}"
    elif mode == 'edit':
        command["editUpcProductList"] = [{
            "upcCode": upc,
            "participateType": 2,
            "specialPrice": float(special_price),
            "activityLimitType": activity_limit_type,
            "activityDayLimit": activity_day_limit
        }]
        operation_desc = f"修改折扣，活动ID: {activity_id}, 商品UPC: {upc}, 特价: {special_price}"
    elif mode == 'delete':
        command["deleteUpcProductList"] = [{
            "upcCode": upc
        }]
        operation_desc = f"删除折扣，活动ID: {activity_id}, 商品UPC: {upc}"
    else:
        raise ValueError(f"不支持的操作模式: {mode}")
    
    # 构造请求数据
    data = {
        "command": json.dumps(command)
    }
    
    self.logger.info(operation_desc)
    self.logger.debug(f"请求数据: {data}")
    
    try:
        response = await self.client.make_request(
            endpoint='updateWithAllData',
            method='POST',
            data=data
        )
        
        if not response or 'data' not in response:
            raise Exception(f"{operation_desc}失败：响应数据格式错误")
        
        result = response.get('data', {})
        if not result.get('code', 0) == 0:
            error_msg = result.get('message') or "未知错误"
            raise Exception(f"{operation_desc}失败：{error_msg}")
        
        return result
        
    except Exception as e:
        self.logger.error(f"{operation_desc}失败: {str(e)}")
        raise
```

### 删除折扣商品-饿了么

删除折扣商品。

#### 代码实现

```python
async def delete_discount(self,
                       sku_id: str,
                       activity_id: str = None) -> Dict[str, Any]:
    """删除商品折扣"""
    if not activity_id:
        await self.ensure_activity_id()
        activity_id = self.activity_id
        
    endpoint = 'mtop.me.ele.merchant.special.pc.deleteActivitySku'
    
    command = {
        "activityId": activity_id,
        "skuShopInfos": [
            {
                "skuId": sku_id,
                "itemSkuId": None
            }
        ]
    }
    
    try:
        response = await self.client.make_request(
            endpoint=endpoint,
            method='POST',
            data={"command": command}
        )
        
        if not response or 'data' not in response:
            raise Exception("删除折扣失败：响应数据格式错误")
        
        result = response['data']
        if result.get('code') != 0:
            error_msg = result.get('message') or result.get('msg') or "未知错误"
            raise Exception(f"删除折扣失败：{error_msg}")
        
        return result
        
    except Exception as e:
        self.logger.error(f"删除折扣失败: {str(e)}")
        raise
```

## 数据结构

### 数据库结构

项目使用SQLite数据库存储折扣信息，主要表结构如下：

```
Discount表：
- store_id: 店铺ID
- itemact_id: 商品活动ID（美团）
- discount_price: 折扣价格
- sku_id: 商品SKU ID
- product_id: 商品SPU ID
- order_limit: 限购数量
- sort_index: 排序索引
- actId: 活动ID
```

## 界面实现

### 单个修改实现

单个商品的折扣价和排序修改通过`EditableLabel`组件实现，当用户修改值后会调用相应的API进行更新。

```python
async def on_value_changed(self):
    """值修改响应"""
    
    # ...代码省略...
    
    # 处理折扣排序列
    elif self.column == 6:  # 折扣排序列
        # 在discounts表中获取排序
        discount = session.query(Discount).filter_by(
            store_id=self.store_id,
            sku_id=self.product_id
        ).first()
        
        result = await self.api.update_discount_sort(
            id=discount.itemact_id,
            spu_id=discount.product_id,
            wm_sku_id=discount.sku_id,
            sort_number=int(new_value)
        )
        success = result.get('data', {}).get('code','')==0

    # 处理限购数量列
    elif self.column == 7:  # 限购数量列
        # 获取当前折扣价格
        discount = session.query(Discount).filter_by(
            store_id=self.store_id,
            itemact_id=self.product_id
        ).first()
        current_price = discount.discount_price if discount else 0
        
        result = await self.api.update_discount(
            item_act_id=self.product_id,
            act_price=current_price,
            order_limit_count=new_value
        )
        success = result and result.get('data', {}).get('code') == 0
```

### 批量修改实现

批量修改功能通过`BatchInterface`类实现，可以同时修改多个商品的折扣价和排序。

```python
async def update_all_stores_sort_index(self, new_index: int, products=None, row=None):
    """更新商品折扣排序"""
    
    # ...代码省略...
    
    # 根据平台调用不同的API
    if platform == '美团':
        # 在discounts表中获取排序
        with get_session_context() as session:
            discount = session.query(Discount).filter_by(
                store_id=store_id,
                product_id=product_id
            ).first()

            # 如果找不到折扣记录
            if not discount:
                error_details.append(f"{store_name}: 未找到折扣记录")
                error_count += 1
                continue
                
            # 在session还活着的时候获取需要的值
            itemact_id = discount.itemact_id
    
        result = await api.update_discount_sort(itemact_id, product_id, sku_id, new_index)
        if not result or result.get('code') != 0:
            error_msg = result.get('msg', '未知错误') if result else '接口调用失败'
            error_details.append(f"{store_name}: {error_msg}")
            error_count += 1
            continue
        success_count += 1
    else:  # 饿了么
        # 不涉及排序
        success_count += 1
```

## 使用示例

### 修改单个商品折扣价

1. 在商品列表中找到要修改的商品
2. 点击折扣价格单元格
3. 输入新的折扣价格
4. 按回车键提交

系统将调用相应平台的API修改折扣价格，并在数据库中更新记录。

### 修改单个商品折扣排序

1. 在商品列表中找到要修改的商品
2. 点击折扣排序单元格
3. 输入新的排序值（值越小排序越靠前）
4. 按回车键提交

系统将调用美团API修改折扣排序，并在数据库中更新记录。注意：饿了么平台不支持排序功能。

### 批量修改折扣价

1. 在商品列表中选择多个要修改的商品
2. 点击"批量操作"按钮
3. 选择"修改折扣价"
4. 输入新的折扣价格
5. 点击确认

系统将为所有选中商品调用相应平台的API修改折扣价格，并在数据库中更新记录。

### 批量修改折扣排序

1. 在商品列表中选择多个要修改的商品
2. 点击"批量操作"按钮
3. 选择"修改折扣排序"
4. 输入新的排序值
5. 点击确认

系统将为所有选中的美团平台商品调用API修改折扣排序，并在数据库中更新记录。饿了么平台商品不会进行排序修改。 