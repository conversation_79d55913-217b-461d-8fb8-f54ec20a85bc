package com.meituaneleme.assistant

import com.meituaneleme.assistant.ElemeApi
import com.meituaneleme.assistant.MeiTuanApi
import android.content.Context
import android.content.Intent
import android.content.res.ColorStateList
import android.graphics.Color
import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.MotionEvent
import android.view.View
import android.view.ViewGroup
import android.view.animation.LinearInterpolator
import android.widget.ArrayAdapter
import android.widget.Button
import android.widget.Spinner
import android.widget.Toast
import androidx.core.content.ContextCompat
import androidx.fragment.app.Fragment
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.DividerItemDecoration
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.google.android.material.floatingactionbutton.FloatingActionButton
import com.meituaneleme.assistant.api.ApiClient
import com.meituaneleme.assistant.api.StoreResponse
import com.meituaneleme.assistant.api.StoreValidCheck
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.async
import kotlinx.coroutines.awaitAll
import kotlinx.coroutines.joinAll
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import org.json.JSONArray
import org.json.JSONObject
import android.animation.ObjectAnimator
import androidx.appcompat.app.AppCompatActivity
import com.meituaneleme.assistant.api.StoreCountResponse
import com.meituaneleme.assistant.api.UserInfoResponse
import kotlin.math.abs
import retrofit2.Call
import retrofit2.Callback
import retrofit2.Response

class StoreFragment : Fragment(), StoreAdapter.OnStoreRefreshListener {
    private lateinit var addStoreButton: Button
    private lateinit var recyclerView: RecyclerView
    private lateinit var storeAdapter: StoreAdapter
    private lateinit var platformSpinner: Spinner
    private lateinit var storeList: MutableList<StoreResponse>
    private lateinit var storevalidcheckList: MutableList<StoreValidCheck>
    private lateinit var storevalidcheckList_r: MutableList<StoreValidCheck>
    private lateinit var refreshButton: Button
//    private var originalButtonBackground: ColorStateList? = null
    private lateinit var refreshFab: FloatingActionButton
    private var dX = 0f
    private var dY = 0f
    private var rotateAnimator: ObjectAnimator? = null

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        return inflater.inflate(R.layout.activity_store_management, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        addStoreButton = view.findViewById(R.id.add_store_button)
        recyclerView = view.findViewById(R.id.recycler_view)
        platformSpinner = view.findViewById(R.id.platform_spinner2)
        storeList = mutableListOf()

        // 初始化 RecyclerView 和适配器，传入刷新监听器
        storevalidcheckList = mutableListOf()
        storeAdapter = StoreAdapter(storevalidcheckList, this)
        recyclerView.layoutManager = LinearLayoutManager(requireContext())
        recyclerView.adapter = storeAdapter

        // 添加分割线
        val dividerItemDecoration = DividerItemDecoration(recyclerView.context, LinearLayoutManager.VERTICAL)
        dividerItemDecoration.setDrawable(ContextCompat.getDrawable(requireContext(), R.drawable.divider)!!)
        recyclerView.addItemDecoration(dividerItemDecoration)

        val platforms = arrayOf("美团", "饿了么")
        val adapter = ArrayAdapter(requireContext(), android.R.layout.simple_spinner_item, platforms)
        adapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item)
        platformSpinner.adapter = adapter

        addStoreButton.setOnClickListener {
            checkStoreLimit { canAdd ->
                if (canAdd) {
                    val selectedItem = platformSpinner.selectedItem
                    if (selectedItem == null) {
                        Toast.makeText(requireContext(), "请选择一个平台", Toast.LENGTH_SHORT).show()
                        return@checkStoreLimit
                    }
                    val platform = selectedItem.toString()
                    // 直接打开 WebViewActivity
                    val intent = Intent(requireContext(), WebViewActivity::class.java)
                    intent.putParcelableArrayListExtra("storeList", ArrayList(storeList))
                    intent.putExtra("platform", platform)
                    startActivity(intent)
                } else {
                    Toast.makeText(requireContext(), "最大可绑定门店数量2,如需解除限制请联系管理员", Toast.LENGTH_SHORT).show()
                }
            }
        }

        // 获取门店明细和 Cookies
        getStoreCookiesFromLocal()

        // 初始化悬浮刷新按钮
        refreshFab = view.findViewById(R.id.refresh_fab)
        
        // 简化为普通的点击监听
        refreshFab.setOnClickListener {
            // 禁用按钮
            refreshFab.isEnabled = false
            startRefreshAnimation()
            checkCookiesValid()
        }
    }

    private fun getCookiesByStoreName(storeName: String,platform:String): String {
        val sharedPreferences = requireContext().getSharedPreferences("StoreCookies", Context.MODE_PRIVATE)
        val existingData = sharedPreferences.getString("storeCookiesList", "[]")
        val jsonArray = JSONArray(existingData)
        for (i in 0 until jsonArray.length()) {
            val store = jsonArray.getJSONObject(i)
            if (store.getString("storeName") == storeName && store.getString("platform") == platform) {
                return store.getString("cookies")
            }
        }
        return ""
    }

    private fun checkCookiesValid() {
        lifecycleScope.launch(Dispatchers.Main) {
            try {
                startRefreshAnimation()
                
                val sharedPreferences = requireContext().getSharedPreferences("StoreCookies", Context.MODE_PRIVATE)
                val existingData = sharedPreferences.getString("storeCookiesList", "[]")
                val jsonArray = JSONArray(existingData)
                
                Log.d("StoreFragment", "开始检查，总共 ${jsonArray.length()} 个门店")
                
                // 创建当前状态列表
                val currentStoreList = mutableListOf<StoreValidCheck>()
                for (i in 0 until jsonArray.length()) {
                    val store = jsonArray.getJSONObject(i)
                    val storeName = store.getString("storeName")
                    val platform = store.getString("platform")
                    val isValid = store.getBoolean("isValid")
                    Log.d("StoreFragment", "初始状态 - 门店: $storeName, 平台: $platform, 状态: $isValid")
                    currentStoreList.add(StoreValidCheck(platform, storeName))
                }
                
                // 初始显示现有状态
                updateStoreList(currentStoreList)
                
                // 使用 async 并发执行所有检查
                val jobs = (0 until jsonArray.length()).map { i ->
                    lifecycleScope.launch(Dispatchers.IO) {
                        val store = jsonArray.getJSONObject(i)
                        val storeName = store.getString("storeName")
                        val platform = store.getString("platform")
                        val cookies = store.getString("cookies")
                        
                        Log.d("StoreFragment", "开始检查门店: $storeName, 平台: $platform")
                        
                        try {
                            var isValid = false
                            if (platform.contains("美团")) {
                                val meiTuanApi = MeiTuanApi(cookies)
                                val responseStr = meiTuanApi.postData("向日葵")
                                val jsonObject = JSONObject(responseStr)
                                isValid = jsonObject.getInt("code") == 0
                                Log.d("StoreFragment", "美团门店 $storeName 检查结果: $responseStr, isValid: $isValid")
                            } else if (platform.contains("饿了么")) {
                                val elemeApi = ElemeApi(cookies, storeName)
                                val responseStr = elemeApi.pageQuery("向日葵")
                                val jsonObject = JSONObject(responseStr)
                                isValid = jsonObject.getString("data").length > 0
                                Log.d("StoreFragment", "饿了么门店 $storeName 检查结果: $responseStr, isValid: $isValid")
                            }
                            
                            // 更新本地存储
                            store.put("isValid", isValid)
                            withContext(Dispatchers.Main) {
                                sharedPreferences.edit().apply {
                                    putString("storeCookiesList", jsonArray.toString())
                                    apply()
                                }
                                
                                // 更新当前列表中对应店铺的状态
                                val index = currentStoreList.indexOfFirst { it.storeName == storeName && it.platform == platform }
                                if (index != -1) {
                                    Log.d("StoreFragment", "更新UI - 门店: $storeName, 新状态: $isValid")
                                    currentStoreList[index] = StoreValidCheck(platform, storeName, isValid)
                                    // 立即更新 UI
                                    updateStoreList(currentStoreList.toList())
                                } else {
                                    Log.e("StoreFragment", "未找到要更新的门店: $storeName")
                                }
                            }
                        } catch (e: Exception) {
                            Log.e("StoreFragment", "检查cookies失败 - 门店: $storeName, 错误: ${e.message}")
                            withContext(Dispatchers.Main) {
                                val index = currentStoreList.indexOfFirst { it.storeName == storeName && it.platform == platform }
                                if (index != -1) {
                                    currentStoreList[index] = StoreValidCheck(platform, storeName)
                                    updateStoreList(currentStoreList.toList())
                                }
                            }
                        }
                    }
                }
                
                jobs.joinAll()
                Log.d("StoreFragment", "所有门店检查完成")
                
                // 确保在主线程中停止动画
                withContext(Dispatchers.Main) {
                    stopRefreshAnimation()
                }
                
            } catch (e: Exception) {
                Log.e("StoreFragment", "检查过程发生错误: ${e.message}")
                withContext(Dispatchers.Main) {
                    Toast.makeText(requireContext(), "检查过程发生错误: ${e.message}", Toast.LENGTH_SHORT).show()
                    stopRefreshAnimation()
                }
            } finally {
                // 在finally中确保重新启用按钮
                withContext(Dispatchers.Main) {
                    refreshFab.isEnabled = true
                }
            }
        }
    }


    private fun getStoreCookiesFromLocal() {
        val sharedPreferences = requireContext().getSharedPreferences("StoreCookies", Context.MODE_PRIVATE)
        val existingData = sharedPreferences.getString("storeCookiesList", "[]")
        val jsonArray = JSONArray(existingData)


        storevalidcheckList_r = mutableListOf()
        for (i in 0 until jsonArray.length()) {
            val store = jsonArray.getJSONObject(i)
            val storeName = store.getString("storeName")
//            val isValid = store.getBoolean("isValid")
            val platform = store.getString("platform")
            storevalidcheckList_r.add(StoreValidCheck(platform, storeName))
        }
        
        // 更新 RecyclerView
        updateStoreList(storevalidcheckList_r)
    }

    fun updateStoreList(newList: List<StoreValidCheck>) {
        lifecycleScope.launch(Dispatchers.Main) {
            Log.d("StoreFragment", "更新UI列表 - 数量: ${newList.size}")
            newList.forEach { store ->
                Log.d("StoreFragment", "列表项 - 门店: ${store.storeName}, 状态: ${store.isValid}")
            }
            storevalidcheckList.clear()
            storevalidcheckList.addAll(newList)
            storeAdapter.notifyDataSetChanged()
        }
    }

    private fun startRefreshAnimation() {
        stopRefreshAnimation()
        rotateAnimator = ObjectAnimator.ofFloat(refreshFab, View.ROTATION, 0f, 360f).apply {
            duration = 1000
            repeatCount = ObjectAnimator.INFINITE
            interpolator = LinearInterpolator()
            start()
        }
    }

    private fun stopRefreshAnimation() {
        rotateAnimator?.let { animator ->
            animator.cancel()
            refreshFab.rotation = 0f
            rotateAnimator = null
        }
    }

    private fun getCurrentUserName(): String? {
        // 获取当前用户ID的逻辑
        // 这里需要根据您的用户管理方式来实现
        //获取缓存的用户名称
        val sharedPreferences = requireContext().getSharedPreferences("UserPrefs", AppCompatActivity.MODE_PRIVATE)
        val username = sharedPreferences.getString("username", "") ?: ""

        return username
    }

    private fun checkStoreLimit(callback: (Boolean) -> Unit) {
        // 从 SharedPreferences 获取 sessionToken 和用户信息
        val sharedPreferences = requireContext().getSharedPreferences("UserPrefs", AppCompatActivity.MODE_PRIVATE)
        val sessionToken = sharedPreferences.getString("sessionToken", "") ?: ""
        
        if (sessionToken.isEmpty()) {
            Toast.makeText(requireContext(), "请先登录", Toast.LENGTH_SHORT).show()
            callback(false)
            return
        }

        // 获取用户信息以获取最大门店数量限制
        ApiClient.api.getUserInfo(sessionToken).enqueue(object : Callback<UserInfoResponse> {
            override fun onResponse(call: Call<UserInfoResponse>, response: Response<UserInfoResponse>) {
                if (response.isSuccessful) {
                    val maxStoreNumber = response.body()?.maxStoreNumber ?: 0
                    
                    // 从本地获取当前门店数量
                    val storeCookiesPrefs = requireContext().getSharedPreferences("StoreCookies", Context.MODE_PRIVATE)
                    val existingData = storeCookiesPrefs.getString("storeCookiesList", "[]")
                    val jsonArray = JSONArray(existingData)
                    val currentCount = jsonArray.length()

                    lifecycleScope.launch(Dispatchers.Main) {
                        callback(currentCount < maxStoreNumber)
                        if (currentCount < maxStoreNumber) {
                            Log.d("StoreFragment", "可以添加新门店: 当前 $currentCount, 最大 $maxStoreNumber")
                        } else {
                            Log.d("StoreFragment", "已达到门店数量限制: 当前 $currentCount, 最大 $maxStoreNumber")
                            Toast.makeText(requireContext(), 
                                "已达到最大门店数量限制($maxStoreNumber)", 
                                Toast.LENGTH_SHORT).show()
                        }
                    }
                } else {
                    lifecycleScope.launch(Dispatchers.Main) {
                        callback(false)
                        Toast.makeText(requireContext(), "获取用户信息失败: ${response.code()}", Toast.LENGTH_SHORT).show()
                    }
                }
            }

            override fun onFailure(call: Call<UserInfoResponse>, t: Throwable) {
                lifecycleScope.launch(Dispatchers.Main) {
                    callback(false)
                    Toast.makeText(requireContext(), "网络错误: ${t.message}", Toast.LENGTH_SHORT).show()
                }
            }
        })
    }

    // 实现单个门店刷新接口方法
    override fun onRefreshStore(store: StoreValidCheck, position: Int) {
        lifecycleScope.launch {
            try {
                // 获取门店信息
                val cookies = getCookiesByStoreName(store.storeName, store.platform)
                
                if (cookies.isEmpty()) {
                    Toast.makeText(requireContext(), "${store.storeName}门店Cookie不存在，请重新绑定", Toast.LENGTH_SHORT).show()
                    return@launch
                }
                
                // 显示加载动画
                val rotateAnimation = ObjectAnimator.ofFloat(
                    recyclerView.findViewHolderForAdapterPosition(position)?.itemView?.findViewById(R.id.iv_refresh), 
                    View.ROTATION, 
                    0f, 
                    360f
                ).apply {
                    duration = 1000
                    repeatCount = ObjectAnimator.INFINITE
                    interpolator = LinearInterpolator()
                    start()
                }
                
                // 禁用刷新按钮
                recyclerView.findViewHolderForAdapterPosition(position)?.itemView?.findViewById<View>(R.id.iv_refresh)?.isEnabled = false
                
                // 获取SharedPreferences保存更新后的状态
                val sharedPreferences = requireContext().getSharedPreferences("StoreCookies", Context.MODE_PRIVATE)
                val existingData = sharedPreferences.getString("storeCookiesList", "[]")
                val jsonArray = JSONArray(existingData)
                
                var storeIndex = -1
                for (i in 0 until jsonArray.length()) {
                    val storeObj = jsonArray.getJSONObject(i)
                    if (storeObj.getString("storeName") == store.storeName && 
                        storeObj.getString("platform") == store.platform) {
                        storeIndex = i
                        break
                    }
                }
                
                if (storeIndex == -1) {
                    Toast.makeText(requireContext(), "找不到门店信息", Toast.LENGTH_SHORT).show()
                    rotateAnimation.cancel()
                    recyclerView.findViewHolderForAdapterPosition(position)?.itemView?.findViewById<View>(R.id.iv_refresh)?.isEnabled = true
                    return@launch
                }
                
                withContext(Dispatchers.IO) {
                    try {
                        var isValid = false
                        val storeJson = jsonArray.getJSONObject(storeIndex)
                        
                        if (store.platform.contains("美团")) {
                            val meiTuanApi = MeiTuanApi(cookies)
                            val responseStr = meiTuanApi.postData("向日葵")
                            val jsonObject = JSONObject(responseStr)
                            isValid = jsonObject.getInt("code") == 0
                            Log.d("StoreFragment", "刷新单个美团门店 ${store.storeName} 结果: $isValid")
                        } else if (store.platform.contains("饿了么")) {
                            val elemeApi = ElemeApi(cookies, store.storeName)
                            val responseStr = elemeApi.pageQuery("向日葵")
                            val jsonObject = JSONObject(responseStr)
                            isValid = jsonObject.getString("data").length > 0
                            Log.d("StoreFragment", "刷新单个饿了么门店 ${store.storeName} 结果: $isValid")
                        }
                        
                        // 更新本地存储
                        storeJson.put("isValid", isValid)
                        
                        withContext(Dispatchers.Main) {
                            // 保存更改到SharedPreferences
                            sharedPreferences.edit().apply {
                                putString("storeCookiesList", jsonArray.toString())
                                apply()
                            }
                            
                            // 更新列表中的门店状态
                            storevalidcheckList[position] = StoreValidCheck(store.platform, store.storeName, isValid)
                            storeAdapter.notifyItemChanged(position)
                            
                            // 停止动画并重新启用按钮
                            rotateAnimation.cancel()
                            recyclerView.findViewHolderForAdapterPosition(position)?.itemView?.findViewById<View>(R.id.iv_refresh)?.rotation = 0f
                            recyclerView.findViewHolderForAdapterPosition(position)?.itemView?.findViewById<View>(R.id.iv_refresh)?.isEnabled = true
                            
                            Toast.makeText(requireContext(), "${store.storeName} 刷新完成", Toast.LENGTH_SHORT).show()
                        }
                    } catch (e: Exception) {
                        withContext(Dispatchers.Main) {
                            Log.e("StoreFragment", "刷新单个门店出错: ${e.message}")
                            Toast.makeText(requireContext(), "刷新失败: ${e.message}", Toast.LENGTH_SHORT).show()
                            
                            // 停止动画并重新启用按钮
                            rotateAnimation.cancel()
                            recyclerView.findViewHolderForAdapterPosition(position)?.itemView?.findViewById<View>(R.id.iv_refresh)?.rotation = 0f
                            recyclerView.findViewHolderForAdapterPosition(position)?.itemView?.findViewById<View>(R.id.iv_refresh)?.isEnabled = true
                        }
                    }
                }
            } catch (e: Exception) {
                withContext(Dispatchers.Main) {
                    Log.e("StoreFragment", "刷新单个门店过程发生错误: ${e.message}")
                    Toast.makeText(requireContext(), "刷新出错: ${e.message}", Toast.LENGTH_SHORT).show()
                }
            }
        }
    }

    // 实现更新Cookies接口方法
    override fun onUpdateCookies(store: StoreValidCheck, position: Int) {
        // 打开对应平台的登录界面来更新Cookies
        val intent = Intent(requireContext(), WebViewActivity::class.java)
        intent.putExtra("platform", store.platform)
        intent.putExtra("storeName", store.storeName)
        intent.putExtra("isUpdate", true) // 标记这是更新操作
        startActivity(intent)
    }
}